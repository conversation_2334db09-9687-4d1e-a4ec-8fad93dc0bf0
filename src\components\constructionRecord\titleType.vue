<!-- 考勤记录头部标签tab -->
<template>
  <el-row class="title-type">
    <el-col>
      <ul :key="keyTimer">
        <li
          v-for="item in list"
          :key="item.value"
          :class="item.isChecked ? 'actived' : ''"
          @click="itemClick(item)"
        >
          <!-- <div>{{item.num || 0}}</div> -->
          <div class="name">{{ item.name }}</div>
        </li>
      </ul>
    </el-col>
  </el-row>
</template>

<script>
import { setStore } from "@/util/store";
export default {
  components: {},
  name: "titleType",
  props: {
    defaultChecked: {
      type: String,
      default: "laborNum"
    },
    disabled: Array
  },
  data() {
    return {
      list: [
        // { name: "分公司", id: 0, value: "totalCardNum", isChecked: false },
        { name: "项目简介", id: 1, value: "laborNum", isChecked: true },
        { name: "视频", id: 2, value: "3", isChecked: false }
      ],
      listNum: {},
      keyTimer: null
    };
  },
  watch: {
    "$i18n.locale"(val) {
      if (val) {
        this.languageChange();
      }
    },
  },
  created() {
    // this.getPersonalNum()
    this.list.forEach(ele => {
      ele.isChecked = ele.value === this.defaultChecked;
      if (this.disabled && this.disabled.length > 0) {
        this.disabled.forEach(v => {
          v == ele.value && ((ele.isChecked = false), (ele.disabled = true));
        });
      }
    });
  },
  mounted() {
    this.languageChange();
  },
  methods: {
    languageChange() {
      this.list[0].name = this.$t("ProjectIntroduction");
      this.list[1].name = this.$t("Video");
      // this.itemClick(this.list[0])
    },
    itemClick(val) {
      if (val.disabled) return false;
      this.list.forEach(ele => {
        if (ele.id == val.id) {
          ele.isChecked = true;
          setStore({ name: "labourType", content: ele.value, type: "session" });
        } else {
          ele.isChecked = false;
        }
      });
      this.$emit("titleClick", val);
    }
  }
};
</script>

<style lang="scss" scoped>
.title-type {
  ul {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    li {
      height: 43px;
      width: 142px;
      font-size: 24px;
      padding-top: 10px;
      margin-right: 100px;
      text-align: center;
      color: #fff;
      background: url(./../../assets/btn_back.png) no-repeat center;
      background-size: 100% 100%;
      cursor: pointer;
      .name {
        font-size: 16px;
      }
    }
    .actived {
      background: url(./../../assets/btn_active.png) no-repeat center;
    }
  }
}
</style>
