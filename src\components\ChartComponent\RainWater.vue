<!--
 * @Description: 水利专版-水雨情监测
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">
      <span>{{ moduleName }}</span>

      <div class="barStyle">
        <el-select
          v-model="deviceValue"
          placeholder="请选择"
          @change="getBarData"
          id="select"
        >
          <el-option
            v-for="item in deviceOptions"
            :key="item.id"
            :label="item.deviceName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="areaContent">
      <div
        class="box"
        ref="rainwaterRef"
      >
        <div
          id="rainwaterChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawBarLineTotalDoubleUnit } from "@/components/constructionRecord/Echarts/echartsOne.js";
import { deviceList, rainwaterAnalysis } from "@/api/echrtsApi";
export default {
  components: {},
  name: "VehicleManage",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      userId: "",
      barWidth: null,
      barHeight: null,
      barParams: {
        dom: "rainwaterChart",
        xAxisData: [],
        seriesData: [],
        isMoreLine: true,
        legendData: [
          {
            name: '降雨量',
            icon: 'circle',
          },
          {
            name: '水位',
            icon: 'rect',
          }
        ],
        legendCenter: "left",
        axisPointerType: "shadow",
        yminInterval: 1,
        boundaryGap: true,
      },
      arr: [],
      deviceOptions: [],
      deviceValue: '',
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.userId = getStore({
      name: "userId",
    });
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
    this.$nextTick(() => {
      this.getDevice();
    })
  },
  methods: {
    getDevice() {
      deviceList().then((res) => {
        this.deviceOptions = res.data.data;
        if (this.deviceOptions.length) {
          this.deviceValue = this.deviceOptions[0].id;
          this.getBarData();
        }
      });
    },
    setEchartsWidth() {
      this.barWidth = this.$refs.rainwaterRef.offsetWidth;
      this.barHeight = this.$refs.rainwaterRef.offsetHeight;
    },
    getBarData() {
      let params = {
        "id": this.deviceValue,
        "type": 0,//0天 1月
      }
      rainwaterAnalysis(params)
        .then((res) => {
          let result = res.data;
          const { data, statusCode } = result;
          if (statusCode == 200) {
            var xInfor = data.rainfallNames;
            var yInfor = data.rainfallValues;
            var yInfor2 = data.waterValues;
            let seriesData = [
              {
                name: '降雨量',
                type: "bar",
                data: yInfor,
                itemStyle: {
                  color: "#0099FF",
                },
              },
              {
                name: '水位',
                type: "line",
                data: yInfor2,
                interval: 0,

                yAxisIndex: 1,
                itemStyle: {
                  color: "#95F204",
                },
              },
            ];
            let current = new Date().getHours();
            this.barParams.xAxisData = xInfor.slice(0, current);
            this.barParams.seriesData = seriesData;
            drawBarLineTotalDoubleUnit(this.barParams);
          }
        })
        .catch(() => { });
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
//改变el-select的宽高
.barStyle {
  right: 28px !important;
  top: 6px !important;
}
.barStyle /deep/ {
  #select {
    max-width: 180px;
    min-width: 160px;
    height: 18px;
    position: relative;
    top: -5px;
  }
  .el-input--suffix {
    height: 24px;
    border: 0;
  }
  .el-input__icon {
    line-height: 24px;
    position: relative;
    top: -5px;
  }
  .el-select .el-input__inner {
    background: url("~@/assets/rectangle_select.png") no-repeat !important;
    background-size: 100% 100% !important;
  }
}
</style>
