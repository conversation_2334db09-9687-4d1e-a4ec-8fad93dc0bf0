<!--复查状态：检查结论为符合或不符合无需整改时为空，检查结论为不符合需整改时，复查人未操作为未复查，已操作为复查人选择的复查结论，多次复查取最后一次
点击查看可查看单条检查项的详情-->
<template>
  <el-row class="content contentBg">
    <!-- <el-dialog
      class="dialog-component"
      :visible.sync="visibleDialog"
      :append-to-body="true"
      width="90%"
      @close="visibleDialog = false">
      <el-row> -->
        <el-col>
          <el-form ref="form" :model="trainForm" label-width="120px">
            <el-col :span="6" >
              <el-form-item label="检查名称:">
                <el-input v-model="trainForm.disclosurePersonal" :disabled="disabled"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计划检查时间:">
                <el-date-picker
                style="width:99%;"
                v-model="trainForm.cultivateDisclosureDDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="发布人:">
                <el-input v-model="trainForm.disclosureType" :disabled="disabled"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="检查人:">
                <el-input v-model="trainForm.disclosureType" :disabled="disabled"></el-input>
              </el-form-item>
            </el-col>
              <el-col>
                <list-table
                  :border="true"
                  :show-overflow-tooltip="true"
                  :columns="columns"
                  :dataSource="listData"
                  :pagination="pagination"
                  v-on:currentChange="handleCurrentChange"
                  v-on:sizeChange="handleSizeChange">
                </list-table>
              </el-col>
          </el-form>
        </el-col>
      <!-- </el-row>
    </el-dialog> -->
  <CheckInfo ref="checkInfoShow">查看检查信息界面</CheckInfo>
  </el-row>
</template>
<script>
import {addData,getList,delData,toDealData,temLibData,detailData} from '@/api/specialCheck'
// import CheckInfo from "@/component/specialCheck/checkInfo.vue";
export default {
  name: "demo",
  components: {},
  props: {
    //是否可编辑
    isAdd: {
      type: Boolean,
      default: true,
    },
    //是否回显数据
    model: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      visibleDialog: false,
      disabled: false,
      listName: "",
      id:'',
      manageList:[],
      labourList:[],
      trainForm: {
        disclosurePersonal: "",
        cultivateDisclosureDDate:'',
        cultivateDisclosureDTimeH: "",
        disclosureType:'',
        courseDisclosureTitle:'',
        cultivateDisFilePath:''
      },
      columns: [
        { code: "index", label: "序号", width: 55,type: "num",},
        { code: "personalName", label: "检查项",align: "center" },
        { code: "sex", label: "评价标准",align: "center" },
        { code: "paperType", label: "隐患等级", align: "center" },
        { code: "paperType", label: "检查描述", align: "center" },
        { code: "paperType", label: "检查时间", align: "center" },
        { code: "paperType", label: "检查部位", align: "center" },
        { code: "paperType", label: "责任人", align: "center" },
        { code: "paperType", label: "检查结论", align: "center" },
        { code: "paperType", label: "复查状态", align: "center" },
        {code: 'opts', type:"button",label: '操作',align:'center',opts:[
            {
              type:'text',
              size:'small',
              name:'查看',
              value:'detail',
              clickEvent:this.detailClick
            },
            {
              type:'text',
              size:'small',
              name:'删除',
              value:'del',
              clickEvent:this.delClick
            }
          ]},
      ],
      listData:[{},{}],
      pagination:{//分页默认值
        totalSize:0,
        currentPage:1,
        pageSize:10,
        position:'center',
        layout:'total,pager,sizes,prev,next,jumper'
      },
    };
  },
  watch: {
  },
  created() {
     this.id = this.$route.query.id,  //接受参数关键代码
     console.log(this.id)
    if (!this.isAdd) {
      this.disabled = this.form.disabled;
      this.getDetailData(this.form.safID);
    }
  },
  methods: {
    //详情
    getDetailData(val) {
      let params = {
        safetyId: val.safID,
      };
      detailData(params).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          this.trainForm = result.data;
        } else {
          this.$showMessage();
        }
      });
    },
    //获取人员数据
    getPersonList(val) {
      let params = {};
      personList(params).then((res) => {});
    },
    detailClick(val){
      // val.disabled = true
      // this.editRow = val
     this.$refs.checkInfoShow.checkInfoDialog = true;
    },
    //新增人员
    sureClick(val) {
      this[this.listName] = val;
    },
    // 页码
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.searchClick()
    },
    // 页数
    handleSizeChange(val) {
      this.pagination.currentPage = 1
      this.pagination.pageSize = val
      this.searchClick()
    },
    // 关闭弹框
    closeDialog() {
      this.showDialog = false;
      this.$emit("update:visible", value);
    },
    //確定
    itemClick() {
      this.showDialog = false;
      let data = this.trainForm;
      data.labourList = this.labourList;
      data.manageList = this.manageList;
      this.$emit("sureClick", data);
    },
    //刪除
    delClick(val, list) {
      this[list].forEach((ele, i) => {
        if (ele.id == val.id) {
          this[list].splice(i, 1);
        }
      });
    },
    uploadFile() {},
    //上传文件
    importClick() {
      this.$refs.apload.dialogVisible = true;
    },
    //继续添加按钮，跳转到index界面的新建按钮功能
    addRow() {
      this.showDialog = false;
      this.$emit("add", this.form);
    },
  },
};
</script>

<style lang="scss" scoped>
.name-box {
  width: 50%;
  border: 1px solid #fff;
  .name-list {
    max-height: 200px;
    overflow-y: auto;
  }
}
</style>
