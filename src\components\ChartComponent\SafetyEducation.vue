<!--
 * @Description: 安全教育
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-22 10:24:02
 * @LastEditors: dong<PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="SafetyEducationRef"
      >
        <div class="customCard">
          <p><span>{{qualifiedRate}}</span> %</p>
          <p>教育合格率</p>
          <span class="tag">{{ evaluation }}</span>
        </div>
        <div
          id="SafetyEducationChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawRadiusPie } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getSafetyEducation } from "@/api/echrtsApi";
export default {
  components: {},
  name: "SafetyEducation",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "SafetyEducationChart",
        img: 'data:image/png;base64,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',
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: "总预警数",
        seriesCenter: ["25%", "50%"],
        richNameWidth: 70,
        tooltipFormatter: function (infor) {
          let msg = `${infor.marker}${infor.data.name}
              <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
              <b>${infor.data.value}</b>人`;
          return msg;
        },
        costomLegendFormatter: function (name) {
          return name;
        },
      },
      arr: [],
      qualifiedRate: 0
    };
  },
  computed: {
    evaluation() {
      if (this.qualifiedRate > 90) {
        return '优';
      } else if (this.qualifiedRate > 60) {
        return '良';
      } else {
        return '差';
      }
    }
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.SafetyEducationRef.offsetWidth - 40;
      this.barHeight = this.$refs.SafetyEducationRef.offsetHeight;
    },
    getBarData() {
      getSafetyEducation()
        .then((res) => {
          const { statusCode, data } = res.data;
          if (statusCode == 200) {
            // 计算总人数
            const totalPeople = data.reduce((sum, item) => sum + item.value, 0);
            let qualifiedPeople = 0;
            let legendFormatter = (name) => {
              const item = data.find((i) => {
                return i.name === name;
              });
              const p = item.value;
              let clientWidth = document.documentElement.clientWidth;
              let newName = name.length > 7 ? name.slice(0, 7) + "..." : name;
              if (clientWidth < 1900) {
                newName = name.slice(0, 5) + "...";
                this.pieParams.richNameWidth = 60;
              }
              return "{name|" + newName + "}" + "{percent|" + p + "}";
            };
            this.pieParams.legendFormatter = legendFormatter;
            this.pieParams.data = data.map((im) => {
              if (im.name == '培训合格人次') {
                qualifiedPeople = im.value;
                this.qualifiedRate = totalPeople > 0 ? ((qualifiedPeople / totalPeople) * 100).toFixed(0) : 0;
              }
              switch (im.name) {
                case "培训合格人次":
                  im.name = this.$t("qualifiedtrainees");
                  break;
                case "培训中人次":
                  im.name = this.$t("trainingpeople");
                  break;
                case "不合格人次":
                  im.name = this.$t("unqualifiedpersonnel");
                  break;
                case "未培训人次":
                  im.name = this.$t("Untrainedpersonnel");
                  break;
              }
              return im;
            });
            console.log("data 666", data);
            drawRadiusPie(this.pieParams);
          }
        })
        .catch(() => { });
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
  .customCard {
    // width: 140px;
    padding: 0 20px;
    height: 66px;
    text-align: center;
    border-radius: 0 66px 66px 0;
    border: 2px solid #fff;
    border-left: transparent;
    border-top: transparent;
    border-bottom: transparent;
    background-image: linear-gradient(to right, #36416d, #015ac0);
    position: relative;
    p {
      white-space: nowrap;
      &:nth-child(1) {
        padding: 8px 0 0;
        span {
          font-size: 28px;
          background: linear-gradient(to bottom, #aadaff, #ffffff);
          background-clip: text;
          -webkit-background-clip: text;
          text-fill-color: transparent;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .tag {
      position: absolute;
      right: -6px;
      top: -10px;
      width: 30px;
      height: 30px;
      background: #26a2cf;
      border: 2px solid #fff;
      border-radius: 50%;
      text-align: center;
      line-height: 26px;
      box-sizing: border-box;
    }
  }
}
</style>
