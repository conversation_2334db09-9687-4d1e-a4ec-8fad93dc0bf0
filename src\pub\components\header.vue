<!--
 * @Description:
 * @Author:
 * @Date: 2021-11-26 11:41:50
 * @LastEditTime: 2025-07-30 11:02:31
 * @LastEditors: dongqi<PERSON>qian
 * @Usage:
-->
<template>
  <div>
    <JkyHeaderTop
      :userInfo="userInfo"
      :serverInfo="serverInfo"
      :isShowPageBtn="isShowPageBtn"
      @handleLanguage="handleLanguage"
      @btn-click="handleBtnClick"
      @tab-change="handleTabChange"
    > </JkyHeaderTop>

    <div v-if="isVisible">
      <div
        class="topPro"
        @click="arrowClick()"
      >
        <div
          :title="companyName"
          style="display: flex; justify-content: space-around"
        >
          <img
            src="@/assets/expand.png"
            alt=""
          />
          <div class="topProName">{{ companyName }}</div>
        </div>
      </div>
      <div>
        <el-dialog
          :visible.sync="dialogTableVisible"
          class="excelList"
          width="50%"
          append-to-body
        >
          <div class="titleWrap">
            <div
              style="display: inline-block"
              v-for="item in titleList"
              :key="item"
              @click="companyClick(item)"
            >
              <div
                class="titleItem"
                :class="item.active ? 'active' : ''"
              >
                {{ item.label }}
              </div>
              <span v-if="item.id === 1">|</span>
            </div>
          </div>
          <el-input
            placeholder="查找项目"
            prefix-icon="el-icon-search"
            clearable
            v-model="treeValue"
            @key.enter="searClick()"
            @change="searClick()"
            style="margin-top: 20px"
          >
          </el-input>
          <el-tree
            ref="tree"
            :data="treeData"
            node-key="directoryId"
            :props="props"
            default-expand-all
          >
            <span
              slot-scope="{ node, data }"
              class="custom-tree-node"
            >
              <template>
                <div
                  v-if="data.dataType === 1"
                  class="node_div"
                >
                  <span
                    :title="data.name"
                    @click.stop="goComClick(node)"
                  >
                    {{ data.name }}
                  </span>
                </div>
                <div
                  v-if="data.dataType === 2"
                  class="node_div"
                >
                  <span
                    :title="data.name"
                    class="name-box"
                    @click.stop="goClick(data)"
                  >
                    <img
                      src="@/assets/build.png"
                      alt=""
                    />
                    {{ data.name }}
                  </span>
                  <span class="size-box">
                    <img
                      src="@/assets/project.png"
                      alt=""
                    />
                    {{ data.projectTypeName || data.authorityProjectTypeName }}
                  </span>
                  <span class="time-box">
                    <img
                      src="@/assets/area.png"
                      alt=""
                    />
                    {{ data.projectArea || 0 }} {{ data.projectUnit }}
                  </span>
                  <span
                    class="upload-box"
                    v-if="data.provinceName"
                  >
                    <img
                      src="@/assets/site.png"
                      alt=""
                    />
                    {{ data.provinceName }} {{ data.cityName }}
                  </span>
                  <span
                    class="upload-box"
                    v-if="data.companyName"
                  >
                    <img
                      src="@/assets/site.png"
                      alt=""
                    />
                    {{ data.companyName }}
                  </span>
                </div>
              </template>
            </span>
          </el-tree>
        </el-dialog>
      </div>
    </div>
  </div>

</template>

<script>
import { updateQueryParam, getUrlParams } from "@/util/util.js";
import {
  getUserInfo,
  getProList,
  getAreaList,
  getUserCookie,
} from "@/api/common";
// import platFormLogo from "@/assets/logo.png";
import {
  BASE_URL,
  BASE_URL_LOGIN,
  BASE_PLATFORMLOGO_URL,
  PROJECT_URL,
  COMPANY_URL,
} from "@/util/constant";
export default {
  name: "HeadTop",
  props: {
    isShowPageBtn: {
      // 首页特有按钮，普通页面不显示
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      userInfo: {},
      serverInfo: {
        ip: BASE_URL_LOGIN,
      },
      dialogTableVisible: false,
      props: {
        // 配置选项
        children: "children",
        label: "name",
        isLeaf: "leaf",
      },
      areaData: [],
      companyData: [],
      treeData: [],
      titleList: [
        { id: 1, label: "公司", active: true },
        { id: 2, label: "地区", active: false },
      ],
      treeValue: "",
      companyName: "",
      isVisible: false,
      companyId: 0,
      rootCompanyId: null,
      projectId: 0,
      current: null,
      language: "zh",
      headerTab: 'project', // 存储头部tab数据
      headerBtn: '', // 存储头部按钮数据
    };
  },
  mounted() {
    this.language = getStore({ name: "language" });
    // console.log(" header mounted", this.language)
    this.$i18n.locale = this.language;
    // console.log("header $i18n.locale mounted", this.$i18n.locale)
    this.companyId = getUrlParams().companyId || getStore({ name: "companyId" });
    this.projectId = getUrlParams().projectId || getStore({ name: "projectId" });
    this.getCookie();
    this.searchTree();
  },
  methods: {
    handleBtnClick(val) {
      this.headerBtn = val
      this.$emit('btn-click', val)
    },
    handleTabChange(val) {
      this.headerTab = val
      this.$emit('tab-change', val)
    },
    handleLoginOut() {
      clearStore({ type: 'session' });
      clearStore({});
    },
    handleLanguage(value) {
      localStorage.setItem("locale", value);
      this.userInfo.language = value;
      setStore({ name: 'language', content: value });
      setTimeout(() => {
        const updatedUrl = updateQueryParam('language', value);
        const currentUrl = window.location.href;
        var index = currentUrl.indexOf("?"); //获取其地址
        if (index != -1) {
          let url = currentUrl.substring(0, index);
          window.location.href = url + updatedUrl;
          console.log(translate.language.getLocal(), value)
        }
      }, 200)
      // this.$i18n.locale = value;
      // console.log("header handleLanguage",this.$i18n.locale, this.userInfo.language)
    },
    async getUserInfoFun() {
      let { data } = await getUserInfo({
        userId: getStore({ name: "userId" }) || -1,
      });
      setStore({ name: "companyName", content: data.data.companyName });
      if (data.data.userType === 7) {
        this.isVisible = true;
      } else {
        this.isVisible = false;
      }
      if (data.statusCode === 200) {
        this.userInfo = { ...data.data };

        if (this.userInfo && this.userInfo.platFormLogo) {
          this.userInfo.platFormLogo = BASE_PLATFORMLOGO_URL + data.data.platFormLogo;
        } else {
          this.userInfo.platFormLogo = null;
        }

        if (this.userInfo && !this.userInfo.platFormName) {
          this.userInfo.platFormName = "建科研智慧建造云平台";
        }

        setStore({ name: "userId", content: this.userInfo.id });
        setStore({ name: "projectId", content: this.userInfo.projectId });
        setStore({ name: "companyId", content: this.userInfo.companyId });
        setStore({ name: "projectName", content: this.userInfo.projectName || "" });
        setStore({ name: "userType", content: this.userInfo.userType });
        setStore({ name: "account", content: this.userInfo.account });
        this.$bus.$emit('getUserInfoSuccess');
        this.userInfo.language = this.language;
        // console.log("header this.userInfo.language", this.userInfo.language)
      }
    },
    getCookie() {
      let params = {
        companyid: this.companyId,
        projectid: this.projectId,
        pk: "pk",
      };
      getUserCookie(params).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          if (result.data.projectName) {
            this.companyName = result.data.projectName;
          } else {
            this.companyName = result.data.companyName;
          }
          this.rootCompanyId = result.data.rootCompanyId;
          //setStore({ name: "userId", content: result.data.userId });
          //setStore({ name: "projectId", content: result.data.projectId });
          //setStore({ name: "companyId", content: result.data.companyId });
          setStore({ name: "projectName", content: result.data.projectName });
          this.getUserInfoFun();
        }
      });
    },
    getValueByUrlParams(paras) {
      let url = window.location.href;

      var paraString = url
        .substring(url.indexOf("?") + 1, url.length)
        .split("&");

      var paraObj = {};
      var i, j;
      for (i = 0; (j = paraString[i]); i++) {
        paraObj[j.substring(0, j.indexOf("=")).toLowerCase()] = j.substring(
          j.indexOf("=") + 1,
          j.length
        );
      }

      var returnValue = paraObj[paras.toLowerCase()];

      if (typeof returnValue == "undefined") {
        return "";
      } else {
        return returnValue;
      }
    },
    searchTree() {
      let params = {
        projectName: this.treeValue,
        companyId: this.rootCompanyId || 0,
      };
      getProList(params)
        .then((res) => {
          let result = res.data;
          if (result.statusCode == 200) {
            if (result.data) {
              this.treeData = [result.data];
            } else {
              this.treeData = [];
            }
          }
        })
        .catch(() => { });
    },
    searchArea() {
      let params = {
        projectName: this.treeValue,
        companyId: this.rootCompanyId,
      };
      getAreaList(params).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          if (result.data) {
            this.treeData = result.data;
          } else {
            this.treeData = [];
          }
        }
      });
    },
    arrowClick() {
      this.dialogTableVisible = true;
      this.treeValue = "";
      this.companyClick(this.titleList[0]);
    },
    searClick() {
      if (this.current === 1) {
        this.searchTree();
      }
      if (this.current === 2) {
        this.searchArea();
      }
    },
    companyClick(val) {
      this.treeValue = "";
      this.current = val.id;
      if (val.id === 1) {
        this.searchTree();
      }
      if (val.id === 2) {
        this.searchArea();
      }
      this.titleList.forEach((ele) => {
        if (ele.id === val.id) {
          ele.active = true;
        } else {
          ele.active = false;
        }
      });
    },
    goClick(val) {
      const userId = getStore({ name: "userId" });
      const projectName = getStore({ name: "projectName" }).replace(/#/g, '');
      const userType = getStore({ name: "userType" });
      const formCompany = true;
      if (val.rootCompanyId && val.companyId && val.id) {
        location.href = `${PROJECT_URL}/?userId=${userId}&companyId=${val.companyId}&projectId=${val.id}&projectName=${projectName}&userType=${userType}&rootCompanyId=${val.rootCompanyId}&formCompany=${formCompany}#/constructionRecord`;
      }
    },
    goComClick(val) {
      const userId = getStore({ name: "userId" });
      const projectName = getStore({ name: "projectName" }).replace(/#/g, '');
      const userType = getStore({ name: "userType" });
      if (val.data.rootCompanyId && val.data.companyId) {
        location.href = `${COMPANY_URL}/?userId=${userId}&companyId=${val.data.companyId
          }&projectId=${0}&projectName=${projectName}&rootCompanyId=${val.data.rootCompanyId
          }&userType=${userType}#/constructionRecord`;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
>>> .el-tree {
  margin: 15px 0;
  max-height: 500px;
  overflow: auto;
  max-width: 1000px;
}
>>> .el-tree-node__content {
  padding: 5px;
  font-size: 18px;
}
>>> .el-dialog__header {
  line-height: 0 !important;
  padding: 1px !important;
  background: none;
}
>>> .excelList {
  .el-dialog {
    width: 650px;
    border: 1px solid #00deff;
    background: #0b3472;
  }
  .el-dialog--center .el-dialog__body {
    padding: 25px 90px 30px 40px;
    font-size: 16px;
    background: none;
  }
  .el-dialog__header {
    // border-bottom: 1px solid #00deff;
    font-size: 20px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #00deff;
    line-height: 0 !important;
    padding: 1px !important;
    background: none;
  }

  .el-dialog__headerbtn {
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    // background: rgba(79, 168, 208, 0.2);
  }
  .el-dialog__headerbtn {
    font-size: 40px;
  }
  .el-dialog__body {
    background: #0b3472;
  }
}
.titleWrap {
  text-align: center;
  font-size: 20px;
}
.titleItem {
  display: inline-block;
  margin: 10px;
  cursor: pointer;
}
.name-box {
  width: 150px;
  padding: 0 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: inline-block;
}
.topPro {
  position: absolute;
  max-width: 270px;
  // text-align: center;
  top: 3%;
  z-index: 2009;
  right: 45%;
  cursor: pointer;
  img {
    width: 22px;
    height: 22px;
  }
}
.topProName {
  max-width: 200px;
  // text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: inline-block;
  font-size: 20px;
  height: 24px;
  line-height: 24px;
  font-weight: 600;
  padding: 0 20px;
  border-right: 1px solid #335175;
}
.node_div {
  display: flex;
  span {
    width: 180px;
  }
}
.active {
  color: aqua;
}
.ignore {
  position: relative;
  z-index: 2000;
}
</style>
