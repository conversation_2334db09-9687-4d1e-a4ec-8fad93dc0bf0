import * as echarts from "echarts";
// import { call } from "file-loader";

// 饼图
export const drawPie = params => {
  let dom = params.dom,
    data = params.data,
    nameTitle = params.nameTitle,
    titleInfor = params.titleInfor ? params.titleInfor : {},
    // colorArray = params.colorArray || [],
    seriesLabel = params.seriesLabel,
    seriesRadius = params.seriesRadius || "70%",
    seriesCenter = params.seriesCenter || ["50%", "50%"],
    legendIcon = params.legendIcon || "circle",
    legendFormatter = params.legendFormatter || null,
    richNameWidth = params.richNameWidth || 80,
    itemStyleEmphasis = params.itemStyleEmphasis || {},
    noTooltipShow = !params.noTooltipShow,
    legendTop = params.legendTop || "center",
    callback = params.callback;

  let tooltipFormatter = params.tooltipFormatter || null;

  let legendType = data.length > 0 ? "scroll" : "plain";

  let id = document.getElementById(dom);
  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }
  // let myChart = echarts.init(id);
  let option = {
    title: {
      ...titleInfor
      // text: nameTitle,
      // left: "center",
      // textStyle: {
      //   color: "#fff"
      // }
    },
    tooltip: {
      show: noTooltipShow,
      trigger: "item",
      formatter: tooltipFormatter,
      confine: true
    },
    legend: {
      icon: legendIcon,
      orient: "vertical",
      type: legendType,
      pageIconColor: "#2C86FF", //翻页箭头颜色
      pageIconInactiveColor: "rgba(44,132,251,0.40)", //翻页（即翻页到头时箭头的颜色）
      pageTextStyle: {
        color: "#fff" //翻页数字颜色
      },
      right: "0",
      top: legendTop,
      textStyle: {
        color: "#fff",
        rich: {
          name: {
            verticalAlign: "right",
            align: "left",
            width: richNameWidth,
            fontSize: 10,
            color: "#D8DDE3",
            fontWeight: "bolder"
          },
          percent: {
            padding: [0, 0, 0, 0],
            color: "#D8DDE3"
          }
        }
      },
      formatter: legendFormatter
    },
    series: [
      {
        name: nameTitle,
        type: "pie",
        radius: seriesRadius,
        center: seriesCenter,
        data: data,
        itemStyle: {
          normal: {
            borderWidth: 1, // 每一个类型之间空隙
            borderColor: "#fff"
            // color: function (params) {
            //     const colorList = colorArray
            //     return colorArray.length > 0 ? colorList[params.dataIndex] : '#EA8187'
            // }
          }
        },
        label: {
          show: seriesLabel,
          position: seriesLabel ? 'inside' : "center",
          formatter: '{d}%',
          fontSize: 9,
          color: '#fff',
        },
        emphasis: itemStyleEmphasis
      }
    ]
  };
  myChart.setOption(option);
  if (callback) {
    myChart.on("click", function (params) {
      return callback(params);
    });
  }
};
// 线状图柱状图统一一个
export const drawBarLineTotal = params => {
  let xAxisData = params.xAxisData,
    seriesData = params.seriesData;
  let callback = params.callback;
  let unit = params.unit || "";
  let isMoreLine = params.isMoreLine || false;
  let legendIcon = params.legendIcon || "circle";
  let boundaryGap = params.boundaryGap; //取反值，看是否挨着边
  let legendCenter = params.legendCenter || "center";
  let axisPointerType = params.axisPointerType || "line";
  let xAxisType = params.xAxisType || "category";
  let yAxisType = params.yAxisType || "value";
  let dataZoom = params.dataZoom || null;

  let tooltipFormatter = params.tooltipFormatter || null;

  let yminInterval = params.yminInterval || null;
  let xminInterval = params.xminInterval || null;

  let axisLabelFormatter = params.axisLabelFormatter || null;

  let legendType = seriesData.length > 0 ? "scroll" : "plain";

  let newLegend = (function () {
    let arr = [];
    if (isMoreLine) {
      seriesData.forEach(ele => {
        arr.push(ele.name);
      });
    } else {
      if (seriesData.name) {
        arr.push(seriesData.name);
      }
    }
    return arr;
  })();
  let legendData = params.legendData || newLegend;
  let dom = params.dom;
  let toolboxShow = params.toolboxShow || false;
  let id = document.getElementById(dom);
  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  // let myChart = echarts.init(id);
  let option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: axisPointerType
      },
      formatter: tooltipFormatter,
      confine: true
    },
    legend: {
      pageIconColor: "#2C86FF", //翻页箭头颜色
      pageIconInactiveColor: "rgba(44,132,251,0.40)", //翻页（即翻页到头时箭头的颜色）
      pageTextStyle: {
        color: "#fff" //翻页数字颜色
      },
      type: legendType,
      data: legendData,
      icon: legendIcon,
      // left: 'right',
      orient: "horizontal", // 'vertical'
      x: legendCenter, // 'center' | 'left' | {number},
      textStyle: { color: "#fff" }
    },
    dataZoom: dataZoom,
    grid: {
      left: "1%",
      right: "7%",
      bottom: "0%",
      top: "22%",
      containLabel: true
    },
    toolbox: {
      show: toolboxShow,
      feature: {
        saveAsImage: {}
      }
    },
    xAxis: {
      minInterval: xminInterval,
      // show: xAxisType == "category" ? true : false,
      show: true,
      type: xAxisType,
      boundaryGap: boundaryGap,
      // axisLine: { show: xAxisType == "category" ? true : false },
      axisLine: { show: true },
      data: xAxisData,
      axisLabel: {
        textStyle: {
          color: "#fff"
        },
        formatter: axisLabelFormatter
      },
      splitLine: {
        show: xAxisType == "value" ? true : false,
        lineStyle: {
          // type: "dashed",
          color: "#46556b"
        }
      },
      axisTick: {
        //y轴刻度线
        show: false
      }
    },
    yAxis: {
      type: yAxisType,
      minInterval: yminInterval,
      axisLine: { show: true },
      axisLabel: {
        textStyle: {
          color: "#fff"
        },
        formatter: "{value}" + unit
      },
      splitLine: {
        show: xAxisType == "category" ? true : false,
        lineStyle: {
          // type: "dashed",
          color: "#46556b"
        }
      },
      data: xAxisData
    },
    series: seriesData
  };
  myChart.setOption(option, true);
  if (callback) {
    myChart.on("click", function (params) {
      return callback(params);
    });
  }
  window.onresize = myChart.resize;
};
export const drawBarLineTotalDoubleUnit = params => {
  let xAxisData = params.xAxisData,
    seriesData = params.seriesData;
  let callback = params.callback;
  let unit = params.unit || "";
  let isMoreLine = params.isMoreLine || false;
  let legendIcon = params.legendIcon || "circle";
  let boundaryGap = params.boundaryGap; //取反值，看是否挨着边
  let legendCenter = params.legendCenter || "center";
  let axisPointerType = params.axisPointerType || "line";
  let xAxisType = params.xAxisType || "category";
  let yAxisType = params.yAxisType || "value";
  let dataZoom = params.dataZoom || null;

  let tooltipFormatter = params.tooltipFormatter || null;

  let yminInterval = params.yminInterval || null;
  let xminInterval = params.xminInterval || null;

  let axisLabelFormatter = params.axisLabelFormatter || null;

  let legendType = seriesData.length > 0 ? "scroll" : "plain";

  let newLegend = (function () {
    let arr = [];
    if (isMoreLine) {
      seriesData.forEach(ele => {
        arr.push(ele.name);
      });
    } else {
      if (seriesData.name) {
        arr.push(seriesData.name);
      }
    }
    return arr;
  })();
  let legendData = params.legendData || newLegend;
  let dom = params.dom;
  let toolboxShow = params.toolboxShow || false;
  let id = document.getElementById(dom);
  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  // let myChart = echarts.init(id);
  let option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: axisPointerType
      },
      formatter: tooltipFormatter,
      confine: true
    },
    legend: {
      pageIconColor: "#2C86FF", //翻页箭头颜色
      pageIconInactiveColor: "rgba(44,132,251,0.40)", //翻页（即翻页到头时箭头的颜色）
      pageTextStyle: {
        color: "#fff" //翻页数字颜色
      },
      type: legendType,
      data: legendData,
      // icon: legendIcon,
      // left: 'right',
      orient: "horizontal", // 'vertical'
      x: legendCenter, // 'center' | 'left' | {number},
      textStyle: { color: "#fff" }
    },
    dataZoom: dataZoom,
    grid: {
      left: "1%",
      right: "7%",
      bottom: "0%",
      top: "28%",
      containLabel: true
    },
    toolbox: {
      show: toolboxShow,
      feature: {
        saveAsImage: {}
      }
    },
    xAxis: {
      minInterval: xminInterval,
      // show: xAxisType == "category" ? true : false,
      show: true,
      type: xAxisType,
      boundaryGap: boundaryGap,
      // axisLine: { show: xAxisType == "category" ? true : false },
      axisLine: { show: true },
      data: xAxisData,
      axisLabel: {
        textStyle: {
          color: "#fff"
        },
        formatter: axisLabelFormatter
      },
      splitLine: {
        show: xAxisType == "value" ? true : false,
        lineStyle: {
          // type: "dashed",
          color: "#fff"
        }
      },
      axisTick: {
        //y轴刻度线
        show: false
      }
    },
    yAxis: [{
      type: yAxisType,
      name: ' 单位:m',
      minInterval: yminInterval,
      axisLine: { show: true },
      axisLabel: {
        textStyle: {
          color: "#fff"
        },
        formatter: "{value} m"
      },
      splitLine: {
        show: xAxisType == "category" ? true : false,
        lineStyle: {
          // type: "dashed",
          color: "#fff"
        }
      },
      alignTicks: true,
      nameTextStyle: {
        color: '#fff'
      },
      nameGap: 10,
      data: xAxisData
    }, {
      type: 'value',
      name: '单位:mm  ',
      position: 'right',
      nameTextStyle: {
        color: '#fff'
      },
      show: true,
      axisLabel: {
        show: true,
        formatter: '{value} mm'
      },
      alignTicks: true,
      axisLine: {
        lineStyle: {
          color: '#fff'
        }
      },
    }],
    series: seriesData
  };
  myChart.setOption(option, true);
  if (callback) {
    myChart.on("click", function (params) {
      return callback(params);
    });
  }
  window.onresize = myChart.resize;
};

// 仪表盘
export const drawDashboardTotal = params => {
  let dom = params.dom;
  let id = document.getElementById(dom);
  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  const colors = ["#fd823b", "#fdff38", "#02d313", "#00d2ff"];
  let detailFormatter = params.detailFormatter;
  let dataValue = params.dataValue;
  let option = {
    series: [
      {
        radius: "86%",
        type: "gauge",
        axisLine: {
          lineStyle: {
            width: 10,
            color: [
              [
                0.1,
                new echarts.graphic.LinearGradient(
                  // 右下左上，渐变色从正下方开始，下面的以此类推
                  0,
                  1,
                  0,
                  0,
                  [
                    { offset: 0, color: colors[0] },
                    { offset: 0.5, color: colors[1] }
                  ]
                )
              ],
              [
                0.3,
                new echarts.graphic.LinearGradient(0, 1, 1, 0, [
                  { offset: 0, color: colors[1] },
                  { offset: 1, color: colors[2] }
                ])
              ],
              [
                0.4,
                new echarts.graphic.LinearGradient(0, 1, 1, 0, [
                  { offset: 0, color: colors[2] },
                  { offset: 1, color: colors[3] }
                ])
              ],
              [
                0.8,
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0.0, color: colors[3] },
                  { offset: 1, color: colors[2] }
                ])
              ],
              [
                0.9,
                new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                  { offset: 0.0, color: colors[2] },
                  { offset: 1, color: colors[1] }
                ])
              ],
              [
                1,
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0.5, color: colors[1] },
                  { offset: 1, color: colors[0] }
                ])
              ]
            ]
          }
        },
        pointer: {
          itemStyle: {
            color: "#407fff"
          }
        },
        axisTick: {
          distance: 2,
          length: 1,
          lineStyle: {
            color: "#52ffff",
            width: 3
          }
        },
        splitLine: {
          distance: -30,
          length: 30,
          lineStyle: {
            color: "#fff",
            width: 0
          }
        },
        axisLabel: {
          color: "#52ffff",
          distance: 14,
          fontSize: 12
        },
        detail: {
          valueAnimation: true,
          formatter: detailFormatter,
          color: "#fdff38",
          offsetCenter: [0, "76%"],
          // fontSize: 20,
          // formatter: function(val) {
          //   //图表中间文字数据展示
          //   return "{bold|" + val + "\n}{gray|" + alias + "}";
          // },
          rich: {
            bold: {
              fontSize: 14,
              color: "#fdff38",
            },
            gray: {
              fontSize: 16,
              lineHeight: 16,
              color: "#fff",
            },
          }
        },
        data: [
          {
            value: dataValue
          }
        ]
      }
    ]
  };
  myChart.setOption(option);
  // if (callback) {
  //   myChart.on("click", function(params) {
  //     return callback(params);
  //   });
  // }
  window.onresize = myChart.resize;
};
