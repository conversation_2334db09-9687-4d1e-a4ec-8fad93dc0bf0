import * as echarts from "echarts";
import { min } from "moment";
// import { call } from "file-loader";

var _color_7 = [
  "rgba(255,127,126,1)",
  "rgba(255,183,39,1)",
  "rgba(170,225,59,1)",
  "rgba(50,240,176,1)",
  "rgba(55,188,255,1)",
  "rgba(135,135,255,1)",
  "rgba(186,83,238,1)",
];

var _img =
  "data:image/png;base64,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";

//修改颜色透明度的方法
const modifyColorOpacity = (rgbaColor, opacity) => {
  // 使用正则表达式匹配 rgba 颜色值
  const rgbaRegex = /rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/;
  const match = rgbaColor.match(rgbaRegex);
  if (match) {
    const [, r, g, b] = match;
    return `rgba(${r},${g},${b},${opacity})`;
  }
  return rgbaColor;
};

// 饼图
export const drawPie = (params) => {
  let dom = params.dom,
    data = params.data,
    nameTitle = params.nameTitle,
    titleInfor = params.titleInfor ? params.titleInfor : {},
    // colorArray = params.colorArray || [],
    seriesLabel = params.seriesLabel,
    seriesRadius = params.seriesRadius || "70%",
    seriesCenter = params.seriesCenter || ["50%", "50%"],
    legendIcon = params.legendIcon || "circle",
    legendFormatter = params.legendFormatter || null,
    richNameWidth = params.richNameWidth || 80,
    itemStyleEmphasis = params.itemStyleEmphasis || {},
    noTooltipShow = !params.noTooltipShow,
    legendTop = params.legendTop || "center",
    callback = params.callback;

  let tooltipFormatter = params.tooltipFormatter || null;

  let legendType = data.length > 0 ? "scroll" : "plain";

  let id = document.getElementById(dom);
  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  let option = {
    title: {
      ...titleInfor,
      // text: nameTitle,
      // left: "center",
      // textStyle: {
      //   color: "#fff"
      // }
    },
    tooltip: {
      show: noTooltipShow,
      trigger: "item",
      formatter: tooltipFormatter,
      confine: true,
    },
    legend: {
      icon: legendIcon,
      orient: "vertical",
      type: legendType,
      pageIconColor: "#2C86FF", //翻页箭头颜色
      pageIconInactiveColor: "rgba(44,132,251,0.40)", //翻页（即翻页到头时箭头的颜色）
      pageTextStyle: {
        color: "#fff", //翻页数字颜色
      },
      right: "0",
      top: legendTop,
      textStyle: {
        color: "#fff",
        rich: {
          name: {
            verticalAlign: "right",
            align: "left",
            width: richNameWidth,
            fontSize: 10,
            color: "#D8DDE3",
            fontWeight: "bolder",
          },
          percent: {
            padding: [0, 0, 0, 0],
            color: "#D8DDE3",
          },
        },
      },
      formatter: legendFormatter,
    },
    series: [
      {
        name: "Access From",
        type: "pie",
        selectedMode: "single",
        radius: ["20%", "30%"],
        center: seriesCenter,
        label: {
          position: "inner",
          fontSize: 14,
        },
        labelLine: {
          show: false,
        },
        data: params.data,
      },
      {
        name: nameTitle,
        type: "pie",
        radius: seriesRadius,
        center: seriesCenter,
        data: data,
        itemStyle: {
          normal: {
            borderWidth: 1, // 每一个类型之间空隙
            borderColor: "#fff",
            // color: function (params) {
            //     const colorList = colorArray
            //     return colorArray.length > 0 ? colorList[params.dataIndex] : '#EA8187'
            // }
          },
        },
        label: {
          show: seriesLabel,
          position: seriesLabel ? "inside" : "center",
          formatter: "{d}%",
          fontSize: 9,
          color: "#fff",
        },
        emphasis: itemStyleEmphasis,
      },
    ],
  };
  myChart.setOption(option);
  if (callback) {
    myChart.on("click", function (params) {
      return callback(params);
    });
  }
};

// 线状图柱状图统一一个
export const drawBarLineTotal = (params) => {
  let xAxisData = params.xAxisData,
    seriesData = params.seriesData;
  let callback = params.callback;
  let unit = params.unit || "";
  let isMoreLine = params.isMoreLine || false;
  let legendIcon = params.legendIcon || "circle";
  let boundaryGap = params.boundaryGap; //取反值，看是否挨着边
  let legendCenter = params.legendCenter || "center";
  let axisPointerType = params.axisPointerType || "line";
  let xAxisType = params.xAxisType || "category";
  let yAxisType = params.yAxisType || "value";
  let dataZoom = params.dataZoom || null;

  let tooltipFormatter = params.tooltipFormatter || null;

  let yminInterval = params.yminInterval || null;
  let xminInterval = params.xminInterval || null;

  let axisLabelFormatter = params.axisLabelFormatter || null;

  let legendType = seriesData.length > 0 ? "scroll" : "plain";

  let newLegend = (function () {
    let arr = [];
    if (isMoreLine) {
      seriesData.forEach((ele) => {
        arr.push(ele.name);
      });
    } else {
      if (seriesData.name) {
        arr.push(seriesData.name);
      }
    }
    return arr;
  })();
  let legendData = params.legendData || newLegend;
  let dom = params.dom;
  let toolboxShow = params.toolboxShow || false;
  let id = document.getElementById(dom);
  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  const CubeLeft = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      const xAxisPoint = shape.xAxisPoint;
      const c0 = [shape.x, shape.y];
      const c1 = [shape.x - 9, shape.y - 9];
      const c2 = [xAxisPoint[0] - 9, xAxisPoint[1] - 9];
      const c3 = [xAxisPoint[0], xAxisPoint[1]];
      ctx
        .moveTo(c0[0], c0[1])
        .lineTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .closePath();
    },
  });
  const CubeRight = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      const xAxisPoint = shape.xAxisPoint;
      const c1 = [shape.x, shape.y];
      const c2 = [xAxisPoint[0], xAxisPoint[1]];
      const c3 = [xAxisPoint[0] + 18, xAxisPoint[1] - 9];
      const c4 = [shape.x + 18, shape.y - 9];
      ctx
        .moveTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .lineTo(c4[0], c4[1])
        .closePath();
    },
  });
  const CubeTop = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      const c1 = [shape.x, shape.y];
      const c2 = [shape.x + 18, shape.y - 9];
      const c3 = [shape.x + 9, shape.y - 18];
      const c4 = [shape.x - 9, shape.y - 9];
      ctx
        .moveTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .lineTo(c4[0], c4[1])
        .closePath();
    },
  });
  echarts.graphic.registerShape("CubeLeft", CubeLeft);
  echarts.graphic.registerShape("CubeRight", CubeRight);
  echarts.graphic.registerShape("CubeTop", CubeTop);
  const MAX = [
    6000, 6000, 6000, 6000, 6000, 5000, 4000, 3000, 2000, 4000, 3000, 2000,
  ];
  const VALUE = [
    2012, 1230, 3790, 2349, 1654, 1230, 3790, 2349, 1654, 3790, 2349, 1654,
  ];

  // let myChart = echarts.init(id);
  let option = {
    // xAxis: {
    //   type: 'category',
    //   data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    // },
    // yAxis: {
    //   type: 'value'
    // },
    // series: [
    //   {
    //     data: [120, 200, 150, 80, 70, 110, 130],
    //     itemStyle: {
    //       // 立体效果核心配置
    //       color: {
    //         type: 'linear',
    //         x: 0,   // 渐变起点x
    //         y: 0,   // 渐变起点y
    //         x2: 0.8,// 渐变终点x（控制立体感）
    //         y2: 0,  // 渐变终点y
    //         colorStops: [{
    //           offset: 0, color: '#5470c6' // 0%处的颜色（主色）
    //         }, {
    //           offset: 0.7, color: '#5470c6' // 70%处颜色不变
    //         }, {
    //           offset: 1, color: '#83a6ed' // 100%处的颜色（高光）
    //         }]
    //       },
    //       // 增强立体感的阴影
    //       shadowBlur: 8,
    //       shadowColor: 'rgba(0, 0, 0, 0.3)',
    //       shadowOffsetX: 3,
    //       shadowOffsetY: 3
    //     },
    //     // 柱子圆角（增强立体感）
    //     barBorderRadius: [5, 5, 0, 0],
    //     // 柱子宽度
    //     barWidth: 30,
    //     type: 'bar',
    //     showBackground: true,
    //     backgroundStyle: {
    //       color: 'rgba(180, 180, 180, 0.2)'
    //     }
    //   },
    //   {
    //     type: 'custom',
    //     renderItem: (params, api) => {
    //       const barIndex = api.value(0);
    //       const barHeight = api.coord([barIndex, api.value(1)]);
    //       const x = barHeight[0];
    //       const y = barHeight[1];
    //       const barWidth = api.size([1, 0])[0];
    //       const diamondSize = barWidth * 0.8; // 菱形大小
    //       const halfSize = diamondSize / 2;

    //       return {
    //         type: 'path',
    //         shape: {
    //           pathData: `M ${x} ${y - halfSize} L ${x + halfSize} ${y} L ${x} ${y + halfSize} L ${x - halfSize} ${y} Z`
    //         },
    //         style: api.style({
    //           fill: 'rgba(92,123,217,1)', // 菱形颜色
    //           opacity: 0.8
    //         })
    //       };
    //     },
    //     data: [120, 200, 150, 80, 70, 110, 130],
    //   }
    // ]

    //2.5D
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      backgroundColor: "rgba(0,0,0,0.8)",
      borderColor: theme.color[1],
      borderWidth: 1,
      textStyle: {
        color: "#fff",
        fontSize: 14,
      },
      formatter: function (params) {
        return `<strong>${params.name}</strong><br>销售额: ${params.value} 万元`;
      },
    },
    grid: {
      left: "5%",
      right: "5%",
      bottom: "15%",
      top: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: categories,
      axisLine: {
        lineStyle: {
          color: "#aaa",
        },
      },
      axisLabel: {
        color: "#ddd",
        fontSize: 14,
        margin: 20,
        interval: 0,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      name: "销售额 (万元)",
      nameTextStyle: {
        color: "#aaa",
        fontSize: 14,
        padding: [0, 0, 0, -40],
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#aaa",
        },
      },
      splitLine: {
        lineStyle: {
          color: "rgba(100, 100, 100, 0.3)",
        },
      },
      axisLabel: {
        color: "#ddd",
        fontSize: 12,
      },
    },
    series: [
      {
        name: "销售额",
        type: "bar",
        // type: 'custom',
        data: data,
        barWidth: 60,
        itemStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: theme.color[0],
              },
              {
                offset: 1,
                color: theme.color[1],
              },
            ],
          },
          borderColor: theme.borderColor,
          borderWidth: 1,
          borderRadius: [6, 6, 0, 0],
          shadowColor: "rgba(0, 0, 0, 0.5)",
          shadowBlur: 10,
          shadowOffsetY: 5,
        },
        // 2.5D效果 - 顶部高光
        emphasis: {
          itemStyle: {
            shadowColor: theme.highlight,
            shadowBlur: 20,
          },
        },
        // 2.5D效果 - 侧面梯形
        renderItem: function (params, api) {
          const xValue = api.value(0);
          const yValue = api.value(1);
          const x = api.coord([xValue, 0])[0];
          const y = api.coord([xValue, yValue])[1];
          const width = api.size([1, 0])[0] * 0.6;
          const depth = 10;

          return {
            type: "group",
            children: [
              // 主柱体
              {
                type: "rect",
                shape: {
                  x: x - width / 2,
                  y: y,
                  width: width,
                  height: api.coord([0, yValue])[1] - y,
                },
                style: {
                  fill: {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: theme.color[0],
                      },
                      {
                        offset: 1,
                        color: theme.color[1],
                      },
                    ],
                  },
                  stroke: theme.borderColor,
                  lineWidth: 1,
                },
                styleEmphasis: {
                  shadowBlur: 20,
                  shadowColor: theme.highlight,
                },
              },
              // 右侧面
              {
                type: "polygon",
                shape: {
                  points: [
                    [x + width / 2, y],
                    [x + width / 2 + depth, y - depth],
                    [
                      x + width / 2 + depth,
                      y - depth + (api.coord([0, yValue])[1] - y),
                    ],
                    [x + width / 2, y + (api.coord([0, yValue])[1] - y)],
                  ],
                },
                style: {
                  fill: {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: echarts.color.lift(theme.color[1], -0.2),
                      },
                      {
                        offset: 1,
                        color: echarts.color.lift(theme.color[1], -0.4),
                      },
                    ],
                  },
                  stroke: theme.borderColor,
                  lineWidth: 1,
                },
              },
              // 顶部面
              {
                type: "polygon",
                shape: {
                  points: [
                    [x - width / 2, y],
                    [x + width / 2, y],
                    [x + width / 2 + depth, y - depth],
                    [x - width / 2 + depth, y - depth],
                  ],
                },
                style: {
                  fill: theme.highlight,
                  opacity: 1,
                },
              },
            ],
          };
        },
        // 数据标签
        label: {
          show: true,
          position: "top",
          distance: 10,
          color: "#fff",
          fontSize: 14,
          fontWeight: "bold",
          formatter: "{c}",
          textShadowBlur: 10,
          textShadowColor: "rgba(0, 0, 0, 0.8)",
        },
        // 动画配置
        animation: animationEnabled,
        animationDuration: 1500,
        animationEasing: "elasticOut",
        animationDelay: function (idx) {
          return idx * 200;
        },
      },
    ],
  };
  myChart.setOption(option, true);
  if (callback) {
    myChart.on("click", function (params) {
      return callback(params);
    });
  }
  window.onresize = myChart.resize;
};

// 2.5D柱状图
export const drawCustomBar = (params) => {
  let xAxisData = params.xAxisData,
    seriesData = params.seriesData;
  let callback = params.callback;

  let dom = params.dom;

  let id = document.getElementById(dom);

  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  const CubeLeft = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      const xAxisPoint = shape.xAxisPoint;
      const c0 = [shape.x, shape.y];
      const c1 = [shape.x - 5, shape.y - 5];
      const c2 = [xAxisPoint[0] - 5, xAxisPoint[1] - 5];
      const c3 = [xAxisPoint[0], xAxisPoint[1]];
      ctx
        .moveTo(c0[0], c0[1])
        .lineTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .closePath();
    },
  });
  const CubeRight = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      const xAxisPoint = shape.xAxisPoint;
      const c1 = [shape.x, shape.y];
      const c2 = [xAxisPoint[0], xAxisPoint[1]];
      const c3 = [xAxisPoint[0] + 10, xAxisPoint[1] - 5];
      const c4 = [shape.x + 10, shape.y - 5];
      ctx
        .moveTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .lineTo(c4[0], c4[1])
        .closePath();
    },
  });
  const CubeTop = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      const c1 = [shape.x, shape.y];
      const c2 = [shape.x + 10, shape.y - 5];
      const c3 = [shape.x + 5, shape.y - 10];
      const c4 = [shape.x - 5, shape.y - 5];
      ctx
        .moveTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .lineTo(c4[0], c4[1])
        .closePath();
    },
  });
  echarts.graphic.registerShape("CubeLeft", CubeLeft);
  echarts.graphic.registerShape("CubeRight", CubeRight);
  echarts.graphic.registerShape("CubeTop", CubeTop);

  let legendData = [];

  seriesData.forEach((item) => {
    if (item.name) {
      legendData.push(item.name);
    }
  });

  let option = {
    // backgroundColor: "#010d3a",
    tooltip: {
      trigger: "item",
    },
    legend: {
      pageIconColor: "#2C86FF", //翻页箭头颜色
      pageIconInactiveColor: "rgba(44,132,251,0.40)", //翻页（即翻页到头时箭头的颜色）
      pageTextStyle: {
        color: "#fff", //翻页数字颜色
      },
      type: "plain",
      data: legendData,
      icon: "rect",
      // left: 'right',
      orient: "horizontal", // 'vertical'
      x: "left", // 'center' | 'left' | {number},
      textStyle: { color: "#fff" },
      itemWidth: 10,
      itemHeight: 6,
    },
    title: {
      text: "",
      top: 32,
      left: 18,
      textStyle: {
        color: "#00F6FF",
        fontSize: 24,
      },
    },
    grid: {
      left: "5%",
      right: "8%",
      bottom: "5%",
      top: "18%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      axisLine: {
        show: true,
        lineStyle: {
          color: "white",
        },
      },
      offset: 0,
      axisTick: {
        show: false,
        length: 9,
        alignWithLabel: true,
        lineStyle: {
          color: "#7DFFFD",
        },
      },
      axisLabel: {
        fontSize: 10,
        // offset: 15,
        padding: [0, 0, 0, 18],
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: true,
        lineStyle: {
          color: "white",
        },
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 16,
      },
      boundaryGap: ["20%", "20%"],
    },
    series: [
      {
        type: "custom",
        name: seriesData[0].name,
        renderItem: (params, api) => {
          // console.log(params.dataindex)
          let location = api.coord([api.value(0), api.value(1)]);
          // location = [location[0]  , location[1]]
          return {
            type: "group",
            children: [
              {
                type: "CubeLeft",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#1283FF",
                    },
                    {
                      offset: 1,
                      color: "#1283FF",
                    },
                  ]),
                },
              },
              {
                type: "CubeRight",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#1283FF",
                    },
                    {
                      offset: 1,
                      color: "#1283FF69",
                    },
                  ]),
                },
              },
              {
                type: "CubeTop",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#1283FF",
                    },
                    {
                      offset: 1,
                      color: "#1283FF69",
                    },
                  ]),
                },
              },
              {
                type: "text",
                style: {
                  text: seriesData[0].data[params.dataIndex],
                  fill: "#fff", // 文本颜色
                  fontSize: 12, // 文本大小
                  textAlign: "center",
                  textBaseline: "bottom",
                },
                position: [location[0] + 2, location[1] - 20], // 文本位置
              },
            ],
          };
        },
        data: seriesData[0].data,
        itemStyle: {
          color: "#1283FF",
        },
      },

      {
        type: "custom",
        name: seriesData[1].name,
        renderItem: (params, api) => {
          const location = api.coord([api.value(0), api.value(1)]);
          const xAxisArr = api.coord([api.value(0), 0]);
          return {
            type: "group",
            children: [
              {
                type: "CubeLeft",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0] + 18, //第二个柱子 右偏移量
                  y: location[1],
                  xAxisPoint: [xAxisArr[0] + 18, xAxisArr[1]],
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#20F8FF",
                    },
                    {
                      offset: 1,
                      color: "#20F8FF69",
                    },
                  ]),
                },
              },
              {
                type: "CubeRight",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0] + 18, //第二个柱子 右偏移量
                  y: location[1],
                  xAxisPoint: [xAxisArr[0] + 18, xAxisArr[1]],
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#20F8FF",
                    },
                    {
                      offset: 1,
                      color: "#20f8ff69",
                    },
                  ]),
                },
              },
              {
                type: "CubeTop",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0] + 18, //第二个柱子 右偏移量
                  y: location[1],
                  xAxisPoint: [xAxisArr[0] + 18, xAxisArr[1]],
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#20F8FF",
                    },
                    {
                      offset: 1,
                      color: "#20F8FF69",
                    },
                  ]),
                },
              },
              {
                type: "text",
                style: {
                  text: seriesData[1].data[params.dataIndex],
                  fill: "#fff", // 文本颜色
                  fontSize: 12, // 文本大小
                  textAlign: "center",
                  textBaseline: "bottom",
                },
                position: [location[0] + 20, location[1] - 20], // 文本位置
              },
            ],
          };
        },
        data: seriesData[1].data,
        itemStyle: {
          color: "#20F8FF",
        },
      },
    ],
  };
  myChart.setOption(option, true);
  if (callback) {
    myChart.on("click", function (params) {
      return callback(params);
    });
  }
  window.onresize = myChart.resize;
};

// 横向柱状图
export const drawHorizontalBarChart = (params) => {
  let xAxisData = params.xAxisData,
    seriesData = params.seriesData;
  let callback = params.callback;
  let unit = params.unit || "";
  let isMoreLine = params.isMoreLine || false;
  let legendIcon = params.legendIcon || "circle";
  let boundaryGap = params.boundaryGap; //取反值，看是否挨着边
  let legendCenter = params.legendCenter || "center";
  let axisPointerType = params.axisPointerType || "line";
  let xAxisType = params.xAxisType || "category";
  let yAxisType = params.yAxisType || "value";
  let dataZoom = params.dataZoom || null;

  let tooltipFormatter = params.tooltipFormatter || null;

  let yminInterval = params.yminInterval || null;
  let xminInterval = params.xminInterval || null;

  let axisLabelFormatter = params.axisLabelFormatter || null;

  let legendType = seriesData.length > 0 ? "scroll" : "plain";

  let series_label_normal_position = params.series_label_normal_position || [
    0,
    "-20",
  ]; // 第一个是水平偏移，第二个是垂直偏移

  let grid = params.grid || {
    borderWidth: 0,
    top: "14%",
    left: "2%",
    right: "21%",
    bottom: "3%",
  };

  seriesData = seriesData[0].data;

  var color = [
    "rgba(90,195,255",
    "rgba(99,230,234",
    "rgba(255,217,121",
    "rgba(255,148,121",
  ]; //,

  let lineY = [];
  for (var i = 0; i < xAxisData.length; i++) {
    var x = i;
    if (x > color.length - 1) {
      x = color.length - 1;
    }
    var data = {
      name: xAxisData[i],
      color: color[x] + ")",
      value: seriesData[i],
      itemStyle: {
        normal: {
          show: true,
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 0,
                color: color[x] + ", 1)",
              },
              {
                offset: 1,
                color: color[x] + ", 0.3)",
              },
            ],
            false
          ),
          barBorderRadius: 10,
        },
        emphasis: {
          shadowBlur: 15,
          shadowColor: "rgba(0, 0, 0, 0.1)",
        },
      },
    };
    lineY.push(data);
  }

  let legendData = params.legendData;
  let dom = params.dom;
  let toolboxShow = params.toolboxShow || false;
  let id = document.getElementById(dom);
  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  let option = {
    // backgroundColor: '#000',
    title: {
      show: false,
    },
    tooltip: {
      //trigger: 'item',
      trigger: "axis",
      axisPointer: {
        type: axisPointerType,
      },
      confine: true,
    },
    grid: grid,
    color: color,
    yAxis: [
      {
        type: "category",
        inverse: true,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          show: false,
          inside: false,
        },
        data: xAxisData,
      },
      {
        type: "category",
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          inside: false,
          textStyle: {
            color: "#fff",
            fontSize: "14",
            fontFamily: "PingFangSC-Regular",
          },
          formatter: function (val) {
            return `${val}%`;
          },
        },
        splitArea: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        data: seriesData,
      },
    ],
    xAxis: {
      type: "value",
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
    },
    series: [
      {
        name: "",
        type: "bar",
        zlevel: 2,
        barWidth: "10px",
        data: lineY,
        animationDuration: 1500,
        label: {
          normal: {
            color: "#fff",
            show: true,
            position: series_label_normal_position,
            textStyle: {
              fontSize: 14,
            },
            formatter: function (a, b) {
              return a.name;
            },
          },
        },
      },
    ],
    animationEasing: "cubicOut",
  };
  myChart.setOption(option, true);
  if (callback) {
    myChart.on("click", function (params) {
      return callback(params);
    });
  }
  window.onresize = myChart.resize;
};

// 圆环饼图
export const drawRadiusPie = (params) => {
  let dom = params.dom,
    data = params.data,
    nameTitle = params.nameTitle,
    titleInfor = params.titleInfor ? params.titleInfor : {},
    // colorArray = params.colorArray || [],
    seriesLabel = params.seriesLabel,
    seriesRadius = params.seriesRadius || "70%",
    seriesCenter = params.seriesCenter || ["50%", "50%"],
    legendIcon = params.legendIcon || "circle",
    legendFormatter = params.legendFormatter || null,
    richNameWidth = params.richNameWidth || 80,
    itemStyleEmphasis = params.itemStyleEmphasis || {},
    noTooltipShow = !params.noTooltipShow,
    legendTop = params.legendTop || "center",
    callback = params.callback;

  let tooltipFormatter = params.tooltipFormatter || null;

  let legendType = data.length > 0 ? "scroll" : "plain";

  let id = document.getElementById(dom);
  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  let img = params.img || null;
  let seriesData = [];
  let color = [
    "#00F9E9",
    "#F86A49yellow",
    "#C5A743",
    "#5D92FF",
    "#FF386B",
    "#ffffff",
    "#ff3000",
  ];

  for (let i = 0; i < data.length; i++) {
    seriesData.push(
      {
        value: data[i].value,
        name: data[i].name,
        itemStyle: {
          normal: {
            borderWidth: 5,
            shadowBlur: 20,
            borderColor: color[i],
            shadowColor: color[i],
          },
        },
      },
      {
        value: 1,
        name: "",
        itemStyle: {
          normal: {
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            color: "rgba(0, 0, 0, 0)",
            borderColor: "rgba(0, 0, 0, 0)",
            borderWidth: 0,
          },
        },
      }
    );
  }

  let seriesObj = [
    {
      name: "",
      type: "pie",
      clockWise: false,
      radius: [50, 50],
      hoverAnimation: false,
      itemStyle: {
        normal: {
          label: {
            show: true,
            position: "outside",
            color: "#ddd",
            formatter: function (params) {
              var percent = 0;
              var total = 0;
              for (var i = 0; i < data.length; i++) {
                total += data[i].value;
              }
              percent = ((params.value / total) * 100).toFixed(0);
              if (params.name !== "") {
                return params.name + "\n{white|" + percent + "%}";
              } else {
                return "";
              }
            },
            rich: {
              white: {
                color: "#ddd",
                align: "center",
                padding: [3, 0],
              },
            },
          },
          labelLine: {
            length: 12,
            length2: 30,
            show: true,
            color: "#00ffff",
          },
        },
      },
      data: seriesData,
    },
  ];
  // alert(myChart.getDom().clientWidth); // 320 => 85   252 => 51
  // 图表宽度的一半 减去 背景图宽度的一半
  let _x = myChart.getDom().clientWidth / 2 - 75;
  let option = {
    graphic: {
      elements: [
        {
          type: "image",
          z: -1,
          style: {
            image: img,
            width: 150,
            height: 150
          },
          left: _x,
          top: "center",
        },
      ],
    },
    tooltip: {
      show: true,
    },
    legend: {
      show: false,
    },
    toolbox: {
      show: false,
    },
    series: seriesObj,
  };
  myChart.setOption(option);
  if (callback) {
    myChart.on("click", function (params) {
      return callback(params);
    });
  }
};

// 带边框双圆环 饼图
export const drawBorderPie = (params) => {
  let dom = params.dom,
    data = params.data,
    nameTitle = params.nameTitle,
    titleInfor = params.titleInfor ? params.titleInfor : {},
    // colorArray = params.colorArray || [],
    seriesLabel = params.seriesLabel,
    seriesRadius = params.seriesRadius || "70%",
    seriesCenter = params.seriesCenter || ["50%", "50%"],
    legendIcon = params.legendIcon || "circle",
    legendFormatter = params.legendFormatter || null,
    richNameWidth = params.richNameWidth || 80,
    itemStyleEmphasis = params.itemStyleEmphasis || {},
    noTooltipShow = !params.noTooltipShow,
    legendTop = params.legendTop || "center",
    callback = params.callback;

  let tooltipFormatter = params.tooltipFormatter || null;

  let legendType = data.length > 0 ? "scroll" : "plain";

  let id = document.getElementById(dom);
  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  const _pie2 = () => {
    let dataArr = [];
    let _color = {
      type: "linear",
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "#9933FF", // 0% 处的颜色
        },
        {
          offset: 1,
          color: "#00CCFF", // 100% 处的颜色
        },
      ],
      global: false, // 缺省为 false
    };
    for (var i = 0; i < 16; i++) {
      if (i % 4 === 0) {
        dataArr.push({
          name: (i + 1).toString(),
          value: 50,
          itemStyle: {
            normal: {
              //color: "rgba(88,142,197,0.5)",
              borderWidth: 0,
              borderColor: "rgba(0,0,0,0)",
            },
          },
        });
      } else if (i % 4 === 1) {
        dataArr.push({
          name: (i + 1).toString(),
          value: 2,
          itemStyle: {
            normal: {
              color: "rgba(88,142,197,0)",
              borderWidth: 0,
              borderColor: "rgba(0,0,0,0)",
            },
          },
        });
      } else if (i % 4 === 2) {
        dataArr.push({
          name: (i + 1).toString(),
          value: 20,
          itemStyle: {
            normal: {
              //color: "rgba(88,142,197,0.2)",
              borderWidth: 0,
              borderColor: "rgba(0,0,0,0)",
            },
          },
        });
      } else {
        dataArr.push({
          name: (i + 1).toString(),
          value: 2,
          itemStyle: {
            normal: {
              //color: "rgba(0,0,0,0)",
              color: "rgba(88,142,197,0)",
              borderWidth: 0,
              borderColor: "rgba(0,0,0,0)",
            },
          },
        });
      }
    }
    return dataArr;
  };
  let option = {
    backgroundColor: "transparent",
    title: {
      text: "386",
      subtext: "总预警/次",
      x: "center",
      y: "25%",
      textStyle: {
        fontSize: 30,
        fontWeight: "normal",
        color: ["#fff"],
      },
      subtextStyle: {
        color: "#fff",
        fontSize: 12,
      },
    },
    grid: {
      bottom: 150,
      left: "10%",
      right: "30%",
    },
    legend: {
      show: true,
      orient: "vertical",
      top: "middle",
      right: "5%",
      textStyle: {
        color: "#fff",
        fontSize: 14,
      },
      icon: "roundRect",
    },
    series: [
      {
        radius: ["20%", "46%"],
        center: ["40%", "50%"],
        type: "pie",
        itemStyle: {
          // normal: {
          borderRadius: 0,
          borderWidth: 8, // 每一个类型之间空隙
          borderColor: "transparent",
          // borderColor: "rgb(11,52,114)",
          // color: function (params) {
          //   return colorList[params.dataIndex]
          // }
          // }
        },
        // labelLine: {
        //   normal: {
        //     show: true,
        //     length: 15,
        //     length2: 120,
        //     lineStyle: {
        //       color: '#d3d3d3'
        //     },
        //     align: 'right'
        //   },
        //   color: "#000",
        //   emphasis: {
        //     show: true
        //   }
        // },
        // label: {
        //   normal: {
        //     formatter: function (params) {
        //       var str = '';
        //       switch (params.name) {
        //         case '体育技能': str = '{a|}\n{nameStyle|体育技能 }' + '{rate|' + params.value + '%}'; break;
        //         case '体育行为': str = '{b|}\n{nameStyle|体育行为 }' + '{rate|' + params.value + '%}'; break;
        //         case '体质健康': str = '{c|}\n{nameStyle|体质健康 }' + '{rate|' + params.value + '%}'; break;
        //         case '体育意识': str = '{d|}\n{nameStyle|体育意识 }' + '{rate|' + params.value + '%}'; break;
        //         case '体育知识': str = '{e|}\n{nameStyle|体育知识 }' + '{rate|' + params.value + '%}'; break;
        //       }
        //       return str
        //     },
        //     padding: [0, -110],
        //     height: 165,
        //     rich: {
        //       a: {
        //         width: 38,
        //         height: 38,
        //         lineHeight: 50,
        //         align: 'left'
        //       },
        //       b: {
        //         width: 29,
        //         height: 45,
        //         lineHeight: 50,
        //         align: 'left'
        //       },
        //       c: {
        //         width: 34,
        //         height: 33,
        //         lineHeight: 50,
        //         align: 'left'
        //       },
        //       d: {
        //         width: 34,
        //         height: 44,
        //         lineHeight: 50,
        //         align: 'left'
        //       },
        //       e: {
        //         width: 38,
        //         height: 30,
        //         lineHeight: 50,
        //         align: 'left'
        //       },
        //       nameStyle: {
        //         fontSize: 16,
        //         color: "#555",
        //         align: 'left'
        //       },
        //       rate: {
        //         fontSize: 20,
        //         color: "#1ab4b8",
        //         align: 'left'
        //       }
        //     }
        //   }
        // },
        // data: [1, 2, 3],
        data: data,
      },
      // 边框的设置
      {
        radius: ["47%", "51%"],
        center: ["40%", "50%"],
        type: "pie",
        label: {
          normal: {
            show: false,
          },
          emphasis: {
            show: false,
          },
        },
        labelLine: {
          normal: {
            show: false,
          },
          emphasis: {
            show: false,
          },
        },
        animation: false,
        tooltip: {
          show: false,
        },
        itemStyle: {
          normal: {
            color: "rgba(250,250,250,0.5)",
          },
        },
        data: [
          {
            value: 10,
          },
        ],
      },
    ],
  };

  myChart.setOption(option);
  if (callback) {
    myChart.on("click", function (params) {
      return callback(params);
    });
  }

  let timer;

  const doing = () => {
    let option = myChart.getOption();
    option.series[1].startAngle = option.series[1].startAngle - 1;
    //option.series[2].startAngle = option.series[2].startAngle - 1;
    //option.series[6].data[0].value = option.series[6].data[0].value + 1;
    myChart.setOption(option);
  };

  const startTimer = () => {
    timer = setInterval(doing, 100);
  };
  const stopTimer = () => {
    clearInterval(timer);

    xzTimer = null;
  };

  // setTimeout(startTimer, 500);
};

//2.5D柱状图--单柱图
export const drawCustomBarSingle = (params) => {
  let xAxisData = params.xAxisData,
    seriesData = params.seriesData;
  let callback = params.callback;
  let axisLabelFormatter = params.axisLabelFormatter || null;
  let grid = params.grid || {
    left: "1%",
    right: "1%",
    bottom: "1%",
    top: "10%",
    containLabel: true,
  };

  let dom = params.dom;

  let id = document.getElementById(dom);

  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  const CubeLeft = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      const xAxisPoint = shape.xAxisPoint;
      const c0 = [shape.x, shape.y];
      const c1 = [shape.x - 5, shape.y - 5];
      const c2 = [xAxisPoint[0] - 5, xAxisPoint[1] - 5];
      const c3 = [xAxisPoint[0], xAxisPoint[1]];
      ctx
        .moveTo(c0[0], c0[1])
        .lineTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .closePath();
    },
  });
  const CubeRight = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      const xAxisPoint = shape.xAxisPoint;
      const c1 = [shape.x, shape.y];
      const c2 = [xAxisPoint[0], xAxisPoint[1]];
      const c3 = [xAxisPoint[0] + 10, xAxisPoint[1] - 5];
      const c4 = [shape.x + 10, shape.y - 5];
      ctx
        .moveTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .lineTo(c4[0], c4[1])
        .closePath();
    },
  });
  const CubeTop = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      const c1 = [shape.x, shape.y];
      const c2 = [shape.x + 10, shape.y - 5];
      const c3 = [shape.x + 5, shape.y - 10];
      const c4 = [shape.x - 5, shape.y - 5];
      ctx
        .moveTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .lineTo(c4[0], c4[1])
        .closePath();
    },
  });
  echarts.graphic.registerShape("CubeLeft", CubeLeft);
  echarts.graphic.registerShape("CubeRight", CubeRight);
  echarts.graphic.registerShape("CubeTop", CubeTop);

  let option = {
    // backgroundColor: "#010d3a",
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params, ticket, callback) {
        const item = params[1];
        return item.name + " : " + item.value;
      },
    },
    grid: grid,
    xAxis: {
      type: "category",
      data: xAxisData,
      axisLine: {
        show: true,
        lineStyle: {
          color: "#A4ABBB"
        },
      },
      axisTick: {
        show: false,
        length: 9,
        alignWithLabel: true,
        lineStyle: {
          color: "#7DFFFD",
        },
      },
      axisLabel: {
        fontSize: 12,
        rotate: 30,
        formatter: axisLabelFormatter,
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: true,
        lineStyle: {
          color: "#A4ABBB",
        },
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 14,

      },
      boundaryGap: ["20%", "20%"],
      min: 0,
    },
    series: [
      {
        type: "custom",
        renderItem: (params, api) => {
          const location = api.coord([api.value(0), api.value(1)]);
          return {
            type: "group",
            children: [
              {
                type: "CubeLeft",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#1283FF",
                    },
                    {
                      offset: 1,
                      color: "#1283FF",
                    },
                  ]),
                },
              },
              {
                type: "CubeRight",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#1283FF",
                    },
                    {
                      offset: 1,
                      color: "#1283FF69",
                    },
                  ]),
                },
              },
              {
                type: "CubeTop",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#1283FF",
                    },
                    {
                      offset: 1,
                      color: "#1283FF",
                    },
                  ]),
                },
              },
            ],
          };
        },
        data: seriesData[0].data,
      },
      {
        type: "bar",
        label: {
          normal: {
            show: true,
            position: "top",
            fontSize: 14,
            color: "#fff",
          },
        },
        itemStyle: {
          color: "transparent",
        },
        tooltip: {},
        data: seriesData[0].data,
      },
    ],
  };
  myChart.setOption(option, true);
  if (callback) {
    myChart.on("click", function (params) {
      return callback(params);
    });
  }
  window.onresize = myChart.resize;
};

//折线图--折线堆积渐变图
export const drawLineStackShadow = (params) => {
  let xAxisData = params.xAxisData,
    seriesData = seriesDataSetAreaStyle(params.seriesData);
  let callback = params.callback;
  let axisLabelFormatter = params.axisLabelFormatter || null;
  let unit = params.unit || "";
  let isMoreLine = params.isMoreLine || false;
  let grid = params.grid || {
    top: "18%",
    left: "15%",
    right: "1%",
    bottom: "15%",
  };

  let newLegend = (function () {
    let arr = [];
    if (isMoreLine) {
      seriesData.forEach((ele) => {
        arr.push(ele.name);
      });
    } else {
      if (seriesData.name) {
        arr.push(seriesData.name);
      }
    }
    return arr;
  })();

  let legendData = params.legendData || newLegend;
  let legendCenter = params.legendCenter || "center";
  let legendIcon = params.legendIcon || "circle";
  let legendType = seriesData.length > 0 ? "scroll" : "plain";
  let dom = params.dom;

  let id = document.getElementById(dom);

  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  let option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        lineStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(255,255,255,0)", // 0% 处的颜色
              },
              {
                offset: 0.5,
                color: "rgba(255,255,255,1)", // 100% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(255,255,255,0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    },
    legend: {
      pageIconColor: "#2C86FF", //翻页箭头颜色
      pageIconInactiveColor: "rgba(44,132,251,0.40)", //翻页（即翻页到头时箭头的颜色）
      pageTextStyle: {
        color: "#fff", //翻页数字颜色
      },
      type: legendType,
      data: legendData,
      icon: legendIcon,
      // left: 'right',
      orient: "horizontal", // 'vertical'
      x: legendCenter, // 'center' | 'left' | {number},
      textStyle: { color: "#fff" },
    },
    grid: grid,
    xAxis: [
      {
        type: "category",
        boundaryGap: true,
        axisLine: {
          //坐标轴轴线相关设置。数学上的x轴
          show: true,
          lineStyle: {
            color: "rgba(255,255,255,0.4)",
          },
        },
        axisLabel: {
          //坐标轴刻度标签的相关设置
          textStyle: {
            color: "#d1e6eb",
            margin: 15,
          },
        },
        axisTick: {
          show: false,
        },
        data: xAxisData,
      },
    ],
    yAxis: [
      {
        type: "value",
        min: 0,
        // max: 140,
        splitNumber: 4,
        splitLine: {
          show: true,
          lineStyle: {
            color: "rgba(255,255,255,0.1)",
          },
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          show: true,
          margin: 20,
          textStyle: {
            color: "#d1e6eb",
          },
          formatter: "{value}" + unit,
        },
        axisTick: {
          show: true,
        },
      },
    ],
    series: seriesData,
  };

  myChart.setOption(option, true);
  if (callback) {
    myChart.on("click", function (params) {
      return callback(params);
    });
  }
  window.onresize = myChart.resize;
};

//系列数据增加区域面积
const seriesDataSetAreaStyle = (seriesData) => {
  var _series = seriesData;
  for (var i = 0; i < _series.length; i++) {
    var color = _color_7[i % _color_7.length];
    var color_opa = modifyColorOpacity(color, 0.3);
    _series[i].symbol = "none";
    // 检查 lineStyle 属性是否存在，不存在则创建
    if (!_series[i].lineStyle) {
      _series[i].lineStyle = { normal: {} };
    }
    _series[i].lineStyle.normal.color = color;

    // 检查 itemStyle 属性是否存在，不存在则创建
    if (!_series[i].itemStyle) {
      _series[i].itemStyle = {};
    }
    _series[i].lineStyle.normal.color = color;
    _series[i].itemStyle.color = color;
    _series[i].itemStyle.borderColor = color;
    _series[i].areaStyle = {
      color: new echarts.graphic.LinearGradient(
        0,
        0,
        0,
        1,
        [
          {
            offset: 0,
            color: color,
          },
          {
            offset: 1,
            color: color_opa,
          },
        ],
        false
      ),
      shadowColor: color_opa,
      shadowBlur: 20,
    };
  }
  return _series;
};

// 三层饼图--环形图
export const drawTripleAnnularChart = (params) => {
  let dom = params.dom;

  let seriesData1 = params.data1 || [];
  let seriesData2 = params.data2 || [];

  let legendData1 = AnnularChartLegendData(seriesData1);
  let legendData2 = AnnularChartLegendData(seriesData2);

  // 计算总和只取第一层数据
  let totalCount = seriesData1.reduce((sum, item) => sum + item.value, 0);

  // 第一层和第二层饼图的半径
  let seriesRadius1 = params.seriesRadius1 || ["40%", "50%"];
  let seriesRadius2 = params.seriesRadius2 || ["60%", "70%"];

  // 装饰配置
  let decorativeRings = generateDecorativeRings(seriesCenter, {
    decorativeRingInnerRadius: "58%",
    decorativeRingOuterRadius: "60%",
    highlightRingInnerRadius: "64%",
    highlightRingOuterRadius: "65%",
    highlightStartAngle: 130,
    innerRingColor: '#325280',
    highlightColor: '#2a92caff'
  });

  let seriesCenter = params.seriesCenter || ["25%", "50%"];
  let series_itemStyle_borderColor = params.series_itemStyle_borderColor || '#081E52';

  // 第一层环形图配置 - 添加立体效果
  let seriesObj1 = AnunlarChartSeriesObj(seriesData1, seriesRadius1, seriesCenter, series_itemStyle_borderColor);
  seriesObj1.forEach(item => {
    item.itemStyle = {
      opacity: 1, // 设置低透明度
      borderWidth: 1,
    };
    item.emphasis = {
      itemStyle: {
        opacity: 0.6, // 高亮时稍微增加一点透明度
      }
    };
  });

  // 第二层环形图配置 - 降低透明度
  let seriesObj2 = AnunlarChartSeriesObj(seriesData2, seriesRadius2, seriesCenter, series_itemStyle_borderColor);
  seriesObj2.forEach(item => {
    if (item.itemStyle && item.itemStyle.color) {
      item.itemStyle.opacity = 0.5; // 比内层稍高的透明度
    }
  });

  let grid = params.grid || {
    top: "10%",
    left: "2%",
    right: "1%",
    bottom: "10%",
  };
  let subtext = params.subtext || "总数";

  let id = document.getElementById(dom);
  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  let option = {
    title: {
      text: `${totalCount}`,
      subtext: subtext,
      textStyle: {
        color: "#f2f2f2",
        fontSize: 16,
      },
      subtextStyle: {
        fontSize: 14,
        color: ["#ff9d19"],
      },
      textAlign: "center",
      left: seriesCenter[0],
      top: "middle",
    },
    tooltip: {
      trigger: "item",
    },
    grid: grid,
    legend: {
      orient: "vertical",
      //right: 5,
      left: '52%',
      top: "middle",
      itemWidth: 14,
      itemHeight: 14,
      textStyle: {
        color: "#fff",
      },
      itemGap: 5,
      data: [...legendData1, ...legendData2],
    },
    series: [...seriesObj1, ...seriesObj2, ...decorativeRings],
  };

  myChart.setOption(option, true);
  if (params.callback) {
    myChart.on("click", function (params) {
      return params.callback(params);
    });
  }
  window.onresize = myChart.resize;
};

//饼图--环形图
export const drawAnnularChart = (params) => {
  let dom = params.dom;

  let seriesData = [];
  if (params.data) {
    seriesData = params.data;
  } else if (params.seriesData) {
    seriesData = params.seriesData;
  }
  let totalData = params.totalData || AnnularChartTotalData(seriesData);

  let legendData = params.legendData || AnnularChartLegendData(seriesData);
  let legendFormatter = params.costomLegendFormatter || null;
  let grid = params.grid || {
    top: "10%",
    left: "2%",
    right: "1%",
    bottom: "10%",
  };
  let subtext = params.subtext || "总数";
  let seriesRadius = params.seriesRadius || ["50%", "60%"];
  let seriesCenter = params.seriesCenter || ["25%", "50%"];
  let series_itemStyle_borderColor = params.series_itemStyle_borderColor || '#081E52';

  let seriesObj = AnunlarChartSeriesObj(seriesData, seriesRadius, seriesCenter, series_itemStyle_borderColor);

  let decorativeRings = generateDecorativeRings(seriesCenter, {
    decorativeRingInnerRadius: "72%",
    decorativeRingOuterRadius: "74%",
    highlightRingInnerRadius: "78%",
    highlightRingOuterRadius: "79%",
    highlightStartAngle: 130,
    innerRingColor: '#325280',
    highlightColor: '#2a92caff'
  });

  let id = document.getElementById(dom);
  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  let option = {
    title: {
      text: totalData,
      subtext: subtext,
      textStyle: {
        color: "#f2f2f2",
        fontSize: 16,
        fontFamily: 'YouSheBiaoTiHei',
      },
      subtextStyle: {
        fontSize: 13,
        color: ["#ff9d19"],
      },
      textAlign: 'center',
      left: '24%',//seriesCenter[0],
      top: 'middle', //"middle",
    },
    tooltip: {
      show: "item",
    },
    grid: grid,
    legend: {
      orient: "vertical",
      //right: 5,
      left: '52%',
      top: "middle",
      itemWidth: 20,
      itemHeight: 10,
      textStyle: {
        color: "#fff",
        fontSize: 12,
      },
      type: "scroll",      // 启用滚动模式
      itemGap: 5,
      data: legendData,
      formatter: function (name) {
        var res = name;
        var _item = seriesData.filter(item => item.name === res);
        var _value = _item ? _item[0].value : null;
        if (_value !== null && _value !== undefined) { res = name + ' ' + _value }
        return res;
      },
    },
    series: [...seriesObj, ...decorativeRings],
  };

  myChart.setOption(option, true);
  if (params.callback) {
    myChart.on("click", function (params) {
      return params.callback(params);
    });
  }
  window.onresize = myChart.resize;
};

//饼图--环形图--总数
const AnnularChartTotalData = (seriesData) => {
  let total = 0;
  for (var i = 0; i < seriesData.length; i++) {
    total += seriesData[i].value;
  }
  return total;
};

// 饼图--环形图--装饰环配置--单线样式
export const generateDecorativeRings = (center = ["25%", "50%"], options = {}) => {
  const {
    decorativeRingInnerRadius = "72%",
    decorativeRingOuterRadius = "74%",
    highlightRingInnerRadius = "78%",
    highlightRingOuterRadius = "79%",
    highlightStartAngle = 130,
    highlightLength = 50,
    innerRingColor = '#325280',
    highlightColor = '#2a92caff'
  } = options;

  return [
    // 第一个装饰环（内圈）
    {
      type: 'pie',
      radius: [decorativeRingInnerRadius, decorativeRingOuterRadius],
      center: center,
      silent: true,
      label: { show: false },
      labelLine: { show: false },
      data: [
        {
          value: 340, // 主线段（340度）
          itemStyle: {
            color: 'transparent',
            borderColor: innerRingColor,
            borderWidth: 1,
          }
        },
        {
          value: 20, // 缺口（20度）
          itemStyle: {
            color: 'transparent',
            borderColor: 'transparent',
          }
        }
      ],
      startAngle: 0 // 缺口位于0度位置
    },
    // 第二个装饰环（外圈）
    {
      type: 'pie',
      radius: [highlightRingInnerRadius, highlightRingOuterRadius],
      center: center,
      silent: true,
      label: { show: false },
      labelLine: { show: false },
      data: [
        {
          value: 320,
          itemStyle: {
            color: innerRingColor,
            borderColor: innerRingColor,
            borderWidth: 1,
          }
        },
        {
          value: 40,
          itemStyle: {
            color: 'transparent',
          }
        }
      ],
      startAngle: 180  // 缺口位于180度位置（与内圈错开）
    },
    // 高亮段（位于内圈装饰环上）
    {
      type: 'pie',
      radius: [decorativeRingInnerRadius, decorativeRingOuterRadius],
      center: center,
      silent: true,
      label: { show: false },
      labelLine: { show: false },
      data: [
        {
          value: highlightLength, // 高亮段长度（50度）
          itemStyle: {
            color: highlightColor,
          },
        },
        {
          value: 340,
          itemStyle: {
            color: 'transparent',
            borderColor: innerRingColor,
            borderWidth: 1,
          }
        }
      ],
      startAngle: highlightStartAngle
    }
  ];
};

// 饼图--环形图--图例数据
const AnnularChartLegendData = (seriesData) => {
  let legendData = [];
  for (var i = 0; i < seriesData.length; i++) {
    legendData.push(seriesData[i].name);
  }
  return legendData;
};
//饼图--环形图--系列对象
const AnunlarChartSeriesObj = (seriesData, seriesRadius, seriesCenter, series_itemStyle_borderColor) => {
  var data = [];
  for (var i = 0; i < seriesData.length; i++) {
    var color = _color_7[i % _color_7.length];
    data.push(
      {
        name: seriesData[i].name,
        value: seriesData[i].value,
      }

    );
  }
  var seriesObj = [
    {
      name: "",
      type: "pie",
      center: seriesCenter,
      clockWise: false,
      radius: seriesRadius,
      padAngle: 5,
      itemStyle: {
        // borderWidth: 3,
        // borderColor: series_itemStyle_borderColor
      },
      label: {
        show: false
      },
      data: data,
    },
  ];
  return seriesObj;
};
// 仪表盘
export const drawDashboardTotal = params => {
  let dom = params.dom;
  let pointerIcon = params.pointerIcon;
  let id = document.getElementById(dom);
  let myChart = echarts.getInstanceByDom(id);
  if (!myChart) {
    myChart = echarts.init(id);
  }

  const color = params.color || "#ACCDE6";
  let detailFormatter = params.detailFormatter;
  let dataValue = params.dataValue;

  // Default gradient colors (can be overridden by params)
  const gradientColors = params.gradientColors || [
    { offset: 0, color: '#9EDCFF' },   // 50%
    { offset: 1, color: '#5AC3FF' }    // 100%
  ];

  let option = {
    series: [
      {
        type: "gauge",
        center: ['50%', '50%'],
        startAngle: 180,
        endAngle: 0,
        min: 0,
        max: 100,
        splitNumber: 10,
        radius: '70%',
        itemStyle: {
          color: color
        },
        progress: {
          show: true,
          roundCap: true,
          width: 10,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: gradientColors,
              global: false
            }
          }
        },
        pointer: {
          icon: 'image://' + pointerIcon,
          length: '70%',
          width: 30,
          height: 45,
          offsetCenter: [0, '30%'],
        },
        axisLine: {
          lineStyle: {
            width: 10,
            color: [[1, '#283D70']],
          }
        },
        axisTick: {
          lineStyle: {
            width: 0,
          }
        },
        splitLine: {
          distance: -10,
          length: 14,
          lineStyle: {
            width: 1,
            color: '#999'
          }
        },
        axisLabel: {
          color: "rgba(109, 139, 172, 0.8)",
          distance: -20,
          fontSize: 12
        },
        detail: {
          valueAnimation: true,
          formatter: detailFormatter,
          color: "#AADAFF",
          offsetCenter: [0, "80%"],
          rich: {
            bold: {
              fontSize: 20,
              color: "#AADAFF",
            },
            gray: {
              fontSize: 12,
              lineHeight: 12,
              color: "#AADAFF",
            },
          }
        },
        data: [
          {
            value: dataValue
          }
        ]
      }
    ]
  };
  myChart.setOption(option);
  window.onresize = myChart.resize;
};

