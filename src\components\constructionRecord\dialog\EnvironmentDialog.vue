<!-- 危大工程-施工方案-的风险管理(查看)的-隐患管理(查看)--新增-->
<template>
  <el-dialog
    class="dialog-component"
    :visible.sync="showDialog"
    :append-to-body="true"
    height="75%"
    width="60%"
    @close="showDialog = false"
    :before-close="handleClose"
  >
    <div
      slot="title"
      class="header-title"
    >
      <span class="title-name">环境监测 </span>
    </div>
    <el-row>
      <el-form
        ref="form"
        :model="trainForm"
        label-width="100px"
      >
        <el-col v-if="false">
          <el-form-item label="监测点名称:">
            <el-select
              v-model="trainForm.name"
              placeholder="请选择"
              style="width: 250px"
            >
              <el-option
                v-for="item in monitorPointList"
                :key="item.value"
                :label="item.text"
                :value="item.value"
                style="width: 250px"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="20">
          <el-select
            v-model="trainForm.name"
            placeholder="请选择"
            style="width: 250px"
          >
            <el-option
              v-for="item in monitorPointList"
              :key="item.value"
              :label="item.text"
              :value="item.value"
              style="width: 250px"
            >
            </el-option>
          </el-select>
        </el-col> -->
        <!-- <el-col :span="5" style="margin-left: 20px;">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="buttonType"
              @click="serchClick"
              >查询</el-button
            >
          </el-col> -->
      </el-form>
    </el-row>
    <ContentCharts ref="chartOverview" />
  </el-dialog>
</template>

<script>
import ContentCharts from "./contentCharts.vue";
export default {
  components: {
    ContentCharts,
  },
  data() {
    return {
      title: "环境监测",
      showDialog: false,
      trainForm: {
        name: null
      },
      monitorPointList: [
        {
          value: 1,
          text: "北京市政2",
        },
        {
          value: 2,
          text: "房地产项目",
        },
      ],
    };
  },
  mounted() { },
  created() { },
  methods: {
    handleClose() {
      this.showDialog = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.header-title {
  border-bottom: 1px solid #4fa8d0;
}
.title-name {
  height: 0.5rem;
  margin-left: 46%;
  line-height: 0.5rem;
  color: #00deff;
  // font-size: 0.4rem;
  font-size: 20px;
  font-weight: bold;
}
// [data-v-4e21be86] .el-dialog__title {
//   margin-left: 45%;
// }
// /deep/ .el-dialog__body {
//   background: #0b3472;
// }
// /deep/ .el-date-editor.el-input,
// .el-date-editor.el-input__inner {
//   width: 250px;
// }
// /deep/ .el-dialog__title {
//   color: skyblue;
//   margin-left: 250px;
//   font-weight: 700;
// }
// /deep/ .el-dialog__header {
//   border-bottom: 1px solid blue;
//   background: #0b3472;
// }
// .content-box {
//   display: flex;
//   justify-content: center;
//   .box {
//     width: 55%;
//     min-height: 550px;
//     // background: #0b3472;
//     margin-right: 70px;
//   }
//   .el-button {
//     background: blue;
//     color: #fff;
//   }
// }
</style>
