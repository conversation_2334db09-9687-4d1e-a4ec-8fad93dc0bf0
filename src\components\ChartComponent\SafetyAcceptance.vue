<!--
 * @Description: 安全验收
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-08-01 15:16:14
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="SafetyAcceptanceRef"
      >
        <div
          id="SafetyAcceptanceChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getSafetyAcceptance } from "@/api/echrtsApi";
export default {
  components: {},
  name: "SafetyAcceptance",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "SafetyAcceptanceChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.safetyAcceptanceTotal"),
        seriesCenter: ["25%", "58%"],
        richNameWidth: 40,
        noTooltipShow: true, //不显示
        itemStyleEmphasis: {
          label: {
            show: true,
            // position: 'center',
            x: "20%",
            y: "10%",
            textStyle: {
              rich: {
                numText: {
                  color: "#fff",
                  fontSize: 13,
                  width: 30,
                  textAlign: "center",
                },
                text: {
                  color: "#fff",
                  fontSize: 13,
                  padding: [0, 0, 10, 0],
                  width: 30,
                  textAlign: "center",
                },
              },
            },
            formatter: (params) => {
              return `{text| ${params.name}${this.$t(
                "customization.cumulative"
              )}：${params.value}}\n{numText|${this.$t("Proportion")}： ${params.percent || 0
                }%}`;
            },
          },
        },
        legendTop: "10%",
        costomLegendFormatter: function (name) {
          return name;
        }
      },
      totalCount: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.SafetyAcceptanceRef.offsetWidth;
      this.barHeight = this.$refs.SafetyAcceptanceRef.offsetHeight;
    },
    getBarData() {
      getSafetyAcceptance()
        .then((res) => {
          const {
            statusCode,
            data: { dataList, totalCount },
          } = res.data;
          if (statusCode == 200) {
            this.setEcharts(dataList);
            this.totalCount = totalCount;
          }
        })
        .catch(() => { });
    },
    setEcharts(val) {
      console.log('验收=', val);
      let dataList = val;
      let legendFormatter = (name) => {
        let newName = name.length > 6 ? name.slice(0, 6) + "..." : name;
        return "{name|" + newName + "}";
      };
      this.pieParams.legendFormatter = legendFormatter;
      this.pieParams.data = dataList.map((item) => {
        switch (item.name) {
          case "生活区管理":
            item.name = this.$t("customization.livingAreaManagement");
            break;
          case "脚手架":
            item.name = this.$t("customization.cli");
            break;
          case "模板支撑体系":
            item.name = this.$t("customization.templateSys");
            break;
          case "安全防护":
            item.name = this.$t("customization.safeDefend");
            break;
          case "临时用电":
            item.name = this.$t("customization.tempElec");
            break;
          case "塔式起重机、起重吊装":
            item.name = this.$t("customization.towerAndweight");
            break;
          case "机械安全":
            item.name = this.$t("customization.machineSafe");
            break;
        }
        return item;
      });

      drawAnnularChart(this.pieParams);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
