<template>
  <div class="chart-overview-wrap">
    <div class="chart-wrap">
      <!-- pm2.5 -->
      <!-- <div class="title">PM2.5</div> -->
      <div
        v-show="1 || showPM25"
        id="pieCitle1"
        class="pie-chart"
      ></div>
      <!-- showPM10 -->
      <!-- <div class="wrap" v-show="1 || showNoise"> -->
      <!-- <div class="title">PM10</div> -->
      <div
        v-show="1 || showPM10"
        id="pieCitle2"
        class="pie-chart"
      ></div>
      <!-- </div> -->
      <!-- 噪音 -->
      <!-- <div class="wrap" v-show="1 || showNoise"> -->
      <!-- <div class="title">噪音</div> -->
      <div
        v-show="1 || showNoise"
        id="pieCitle3"
        class="pie-chart"
      ></div>
      <!-- </div> -->
    </div>
    <div class="chart-wrap">
      <div
        v-show="1 || showPM25"
        id="pieCharts1"
        class="pie-chart"
      ></div>
      <div
        v-show="1 || showPM10"
        id="pieCharts2"
        class="pie-chart"
      ></div>
      <div
        v-show="1 || showNoise"
        id="pieCharts3"
        class="pie-chart"
      ></div>
    </div>
    <div class="line-chart-wrap">
      <div
        v-show="1 || showWind"
        id="lineCharts1"
        class="pie-chart"
      ></div>
      <div
        v-show="1 || showTemp"
        id="lineCharts2"
        class="pie-chart"
      ></div>
      <div
        v-show="1 || showPM25 || showPM10"
        id="lineCharts3"
        class="pie-chart"
      ></div>
    </div>
  </div>
</template>
<script>
import { drawPie, drawLine } from "@/util/echarts";
import { drawRing3 } from "../Echarts/echarts";
import { getPieHollow, getPieCicle, getOverviewLine } from "@/api/chart";
import { getCOnfig } from "@/api/parameterDialog";
export default {
  data: () => ({
    barWidth: 600,
    barHeight: 500,
    barShow: true,
    // 折线图
    pipParams1: {
      dom: "lineCharts1",
      nameTitle: "风速",
      xAxisData: [],
      isMoreLine: true,
      seriesData: [
        {
          name: "风速",
          data: [],
        },
      ],
    },
    pipParams2: {
      dom: "lineCharts2",
      nameTitle: "气温",
      xAxisData: [],
      isMoreLine: true,
      seriesData: [
        {
          name: "气温",
          data: [],
        },
      ],
    },
    pipParams3: {
      dom: "lineCharts3",
      nameTitle: "最近24小时污染浓度",
      xAxisData: [],
      isMoreLine: true,
      seriesData: [],
    },
    // 实心饼图
    pieParams1: {
      dom: "pieCharts1",
      nameTitle: "",
      data: [2, 2, 3],
      colorArray: ["#38A1F7", "#59CECE", "#5ECE7D"],
    },
    pieParams2: {
      dom: "pieCharts2",
      nameTitle: "",
      data: [2, 2, 3],
      colorArray: ["#38A1F7", "#59CECE", "#5ECE7D"],
    },
    pieParams3: {
      dom: "pieCharts3",
      nameTitle: "",
      data: [2, 2, 3],
      colorArray: ["#38A1F7", "#59CECE", "#5ECE7D"],
    },
    // 空心饼图
    pieParamsCitle1: {
      dom: "pieCitle1",
      nameTitle: "",
      radius: ["50%", "35%"],
      total: 0,
      data: [
        {
          value: 0,
          name: "pm",
        },
        {
          value: 0,
          name: "pm2.5",
        },
      ],
      colorArray: ["#F0EF68"],
    },
    pieParamsCitle2: {
      dom: "pieCitle2",
      nameTitle: "PM10",
      radius: ["50%", "35%"],
      total: 0,
      data: [
        {
          value: 0,
          name: "",
        },
      ],
      colorArray: ["#F0EF68"],
    },
    pieParamsCitle3: {
      dom: "pieCitle3",
      nameTitle: "噪音",
      radius: ["50%", "35%"],
      total: 0,
      data: [
        {
          value: 0,
          name: "",
        },
      ],
      colorArray: ["#F0EF68"],
    },
    projectId: "",
    pieHollowData: null, //空心饼图
    pieCicleData: null, // 实心饼图
    showPM25: false,
    showPM10: false,
    showNoise: false,
    showTemp: false,
    showWind: false,
  }),
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    // console.log(this.projectId);
  },
  mounted() {
    // 首页无数据
    this.getCOnfig();
  },
  methods: {
    // initEchart() {
    //   const echarsWidth = document.getElementsByClassName('box')[0].clientWidth
    //   const echarsHeight = document.getElementsByClassName('box')[0].clientHeight
    //   this.barWidth = echarsWidth
    //   this.barHeight = echarsHeight
    //   this.$nextTick(() => {
    //     drawLine(this.pipParams2)
    //   })
    // },
    // 空心饼图
    getPieHollow() {
      getPieHollow(this.projectId).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          this.pieHollowData = result.data;
          this.pieParamsCitle1.data[0].value = result.data.pM25
            ? result.data.pM25
            : 0;
          this.pieParamsCitle2.data[0].value = result.data.pM10
            ? result.data.pM10
            : 0;
          this.pieParamsCitle3.data[0].value = result.data.noise
            ? result.data.noise
            : 0;
          this.pieParamsCitle1.total = result.data.pM25;
          this.pieParamsCitle2.total = result.data.pM10;
          this.pieParamsCitle3.total = result.data.noise;
          drawRing3(this.pieParamsCitle1);
          drawRing3(this.pieParamsCitle2);
          drawRing3(this.pieParamsCitle3);
        }
      });
    },
    // 实心图
    getPieCicle(data) {
      // console.log(data);
      let params = {
        projectId: this.projectId,
        monthDate: data.form,
      };
      getPieCicle(params).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          result.data.chartPM25Data.map((item) => {
            item.name = item.strName;
            item.value = item.strValue;
          });
          result.data.chartPM10Data.map((item) => {
            item.name = item.strName;
            item.value = item.strValue;
          });
          result.data.chartNoiseData.map((item) => {
            item.name = item.strName;
            item.value = item.strValue;
          });
          this.pieParams1.data = result.data.chartPM25Data.length
            ? result.data.chartPM25Data
            : [0];
          this.pieParams2.data = result.data.chartPM10Data.length
            ? result.data.chartPM10Data
            : [0];
          this.pieParams3.data = result.data.chartNoiseData.length
            ? result.data.chartNoiseData
            : [0];
          this.pieHollowData = result.data;
          drawPie(this.pieParams1);
          drawPie(this.pieParams2);
          drawPie(this.pieParams3);
        }
      });
    },
    getOverviewLine() {
      this.pipParams3.seriesData = [];
      getOverviewLine(this.projectId).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          this.pipParams1.xAxisData = result.data.chartTimeData;
          this.pipParams2.xAxisData = result.data.chartTimeData;
          this.pipParams3.xAxisData = result.data.chartTimeData;
          this.pipParams1.seriesData[0].data = result.data.chartWindSpeedData;
          this.pipParams2.seriesData[0].data = result.data.chartTemperatureData;
          this.pipParams3.seriesData.push({
            name: "PM2.5",
            data: result.data.chartPM25Data,
          });
          this.pipParams3.seriesData.push({
            name: "PM10",
            data: result.data.chartPM10Data,
          });
          drawLine(this.pipParams1);
          drawLine(this.pipParams2);
          drawLine(this.pipParams3);
        }
      });
    },
    // 获取参数配置
    getCOnfig() {
      this.checkedLis = [];
      getCOnfig(this.projectId).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          // TODO 暂时这样 需要修改
          if (result.data.pM25) {
            this.showPM25 = true;
          } else {
            this.showPM25 = false;
          }
          if (result.data.pM10) {
            this.showPM10 = true;
          } else {
            this.showPM10 = false;
          }
          if (result.data.noise) {
            this.showNoise = true;
          } else {
            this.showNoise = false;
          }
          if (result.data.temperature) {
            this.showTemp = true;
          } else {
            this.showTemp = false;
          }
          if (result.data.windSpeed) {
            this.showWind = true;
          } else {
            this.showWind = false;
          }
          this.getPieHollow();
          this.getOverviewLine();
          this.getPieCicle({
            form: new Date().format(),
          });
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.chart-overview-wrap {
  background: rgba(9, 35, 92, 0.6);
  border: 1px solid #365f7d;
  .chart-wrap {
    width: 100%;
    display: flex;
    // margin-top: 60px;
    .pie-chart {
      width: 50%;
      height: 330px;
      margin-top: 10px;
    }
  }
  .title {
    color: #fff;
    font-size: 20px;
    font-weight: 500;
    position: absolute;
    top: 40px;
    left: 150px;
  }
  .pie-chart {
    width: 50%;
    height: 330px;
    margin-top: 10px;
  }
  .line-chart-wrap {
    display: flex;
    margin-top: 60px;
  }
}

.content-echarts {
  background: rgba(9, 35, 92, 0.6);
  border: 1px solid #365f7d;

  .content-box {
    display: flex;

    .box {
      width: 29%;
      min-height: 500px;
    }

    .el-button {
      background: blue;
      color: #fff;
    }
  }
}
</style>
