/*
 * @Description:
 * @Author:
 * @Date: 2022-12-26 13:39:29
 * @LastEditTime: 2022-12-28 15:26:04
 * @LastEditors: lihanbing
 * @Usage:
 */
const path = require("path");
module.exports = {
  env: {
    browser: true,
    es2021: true
  },
  extends: [
    "eslint:recommended",
    "plugin:vue/essential",
    "plugin:import/errors",
    "plugin:import/warnings"
  ],
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module"
  },
  plugins: ["vue", "import"],
  rules: {
    "space-before-function-paren": 0,
    "no-undef": 0, // 禁用未声明的变量
    "no-debugger": 2, // 禁用debugger
    "vue/require-valid-default-prop": "error", // props默认值检查
    "vue/no-v-model-argument": "off", // 禁止v-model在自定义组件中添加参数
    // eqeqeq: 2, // 使用 === 和 !==
    eqeqeq: ["off"], // 使用 === 和 !==
    "no-sparse-arrays": 2, // 禁用稀疏数组
    "no-dupe-args": 2, // 禁止function中定义重名参数
    "no-dupe-keys": 2, // 禁止对象字面量中出现重复的 key
    "no-empty-character-class": 2, // 禁止在正则表达式中使用空字符集
    "no-invalid-regexp": 2, // 禁止RegExp构造函数中存在无效的正则表达式字符串
    "no-func-assign": 2, // 禁止对function声明重新赋值
    "no-unreachable": 2, // 禁止在return、throw、continue、break语句之后出现不可达代码
    "no-unexpected-multiline": 2, // 禁止出现令人困惑的多行表达式
    "key-spacing": [
      1,
      {
        beforeColon: false,
        afterColon: true
      }
    ], // 强制在对象字面量的属性中键和值之间使用一致的间距
    "no-unused-expressions": [
      2,
      {
        allowShortCircuit: true,
        allowTernary: true
      }
    ], // 禁止出现未使用过的表达式
    "no-void": 2, // 禁止void操作符
    "no-duplicate-imports": 1, // 禁止重复模块导入
    "no-extra-semi": 2, // 禁止不必要的分号
    "no-undef-init": 2, // 禁止将变量初始化为undefined
    "no-undefined": 2, // 禁止将undefined作为标识符
    "no-unused-vars": [2, { vars: "all", args: "after-used" }], // 禁止出现未使用过的变量
    "comma-style": [2, "last"], // 强制使用一致的逗号风格
    "new-cap": [2, { newIsCap: true, capIsNew: false }], // 要求构造函数首字母大写
    "no-inline-comments": 0, // 禁止在代码后使用内联注释
    "no-multiple-empty-lines": [2, { max: 2 }], // 禁止出现多行空行
    "no-trailing-spaces": 2, // 禁用行尾空格
    // "space-before-function-paren": [2, { anonymous: "always", named: "never" }], //强制在function的左括号之前使用一致的空格
    "max-depth": 0, // 强制可嵌套的块的最大深度
    "max-params": ["error", 4], // 强制函数定义中最多允许的参数数量
    "max-statements": ["error", 40], // 指定一个函数中所允许允许的最大语句数量
    "vue/require-prop-types": "error", // props属性指定类型
    "vue/multi-word-component-names": "off", // 关闭组件名必须多单词的要求
  },
  settings: {
    "import/resolver": {
      alias: {
        map: [["@", path.resolve(__dirname, "src")]],
        extensions: [".ts", ".js", ".jsx", ".json", ".vue"]
      }
    }
  }
};
