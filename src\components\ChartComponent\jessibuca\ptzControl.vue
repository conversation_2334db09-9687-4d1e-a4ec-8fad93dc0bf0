<!--
 * @Description: 云台控制
 * @Author: 
 * @Date: 2024-05-20 10:56:52
 * @LastEditTime: 2024-05-24 10:04:01
 * @LastEditors: dongqi<PERSON>qian
 * @Usage: 
-->
<template>
  <div>
    <div style="display: flex; justify-content: left;">
      <div class="control-wrapper">
        <div
          class="control-btn control-top"
          @mousedown="ptzCamera('up')"
          @mouseup="ptzCamera('stop')"
        >
          <i class="el-icon-caret-top"></i>
          <!-- <div class="control-inner-btn control-inner"></div> -->
        </div>
        <div
          class="control-btn control-left"
          @mousedown="ptzCamera('left')"
          @mouseup="ptzCamera('stop')"
        >
          <i class="el-icon-caret-left"></i>
          <!-- <div class="control-inner-btn control-inner"></div> -->
        </div>
        <div
          class="control-btn control-bottom"
          @mousedown="ptzCamera('down')"
          @mouseup="ptzCamera('stop')"
        >
          <i class="el-icon-caret-bottom"></i>
          <!-- <div class="control-inner-btn control-inner"></div> -->
        </div>
        <div
          class="control-btn control-right"
          @mousedown="ptzCamera('right')"
          @mouseup="ptzCamera('stop')"
        >
          <i class="el-icon-caret-right"></i>
          <!-- <div class="control-inner-btn control-inner"></div> -->
        </div>
        <div class="control-round">
          <div><i class="fa fa-pause-circle"></i></div>
        </div>

        <div
          class="zoomWrap"
          style="height: 6.25rem;"
        >
          <div
            @mousedown="ptzCamera('zoomin')"
            @mouseup="ptzCamera('stop')"
          ><i
              class="el-icon-zoom-in control-zoom-btn"
              style="font-size: 1.875rem;"
            ></i></div>
          <div
            style="font-size: 1.875rem;"
            @mousedown="ptzCamera('zoomout')"
            @mouseup="ptzCamera('stop')"
          ><i class="el-icon-zoom-out control-zoom-btn"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import { ptzControl } from './api'

export default {
  name: 'ptzControl',
  props: {
    videoCfg: {
      type: Object,
      default: () => { }
    }
  },
  computed: {},
  created() {

  },
  data() {
    return {
      controSpeed: 30,
    };
  },
  methods: {
    ptzCamera: function (command) {
      const params = {
        ...this.videoCfg,
        command,
        horizonSpeed: 90,
        verticalSpeed: 90,
        zoomSpeed: this.controSpeed,
      }
      ptzControl(params).then(res => {
        if (res.data.code != 0) {
          this.$message.error(res.data.msg || '操作失败，请稍后再试')
        }
      })
    },
  }
};
</script>

<style>
.control-wrapper {
  position: relative;
  width: 6.25rem;
  height: 6.25rem;
  max-width: 6.25rem;
  max-height: 6.25rem;
  border-radius: 100%;
  margin-top: 1.5rem;
  margin-left: 0.5rem;
  float: left;
  background: #fff;
}

.control-panel {
  position: relative;
  top: 0;
  left: 5rem;
  height: 11rem;
  max-height: 11rem;
}

.control-btn {
  display: flex;
  justify-content: center;
  position: absolute;
  width: 44%;
  height: 44%;
  border-radius: 5px;
  /* border: 1px solid #78aee4; */
  box-sizing: border-box;
  transition: all 0.3s linear;
}

.control-btn:hover {
  cursor: pointer;
}
.control-btn:active {
  background: rgba(4, 151, 248, 0.3);
}

.control-btn i {
  font-size: 20px;
  color: #1890ff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.control-btn i:hover {
  cursor: pointer;
}

.control-zoom-btn {
  color: #1890ff;
}
.control-zoom-btn:hover {
  cursor: pointer;
}
.control-zoom-btn:active {
  background: rgba(4, 151, 248, 0.3);
}

.control-round {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30%;
  height: 30%;
  background: #1890ff;
  transform: translate(-50%, -50%);
  border-radius: 100%;
}

.control-round-inner {
  position: absolute;
  left: 13%;
  top: 13%;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 70%;
  height: 70%;
  font-size: 40px;
  color: #78aee4;
  border: 1px solid #78aee4;
  border-radius: 100%;
  transition: all 0.3s linear;
}

.control-inner-btn {
  position: absolute;
  width: 60%;
  height: 60%;
  background: #fafafa;
}

.control-top {
  top: -8%;
  left: 27%;
  transform: rotate(-45deg);
  border-radius: 5px 100% 5px 0;
}

.control-top i {
  transform: rotate(45deg);
  border-radius: 5px 100% 5px 0;
}

.control-top .control-inner {
  left: -1px;
  bottom: 0;
  border-top: 1px solid #78aee4;
  border-right: 1px solid #78aee4;
  border-radius: 0 100% 0 0;
}

.control-top .fa {
  transform: rotate(45deg) translateY(-7px);
}

.control-left {
  top: 27%;
  left: -8%;
  transform: rotate(45deg);
  border-radius: 5px 0 5px 100%;
}

.control-left i {
  transform: rotate(-45deg);
}

.control-left .control-inner {
  right: -1px;
  top: -1px;
  border-bottom: 1px solid #78aee4;
  border-left: 1px solid #78aee4;
  border-radius: 0 0 0 100%;
}

.control-left .fa {
  transform: rotate(-45deg) translateX(-7px);
}

.control-right {
  top: 27%;
  right: -8%;
  transform: rotate(45deg);
  border-radius: 5px 100% 5px 0;
}

.control-right i {
  transform: rotate(-45deg);
}

.control-right .control-inner {
  left: -1px;
  bottom: -1px;
  border-top: 1px solid #78aee4;
  border-right: 1px solid #78aee4;
  border-radius: 0 100% 0 0;
}

.control-right .fa {
  transform: rotate(-45deg) translateX(7px);
}

.control-bottom {
  left: 27%;
  bottom: -8%;
  transform: rotate(45deg);
  border-radius: 0 5px 100% 5px;
}

.control-bottom i {
  transform: rotate(-45deg);
}

.control-bottom .control-inner {
  top: -1px;
  left: -1px;
  border-bottom: 1px solid #78aee4;
  border-right: 1px solid #78aee4;
  border-radius: 0 0 100% 0;
}

.control-bottom .fa {
  transform: rotate(-45deg) translateY(7px);
}

.zoomWrap {
  background: #fff;
  width: 30px;
  position: absolute;
  left: 118px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 5px 0;
  box-sizing: border-box;
}

.trank {
  width: 80%;
  height: 180px;
  text-align: left;
  padding: 0 10%;
  overflow: auto;
}

.trankInfo {
  width: 80%;
  padding: 0 10%;
}
</style>
