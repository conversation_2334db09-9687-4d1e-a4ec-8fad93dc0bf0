<!-- 
  视频播放器
  基于vue-aliplayer-v2定制，增加方向、缩放、预设点功能
-->
<template>
  <div class="player_wrap" id="player_wrap">
    <vue-aliplayer-v2
      :source="source"
      :options="options"
      style="width: 100%; height: 100%"
    />
    <div class="presetList" v-if="presetList.length > 0">
      预设点管理
      <el-tag
        v-for="(tag, idx) in presetList"
        :key="tag.id"
        closable
        @close="onDelPreset(idx)"
        @click="onClickPreset(idx)"
        size="small"
        :class="[selectPresetIdx === idx ? 'active' : '']"
      >
        {{ tag.pointName }}
      </el-tag>
    </div>

    <!-- 控制面板 -->
    <!-- <div v-if="panelStatus === 1" class="panelWrap">
      <div class="panel">
        <div class="direction clearfix">
          <div
            :class="['sector', mouseDownEle === 0 ? 'active' : '']"
            @mousedown="onMousedownPTZ(0)"
            @mouseup="onMouseUpPTZ(0)"
          >
            <div class="upA" />
          </div>
          <div
            :class="['sector', mouseDownEle === 3 ? 'active' : '']"
            @mousedown="onMousedownPTZ(3)"
            @mouseup="onMouseUpPTZ(3)"
          >
            <div class="rightA" />
          </div>
          <div
            :class="['sector', mouseDownEle === 2 ? 'active' : '']"
            @mousedown="onMousedownPTZ(2)"
            @mouseup="onMouseUpPTZ(2)"
          >
            <div class="leftA" />
          </div>
          <div
            :class="['sector', mouseDownEle === 1 ? 'active' : '']"
            @mousedown="onMousedownPTZ(1)"
            @mouseup="onMouseUpPTZ(1)"
          >
            <div class="downA" />
          </div>
        </div>
        <div class="scale">
          <div
            :class="[mouseDownEle === 9 ? 'active' : '']"
            @mousedown="onMousedownPTZ(9)"
            @mouseup="onMouseUpPTZ(9)"
          >
            -
          </div>
          <div
            :class="[mouseDownEle === 8 ? 'active' : '']"
            @mousedown="onMousedownPTZ(8)"
            @mouseup="onMouseUpPTZ(8)"
          >
            +
          </div>
        </div>
      </div>
      <div
        class="preset"
        v-if="deviceCapacity.ptz_preset === '1'"
        @click="onClickAddPresetBtn"
      >
        设置预设点
      </div>
    </div> -->

    <el-dialog
      class="presetDialog"
      title="设置预设点"
      :visible.sync="dialogVisible"
      width="30%"
      center
    >
      <div class="inptW">
        <label>预设点名称</label>
        <el-input placeholder="请输入预设点名称" v-model="presetName">
        </el-input>
      </div>
      <div class="footer">
        <el-button type="primary" @click="onAddPreset">确 定</el-button>
        <el-button @click="onClosePresetDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import VueAliplayerV2 from "vue-aliplayer-v2";

// import { videoControlPTZ, videoControlMove } from "./api.js";
import DirectionControl from "./DirectionControl";

import {
  addVideoPreset,
  getVideoPresetList,
  getVideoDetail,
  delVideoPreset,
  delMileStoneCapture,
} from "../../../api/VideoSurveillance";

import JkyUtils from "jky-utils";
const { videoControlPTZ, videoControlMove, getDeviceCapacity } =
  JkyUtils.videoPTZ;

export default {
  name: "Player",
  data() {
    return {
      // deviceCapacity: {},
      // panelStatus: 0,
      mouseDownEle: null,
      options: {
        autoplay: false,
        isLive: true, // 切换为直播流的时候必填
        format: "m3u8", // 切换为直播流的时候必填
        controlBarVisibility: "always",
        components: [
          {
            name: "DirectionControl",
            type: DirectionControl,
            args: [
              {
                onClick: (val) => {
                  this.togglePanel(val);
                },
                onMouseUpPTZ: this.onMouseUpPTZ,
                onMousedownPTZ: this.onMousedownPTZ,
                onClickAddPresetBtn: this.onClickAddPresetBtn,
              },
            ],
          },
        ],
      },

      dialogVisible: false,
      presetName: "",
      presetList: [],
      selectPresetIdx: null,
    };
  },
  props: {
    videoId: {
      type: [String, Number],
      retuired: true,
    },
    source: {
      type: String,
      required: true,
    },
  },
  components: {
    VueAliplayerV2,
  },
  watch: {
    videoId(val, oldVal) {
      if (oldVal && val !== oldVal) {
        // videoId发生变化，重新获取video详情，刷新assetsToken
        this.initDatas(val);
      }
    },
  },
  created() {},
  mounted() {
    this.initDatas(this.videoId);
  },
  methods: {
    // 初始化页面数据
    initDatas(videoId) {
      this.dialogVisible = false;
      this.presetName = "";
      this.selectPresetIdx = null;
      this.mouseDownEle = null;

      this.getVideoDetail(videoId);
      this.getPresetList(videoId);
    },

    // 初始化视频基本信息
    async getVideoDetail(videoId) {
      if (!videoId) return;

      const res = await getVideoDetail(videoId);
      const data = res.data || {};

      this.videoInfo = data.data || {};

      this.getDeviceCapacity();
    },

    // 查询设备能力集
    // https://open.ys7.com/help/77
    async getDeviceCapacity() {
      const cfg = this.getVideoCfg();
      let res = await getDeviceCapacity(cfg);
      res = res.data || {};

      const data = res.data || {};

      // this.deviceCapacity = data;

      if (!data.ptz_preset || data.ptz_preset !== "1") {
        const player_wrap = document.getElementById("player_wrap");
        const presetBtn = player_wrap.getElementsByClassName("presetBtn")[0];
        presetBtn.style.display = "none";
      }
    },

    // 刷新萤石accessToken
    resetAccessToken() {
      this.getVideoDetail(this.videoInfo.id);
    },

    // // 切换控制盘显示状态
    // togglePanel(status) {
    //   if (status === undefined) {
    //     status = this.panelStatus === 0 ? 1 : 0;
    //   }
    //   this.panelStatus = status;
    // },

    // 获取萤石接口公共参数
    getVideoCfg() {
      const { deviceSerial, channelNo, token } = this.videoInfo || {};
      return {
        deviceSerial,
        channelNo,
        accessToken: token,
      };
    },

    // 根据萤石接口返回的code码确认是否需要刷新token
    fetchYSAPIAfter(data = {}) {
      if (data.code === "200") return;

      if (data.code === "10002") {
        this.$message.error("操作失败，请稍后再试");
        return this.resetAccessToken();
      }

      this.$message.error(data.msg || "操作失败");
    },

    async onMousedownPTZ(direction) {
      this.mouseDownEle = direction;

      const cfg = this.getVideoCfg();
      let res = await videoControlPTZ("start", { ...cfg, speed: 1, direction });

      this.selectPresetIdx = null;

      res = res.data;
      this.fetchYSAPIAfter(res);
    },

    async onMouseUpPTZ(direction) {
      this.mouseDownEle = null;

      const cfg = this.getVideoCfg();
      let res = await videoControlPTZ("stop", { ...cfg, direction });

      this.selectPresetIdx = null;

      res = res.data;
      this.fetchYSAPIAfter(res);
    },

    // 点击新增预设点按钮
    onClickAddPresetBtn() {
      if (this.dialogVisible) {
        this.dialogVisible = false;
        return;
      }

      if (this.presetList.length && this.presetList.length >= 3) {
        return this.$message.error("最多可设置三个预设点");
      }
      this.presetName = "";
      this.dialogVisible = true;
    },

    // 获取当前视频预设点列表
    async getPresetList(videoId) {
      if (!videoId) return;

      let res = await getVideoPresetList(videoId);
      res = res.data || {};
      if (res.statusCode !== 200) return;

      const data = res.data || [];
      const curInfo = data[0] || {};
      this.presetList = curInfo.points || [];
    },

    // 新增预设点
    async onAddPreset() {
      if (!this.presetName) {
        return this.$message("请输入预设点名称");
      }

      const userInfo = getStore({ name: "userInfo" });

      let res = await addVideoPreset({
        infoId: this.videoInfo.id,
        userName: userInfo.account,
        pointName: this.presetName,
      });

      res = res.data;
      if (res.statusCode !== 200) {
        return this.$message.error(res.errors || "操作失败");
      }

      // 刷新预设点列表
      this.getPresetList(this.videoInfo.id);

      this.onClosePresetDialog();
    },

    onClosePresetDialog() {
      this.dialogVisible = false;
      this.presetName = "";
    },

    // 删除预设点确认框
    onDelPreset(idx) {
      const that = this;
      this.$confirm("是否确认删除预设点，删除后不可恢复", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(async () => {
          const pointInfo = that.presetList[idx];
          const { id } = pointInfo;

          let res = await delVideoPreset(id);
          res = res.data;
          if (res.statusCode !== 200) {
            return that.$message.error("操作失败，请稍后再试");
          }

          that.presetList.splice(idx, 1);
          if (that.selectPresetIdx === idx) {
            that.selectPresetIdx = null;
          }

          const _info = {
            videoInfoId: that.videoId,
            pointId: id,
          };
          // 删除视频预设点后，需要删除施工进度中相关预设点
          delMileStoneCapture(_info);

          that.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch((e) => {
          console.log(e);
        });
    },

    // 点击预设点
    async onClickPreset(idx) {
      const info = this.presetList[idx];
      this.selectPresetIdx = idx;

      const cfg = this.getVideoCfg();
      const res = await videoControlMove({ ...cfg, index: info.pointKey });

      this.fetchYSAPIAfter(res.data);
    },
  },
};
</script>

<style lang="scss" scoped>
.player_wrap {
  width: 100%;
  height: 100%;
  position: relative;
}

.presetList {
  display: flex;
  margin-top: 10px;
}

>>> .el-tag {
  background: transparent;
  color: #fff;
  margin-left: 10px;
  cursor: pointer;
  &.active {
    background: rgba(47, 101, 237, 1);
    border-color: rgba(47, 101, 237, 1);
    color: #fff;
  }
}
</style>
<style lang="scss">
$arrow-color: rgba(0, 0, 0, 0.8);
$sector-border: 1px solid rgba(130, 130, 130, 0.8);
$arrow-border: 2px solid $arrow-color;
.panelWrap {
  position: absolute;
  right: 10px;
  top: 50%;
  z-index: 9999;
}

.panel {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: $sector-border;

  .direction {
    transform: rotate(45deg);
  }

  .sector {
    width: 60px;
    height: 60px;
    float: left;
    box-sizing: border-box;
    cursor: pointer;

    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);

    &.active {
      opacity: 0.8;
    }

    &:nth-of-type(1) {
      border-right: $sector-border;
      border-bottom: $sector-border;
    }
    &:nth-of-type(2) {
      border-bottom: $sector-border;
    }
    &:nth-of-type(3) {
      border-right: $sector-border;
    }
  }

  .sector > div {
    width: 15px;
    height: 15px;
  }

  .upA {
    border-top: $arrow-border;
    border-left: $arrow-border;
  }
  .downA {
    border-right: $arrow-border;
    border-bottom: $arrow-border;
  }

  .leftA {
    border-bottom: $arrow-border;
    border-left: $arrow-border;
  }
  .rightA {
    border-right: $arrow-border;
    border-top: $arrow-border;
  }

  .scale {
    position: absolute;
    top: 34px;
    left: 34px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    border: 1px solid rgba(0, 0, 0, 0.2);
    cursor: pointer;

    & > div {
      width: 50%;
      text-align: center;
      color: $arrow-color;
      box-sizing: border-box;
      font-size: 32px;

      display: flex;
      align-items: center;
      justify-content: center;
      background: #e4e7ed;

      &.active {
        background: #fff;
      }

      &:first-child {
        border-right: 1px solid rgba(0, 0, 0, 0.2);
        padding-bottom: 3px;
      }

      &:last-child {
        border-left: 1px solid rgba(0, 0, 0, 0.2);
      }
    }
  }
}

.presetBtn {
  width: 120px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  border-radius: 6px;
  cursor: pointer;
  color: $arrow-color;
  margin-top: 6px;
}

.presetDialog {
  .inptW {
    display: flex;
    label {
      width: 100px;
      font-size: 15px;
      line-height: 42px;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
  }
}

.clearfix:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.jky_directionControl {
  width: 20px;
  height: 20px;
  float: right;
  background-image: url("../../../assets/fullscreen.png");
  background-size: cover;
  margin-right: 8px;
  margin-top: 14px;
  cursor: pointer;
  transition: 0.5s;
  &:hover {
    transform: scale(1.2);
  }
}
</style>
<style type="text/css">
video {
  object-fit: fill;
}
</style>
