function ShowJDDChart(data) {

    var echartleft3 = echarts.init($(".clgl")[0]);
   
    var count_num = 0;
    for (var m in data) {
        count_num = count_num + data[m]['value'];
    }

    var color3 = ['#ffff00', '#cc3d73', '#343399', '#6463a0', '#f6931c', '#70b917', '#0080ff', '#1150ff'];
    var option3 = {
        title: {
            text: '',
            subtext: '',
            x: 'center',
            top: '30',
            textStyle: {
                color: "#fff",
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: "{b} : {c}({d}%)"
        },
        legend: {
            type: 'scroll',
            orient: 'vertical',
            right: 10,
            top: 10,
            bottom: 10,
            data: data,
            textStyle: {
                color: '#fff'
            }
        },
        series: [{
            type: 'pie',
            radius: '85%',
            center: ['35%', '50%'],
            label: {
                show: false
            },
            data: data,
            itemStyle: {
                emphasis: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            },
            color: color3
        }]
    };

    echartleft3.setOption(option3);
}