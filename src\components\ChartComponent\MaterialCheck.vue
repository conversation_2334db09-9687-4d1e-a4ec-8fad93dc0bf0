<!--
 * @Description: 物资验收
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-24 17:53:25
 * @LastEditors: dong<PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div class="box" ref="MaterialCheckRef">
        <div
          id="MaterialCheckChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawCustomBar } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getMaterialCheck } from "@/api/echrtsApi";
export default {
  components: {},
  name: "MaterialCheck",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      lineParams: {
        dom: "MaterialCheckChart",
        xAxisData: [],
        seriesData: [],
        boundaryGap: true,
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        yminInterval: 1,
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.MaterialCheckRef.offsetWidth;
      this.barHeight = this.$refs.MaterialCheckRef.offsetHeight;
    },
    getData() {
      getMaterialCheck(this.projectId)
        .then((res) => {
          let result = res.data;
          const {
            data: { names, totalCount, witnessCount },
            statusCode,
          } = result;
          if (statusCode == 200) {
            // let xAxisData = [];
            let seriesData = [
              {
                type: "bar",
                ...totalCount,
                itemStyle: {
                  normal: {
                    color: "#4e81e4",
                  },
                },
                label: {
                  normal: {
                    show: true,
                    position: "top",
                    textStyle: {
                      fontSize: 10,
                    },
                    formatter: function (num) {
                      return num.value > 0 ? num.value : "";
                    },
                  },
                },
              },
              {
                type: "bar",
                ...witnessCount,
                itemStyle: {
                  normal: {
                    color: "#4ebf9d",
                  },
                },
                label: {
                  normal: {
                    show: true,
                    position: "top",
                    textStyle: {
                      fontSize: 10,
                    },
                    formatter: function (num) {
                      return num.value > 0 ? num.value : "";
                    },
                  },
                },
              },
            ];

            this.lineParams.xAxisData = names;
            console.log("names 6666", names);
            this.lineParams.seriesData = seriesData.map((item) => {
              switch (item.name) {
                case "物资验收的次数":
                  item.name = this.$t("customization.materialNumer");
                  break;
                case "需要见证的次数":
                  item.name = this.$t("customization.witnessNumber");
                  break;
              }
              return item;
            });
           
            drawCustomBar(this.lineParams);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
