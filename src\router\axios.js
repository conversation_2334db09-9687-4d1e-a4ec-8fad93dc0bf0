/*
 * @Description:
 * @Author:
 * @Date: 2021-12-06 16:15:37
 * @LastEditTime: 2023-12-25 17:12:36
 * @LastEditors: Jky200guo guo<PERSON>@jiankeyan.com
 * @Usage:
 */
import Axios from "axios";
import qs from "qs";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css";
import { Message } from "element-ui";
import { serialize } from "@/util/util";
import errorCode from "@/util/errorCode";
var env = process.env.NODE_ENV;
Axios.defaults.timeout = 30000;
// 返回其他状态吗
Axios.defaults.validateStatus = function (status) {
  return status >= 200 && status <= 500; // 默认的
};
// 跨域请求，允许保存cookie
Axios.defaults.withCredentials = true;
Axios.withCredentials = true;
NProgress.configure({
  showSpinner: false
});
let prodPoxy = "";
if (env == '"development"') {
  prodPoxy = "";
} else {
  prodPoxy = "/service";
}

// if (env == '"production"') {
//   prodPoxy = '/service'
// }
// Axios.defaults.baseURL = location.origin;
// HTTPrequest拦截
Axios.interceptors.request.use(
  config => {
    if (config.accessToken) {
      config.headers['access-token'] = config.accessToken
    }
    config.isResponseType && (config.responseType = "blob");
    let { url } = config;
    if (env == '"production"') {
      if (url.split("/")[1] && url.split("/")[1] == "api") {
        config.url = prodPoxy + config.url;
      } else if (url.split("/")[1] && url.split("/")[1] == "bjzb") {
        // config.url = "/bjzb" + config.url;
        // eslint-disable-next-line no-self-assign
        config.url = config.url;
      } else if (url.split("/")[1] && url.split("/")[1] == "gczl") {
        // config.url = "/bjzb" + config.url;
        // eslint-disable-next-line no-self-assign
        config.url = "/gczl" + config.url;
      } else {
        config.url = url.indexOf('GB') > -1 ? config.url : '/center' + config.url
      }
    } else {
      config.url = prodPoxy + config.url;
    }
    NProgress.start(); // start progress bar
    let times = new Date().getTime();
    config.params = config.params ? { ...config.params, times } : { times };
    // config.loading && loading.start()
    // headers中配置serialize为true开启序列化
    if (config.method === "post" && config.headers.serialize) {
      config.data = serialize(config.data);
      delete config.data.serialize;
    }

    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  error => {
    // loading.stop()
    return Promise.reject(error);
  }
);

// HTTPresponse拦截
Axios.interceptors.response.use(
  res => {
    NProgress.done();
    // loading.stop()
    const status = Number(res.status) || 200;
    var message = res.data.msg || errorCode[status] || errorCode["default"];
    // const resConfig = res.config || {};
    if (status === 401) {
      Message({
        message: message,
        type: "error"
      });
      return;
    }

    if (status !== 200 || res.data.code === 1) {
      Message({
        message: message,
        type: "error"
      });
      return Promise.reject(new Error(message));
    }

    return res;
  },
  error => {
    NProgress.done();
    // loading.stop()
    return Promise.reject(new Error(error));
  }
);

export default Axios;
