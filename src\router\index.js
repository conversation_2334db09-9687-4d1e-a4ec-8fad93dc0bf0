/*
 * @Author: Jky200<PERSON><PERSON> <EMAIL>
 * @Date: 2023-12-11 09:57:24
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-07-14 15:25:04
 * @FilePath: \ProjectHomePage\src\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import Router from 'vue-router'
const constructionRecord = () => import(/* webpackChunkName: "views" */ "@/components/constructionRecord/index");
const home = () => import(/* webpackChunkName: "views" */ "@/components/home/<USER>");
const Authorization = () => import(/* webpackChunkName: "views" */ "@/components/Report/Authorization/index");
Vue.use(Router)
// 解决ElementUI导航栏中的vue-router在3.0版本以上重复点菜单报错问题
const originalPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}
export default new Router({
  routes: [
    {
      path: "/",
      name: 'constructionRecord',
      redirect: '/constructionRecord',
      meta: { showHeader: false },
    },
    // {
    //   path: "/home",
    //   name: 'home',
    //   component: home,
    //   children: [],
    //   meta: { showHeader: false }
    // },
    {
      path: "/constructionRecord",
      name: 'constructionRecord',
      component: home,
      children: [],
      meta: {
        isTemp: true,
        showHeader: false
      },
    },
    // 一键填报
    {
      path: "/Authorization",
      name: 'Authorization',
      component: Authorization,
      children: [],
      meta: {
        isTemp: true,
        showHeader: true
      },
    },
    //待办列表详情
    {
      path: "/todo",
      name: 'todo',
      component: () => import(/* webpackChunkName: "views" */ "@/pub/components/todo.vue"),
      children: [],
      meta: {
        isTemp: true,
        showHeader: true
      },
    },
    {
      path: "/warning",
      name: 'warning',
      component: () => import(/* webpackChunkName: "views" */ "@/pub/components/message.vue"),
      children: [],
      meta: {
        isTemp: true,
        showHeader: true
      },
    },
  ]
})
