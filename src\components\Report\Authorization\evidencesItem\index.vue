<template>
  <div class="device-management-wrap">
    <div class="left contentStyle">
      <div
        v-for="(item, index) in operateList"
        :key="item.id"
        :class="{ isactive: index == isActive, item: 'item' }"
        @click="leftClick(index, item.item1)"
      >
          <div class="statusNo">{{ item.item2 }}</div>
      </div>
    </div>
    <div
      class="middle contentStyle"
      style="overflow: auto;padding: 1.25rem !important;"
    >
      <el-row>
        <el-col>
          <el-form
            ref="form"
            :model="myForm"
            label-width="90px"
          >
          <el-col :span="3">
              <el-form-item label="" style="text-align: left;" label-width="20px">
                <el-checkbox v-model="myForm.showThisMonthDada" @change="selectInfo()">只显示本月数据</el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="查询条件:">
                <el-input   v-model="myForm.keyword"  placeholder="请输入关键字查询"></el-input>
              </el-form-item>
            </el-col>
            <el-col
              :span="6"
              :offset="1"
            >
              <el-form-item label="资料状况:">
                <el-select
                  placeholder="请选择"
                  v-model="myForm.status"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              :span="5"
              :offset="1"
            >
              <el-button
                type="primary"
                class="buttonType"
                @click="selectInfo()"
              >查询</el-button>
              <el-button
              :disabled="isExport"
                type="primary"
                :loading="isExport"
                class="buttonType"
                @click="exportClick()"
              >导出</el-button>
            </el-col>
          </el-form>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
            <TableList
            :border="false"
              :containerHeight="640"
              :tHeader="columns"
              :tableData="listData"
              :mulSelect="true"
                :mulSelectFixed="true"
              @selectChange="selectionChange"
            >
              <template #options="scope">
                <el-button type="text" @click="detailClick(scope.row)"
                  >查看</el-button
                >
              </template>
              <template #statusName="scope">
                    <span v-if="scope.row.status==2" style="color: #D9001B;">未优化</span>
                    <span v-if="scope.row.status==1" style="color: #fff;">正常</span>
                    <span v-if="scope.row.status==0" style="color: #D9001B;">执行中</span>
                  </template>
            </TableList>
          </el-col>
      </el-row>
    </div>
    <div>
    <el-dialog
      :title="title"
      :visible.sync="visibleDetail"
      width="70%"
      class="reportClass evidencesClass"
      append-to-body
    ><el-row >
        <el-form :model="form" label-width="100px">
          <el-col class="infoType">
            <el-col :span="13" class="infoWrap">
              <el-col class="titleType">补充资料</el-col>
              <el-col class="specialType"><el-form-item label="建议：" label-width="60px" class="font16"><el-col class="font16">{{form.recommendation}}</el-col> </el-form-item></el-col>
              <el-col class="specialType">
                <el-form-item style="display: inline-block;margin-right: 10px;" label="查询条件：" >
                  <el-input v-model="form.keyword" placeholder="请输入关键字查询"
                  ></el-input>
                </el-form-item>
                <el-button type="primary" @click="searchClick">查询</el-button>
                <el-button @click="addClick" type="primary">新增</el-button>
              </el-col>
              <el-col>
                <el-col v-if="isShow" class="" style="max-height: 415px;">
                <TableList
                :border="false"
                :showTooltip="true"
                  :containerHeight="408"
                  :tHeader="columns2"
                  :tableData="listData2"
                >
                  <template #options="scope">
                    <el-button
                      type="text"
                      class="table-btn-error"
                      @click="delHandle(scope.row)"
                      >删除</el-button
                    >
                    <el-button type="text" @click="detailRow(scope.row)"
                      >查看</el-button
                    >
                    <el-button
                      type="text"
                      class="table-btn-error"
                      @click="uploadHandle(scope.row)"
                      >上传</el-button
                    >
                  </template>
                  <template #picPath="scope">
                    <el-image :src="$BASEURL + scope.row.picPath"
                    :alt="scope.row.picName"
                :preview-src-list="PictureList"
                :title="scope.row.picName"
                style="width: 65px;height: 30px;"
                @click="clickPicture(scope.row.picPath)"></el-image>
                  </template>
                </TableList>
              </el-col>
              </el-col>
              <el-col  style="margin:7px 0">
                备注：在保存前，请核实是否根据补充资料中的建议，上传补充资料至确认导出资料列表，以及确认导出资料列表中有无红色标注字样，若有请前往模块对应页面进行数据维护，并在该图片预览中点击手动抓拍，刷新抓拍数据，否则资料状况将显示“未优化”。</el-col>
              <el-col>
            </el-col>
            </el-col>
            <el-col :span="1" class="dividerType" ></el-col>
            <el-col :span="10" class="infoWrap">
              <el-col class="titleType">
              确认导出资料</el-col>
              <el-col class="exportFile">
                <el-col>
                  注：红色字体标注的图片为系统平台中空数据或本月数据无更新
                </el-col>
                <el-col class="padding20">
                关键认证点：
                <el-col class="itemInfoList">
                  <el-col v-for="(item,index) in exportInfo" :key="index">
                    <el-col class="itemType">{{ index+1 }}、{{ item.pointName }}：</el-col>
                    <el-col v-for="(e,i) in item.childs" :key="i" class="itemPro">
                      <el-col :span="1" class="iconType"><i class="el-icon-picture"></i></el-col>
                      <el-col :span="16"  :class="e.isWarning?'warningName':'itemName'" >
                        <!-- <el-tooltip class="item" effect="dark" :content="e.title" placement="top-start"> -->
                          <span  :title="e.title">{{ e.title }}</span>
                        <!-- </el-tooltip> -->
                      </el-col>
                      <el-col :span="1"  class="iconType"><i class="el-icon-view" style="cursor: pointer;" @click="imgClick(e,index,i)"></i></el-col>
                      <el-col :span="1"><i class="el-icon-delete" style="cursor: pointer;" @click="delClick(e,index,i)"></i></el-col>
                    </el-col>
                  </el-col>
                </el-col>
                </el-col>
              </el-col>
            </el-col>
            <el-col class="accept">
              <el-button type="primary"  @click="handleClose">取消</el-button>
              <el-button type="primary" @click="handleAdd">保存</el-button>
            </el-col>
          </el-col>
        </el-form>
        <el-dialog
          :title="title2"
          :visible.sync="addDetail"
          width="28%"
          append-to-body
          class="reportClass"
          ><el-row >
              <el-form :model="supplyform" ref="supplyform" label-width="140px" :rules="rules">
                <el-form-item label="请选择认证点：" prop="pointId">
                  <el-select
                  style="width: 100%;"
                  placeholder="请选择认证点"
                  :disabled="disabled"
                  v-model="supplyform.pointId"
                >
                  <el-option
                    v-for="item in CertificationOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
                </el-form-item>
                <el-form-item label="请输入图片名称："  prop="picName">
                  <el-input  :disabled="disabled" v-model="supplyform.picName" placeholder="请输入图片名称"></el-input>
                </el-form-item>
                <el-form-item label="请上传补充材料：" prop="picPath">
                  <el-col class="itemName">
                    <span :title="supplyform.picFiles[0].fileName" v-if="disabled">
                    {{ supplyform.picFiles[0].fileName }}
                  </span>
                  <el-col v-else>
                    <Upload  v-if="addDetail" ref="uploadFile" v-model="supplyform.picFiles" />
                    <el-col style="text-align: left;">
                      请上传.JPG、.PNG格式图片
                    </el-col>
                  </el-col>
                </el-col>

                </el-form-item>
                <el-col style="text-align: center;">
                  <el-button type="primary" v-if="disabled" @click="editClik">编辑</el-button>
                  <el-button type="primary" v-if="!disabled" @click="CloseClick('supplyform')">取消</el-button>
                  <el-button type="primary" @click="sureClick('supplyform')">确定</el-button>
                </el-col>
              </el-form>
          </el-row>
        </el-dialog>
        <el-dialog
          title="抓拍照片"
          :visible.sync="visibleImg"
          width="50%"
          class="reportClass"
          append-to-body>
          <el-row >
            <el-col style="text-align: center;">
                <el-col v-if="imgType==1"  class="snapImg">
                    <!-- <img :src="imgUrl"/> -->
                    <el-image   :src="imgUrl"
                        :preview-src-list="PictureList"
                        style="display: block;"
                        @click="clickSnapPicture(imgUrl)">
                    </el-image>
                </el-col>
                <div v-if="imgType==2" class="snapImg2" >
                  <el-image   :src="imgUrl"
                        :preview-src-list="PictureList"
                        style="display: block;"
                        @click="clickSnapPicture(imgUrl)">
                    </el-image>
                </div>
                <el-col class="snapTime" v-if="snapTime"><span v-if="imgType==1"> 抓拍执行时间：</span><span v-if="imgType==2"> 上传时间：</span> {{snapTime}}</el-col>
                <el-col class="snapTime" v-else><span>图片生成中请稍后~</span></el-col>
                <el-col class="accept">
                  <el-button v-if="imgType==1"  @click="ReshotClick">重新抓拍</el-button>
                  <el-button @click="handClose">关闭</el-button>
                  <el-button v-if="isRefresh"  @click="RefreshClick">刷新</el-button>
                </el-col>
          </el-col>
          </el-row>
        </el-dialog>
      </el-row>
    </el-dialog>
  </div>
  </div>
</template>
<script>
import Upload from "./upload";
import {exportData,importImgData, typeEnum,batchList,batchtTree,extendList,delData,creatFile,editeData,saveInfo,refreshFile,reshotInfo} from "@/api/report";
export default {
  components: {
    Upload,
  },
  data: () => ({
    components: {},
    isActive: 0,
    operateList: [],
    myForm: {
      practiceType: "",
      status: null,
      keyword: "",
      showThisMonthDada: true
    },
    options: [
      {
        value: null,
        label: "全部",
      },
      {
        value: 0,
        label: "执行中",
      },
      {
        value: 1,
        label: "正常",
      },
      {
        value: 2,
        label: "未优化",
      },
    ],
    listData: [],
    listData2: [],
    columns: [
      {
        prop: "index",
        label: "序号",
        type: "codeNum",
        width: 80
      },
      {
        prop: "name",
        label: "智慧工地做法",
      },
      {
        prop: "keyContent",
        label: "认证关键点",
      },
      {
        prop: "batchExcuteTime",
        label: "自动抓拍时间",
        width: 260
      },
      {
        prop: "statusName",
        label: "资料状况",
        type: "slot",
        width: 180
      },
      {
        prop: 'options',
        type: "slot",
          label: '操作',
          width: 150
        },
    ],
    columns2: [
    {
        prop: "picPath",
        label: "图片",
        type: "slot",
      },
      {
        prop: "createTime",
        label: "时间",
      },
      {
        prop: "pointName",
        label: "认证点",
      },
      {
        prop: "picName",
        label: "图片名称",
      },
      {
        prop: 'options',
        type: "slot",
          label: '操作',
          width: 150
        },
    ],
    pagination: {
      //分页默认值
      totalSize: 0,
      currentPage: 1,
      pageSize: 10,
      position: "center",
      layout: "total,pager,sizes,prev,next,jumper",
    },
    projectId: "",
    addShow: false,
    selectedData: [],
    visibleDetail: false,
    form: {keyword: '',recommendation: ""},
    addDetail: false,
    supplyform: {
      pointId: null,
      picName: "",
      picPath: "",
      notice: "",
      picFiles: []
    },
    exportInfo: [],
    title: '',
    title2: '新增补充资料',
    rules: {
      pointId: [
            { required: true, message: '请选择认证点', trigger: 'change' }
          ],
          picName: [
            { required: true, message: '请输入图片名称', trigger: 'blur' }
          ],
          picPath: [
            { required: true, message: '请输入图片路径', trigger: 'blur' }
          ],
     },
     visibleImg: false,
     imgUrl: '',
     batchId: null,
     PictureList: [],
     CertificationOptions: [],
     extendFileId: null,
     isShow: true,
     addExtendFileIds: [],
     addExtendFiles: [],
     deletePointFileList: [],
     imgType: 1,
     snapTime: '',
     pointFileId: null,
     currentIndex: null,
     childIndex: null,
     uploadUrl: '',
     fileList: [],
     isEdit: false,
     disabled: false,
     isRefresh: false,
     isExport: false

  }),
  // 生命周期函数
  created() {
    this.projectId = getStore({ name: "projectId" });
    // 初始化
    this.getData();
    this.uploadUrl = `${importImgData}`;
  },
  methods: {
    editClik(){
      this.disabled=false
    },
    imgClick(val,index,i){
      this.isRefresh=false
      this.imgType = val.type
      this.snapTime = val.snapTime
      this.pointFileId=val.pointFileId
      this.imgUrl=''
      if(val.type==2){
       this.imgUrl = this.$BASEURL+val.picPath
       this.visibleImg = true
      }else{
        this.currentIndex=index
        this.childIndex=i
        this.imgUrl=''
        // 调用接口
        this.RefreshClick()
      }
    },
    // 关闭
    handClose(){
      this.visibleImg=false
    },
    RefreshClick(){
      let par={
        batchid: this.batchId,
        pointfileid: this.pointFileId
      }
        refreshFile(par).then((res) => {
          console.log(res,"RefreshClick")
          let result = res.data;
          if (result.statusCode == 200) {
            this.imgType = result.data.type
            this.pointFileId=result.data.pointFileId
          this.snapTime = result.data.snapTime
          this.exportInfo[this.currentIndex].childs[this.childIndex]=result.data
            this.imgUrl =this.$BASEURL+result.data.picPath
            this.visibleImg = true
          }else{
          this.$message.error(result.errors);
        }
        });
      },
    // 重新抓拍
    ReshotClick(){
     let par={
        batchid: this.batchId,
        pointfileid: this.pointFileId
      }
      reshotInfo(par).then((res) => {
        let result = res.data;
          if (result.statusCode == 200) {
            this.imgType = result.data.type
            this.snapTime = result.data.snapTime
            this.pointFileId=result.data.pointFileId
            this.exportInfo[this.currentIndex].childs[this.childIndex]=result.data
            this.imgUrl = this.$BASEURL+result.data.picPath
            //this.visibleImg=false
            if(!result.data.snapTime){
              this.isRefresh=true
            }else{
              this.isRefresh=false
            }
          }else{
            this.$message.error(result.errors)
          }
      });
    },
    getData() {
      typeEnum().then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          this.operateList = result.data;
          this.leftClick(0, this.operateList[0].item1);
        }
      });
    },
    // 新增补充资料弹框
    addClick(){
      this.title2 = '新增补充资料'
      this.addDetail=true
      this.isEdit=false
      this.disabled=false
      this.extendFileId=null
      // this.$refs.uploadFile.restart();
      this.supplyform={
        pointId: null,
        picName: "",
        picPath: "",
        notice: "",
        picFiles: []
      }
    },
    // 查看补充资料详情
    detailRow(val){
      this.title2 = '查看补充资料'
      // this.$refs.uploadFile.restart();
      this.addDetail = true
      this.isEdit=true
      this.disabled=true
      this.extendFileId=val.extendFileId
      this.supplyform=val
      this.supplyform.picFiles= []
      let par ={filePath: val.picPath,fileName: val.notice}
      this.supplyform.picFiles.push(par)
    },
    CloseClick(formName){
      this.searchClick()
      this.addDetail=false
      this.$refs[formName].resetFields();
    },
    sureClick(formName){
      if(this.supplyform.picFiles&&this.supplyform.picFiles.length>0){
        this.supplyform.picPath=this.supplyform.picFiles[0].filePath
        this.supplyform.notice=this.supplyform.picFiles[0].fileName
      }else{
        this.$message.error('请上传补充材料')
        return;
      }
        this.$refs[formName].validate((valid) => {
          if (valid) {
            if(this.isEdit){
              this.editeData()
            }else{
              this.saveData()
            }
            this.$refs[formName].resetFields();
            this.addDetail = false
          } else {
            console.log('error submit!!');
            return false;
          }
        });
    },
    editeData(){
      let params ={
        ...this.supplyform,
        batchId: this.batchId,
        extendFileId: this.extendFileId
      }
      editeData(params).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          this.searchClick()
          this.$message({
            type: "success",
            message: "保存成功!",
          });
        }else{
          this.$message.error(result.errors);
        }
      });
    },
    saveData(){
      let params ={
        ...this.supplyform,
        batchId: this.batchId
      }
      creatFile(params).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          this.searchClick()
          this.$message({
            type: "success",
            message: "保存成功!",
          });
        }else{
          this.$message.error(result.errors);
        }
      });
    },
    delClick(val,index,i){
      this.$confirm(
        `是否确认删除此附件？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.exportInfo[index].childs.splice(i,1)
          if(val.pointFileId){
            // 接口获取元素
            this.deletePointFileList.push({pointFileId: val.pointFileId,
                type: val.type})
                // console.log(this.deletePointFileList,"删除接口元素 deletePointFileList")
          }else{
        // 特殊处理：先上传再删除的元素
            this.addExtendFiles.forEach((e,is)=>{
                if(val.picName==e.picName&&val.snapTime==e.snapTime){
                  // console.log(e,val,"元素",this.addExtendFiles)
                  this.addExtendFiles.splice(is,1)
                  // console.log(this.addExtendFiles,"addExtendFiles")
                }
            })
      }
      this.addExtendFileIds=[]
      if(this.addExtendFiles.length>0){
              this.addExtendFiles.forEach((e)=>{
                this.addExtendFileIds.push(e.extendFileId)
              })
            }
            // console.log(this.addExtendFileIds,"addExtendFileIds")
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });

    },
    // 删除补充资料
    delHandle(val){
      this.$confirm(
        `是否确认删除此材料？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
            let params ={
              batchid: this.batchId,
              extendfileid: val.extendFileId
            }
            delData(params).then((res) => {
              let result = res.data;
              if (result.statusCode == 200) {
                this.searchClick()
                that.$message({
                  type: "success",
                  message: "删除成功!",
                });
              }else{
          this.$message.error(result.errors);
        }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 上传补充资料
    uploadHandle(val){
      this.$confirm(
        `是否确认将此图片上传至确认导出资料列表？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.addExtendFileIds=[]
          this.exportInfo.forEach((ele)=>{
            if(ele.pointId === val.pointId){
              let par ={
                extendFileId: val.extendFileId,
                pointFileId: "",
                title: '补充-'+ val.picName,
                picName: val.picName,
                picPath: val.picPath,
                type: 2,
                isWarning: false,
                snapTime: this.getDateTime()
              }
              ele.childs.push(par)
              this.addExtendFiles.push(par)
            }
          })
          setTimeout(()=>{
            this.addExtendFiles.forEach((e)=>{
              this.addExtendFileIds.push(e.extendFileId)
            })
            // console.log(this.addExtendFileIds,"addExtendFileIds")
          },100)
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消上传",
          });
        });
    },
    getDateTime(){
      var currentDate = new Date();
      var year = currentDate.getFullYear();
      var month = currentDate.getMonth() + 1; // 月份从0开始计数，所以需要加1
      var day = currentDate.getDate();
      var hour = currentDate.getHours();
      var minute = currentDate.getMinutes();
      var second = currentDate.getSeconds();
      if(month<10){
      month='0'+month
    }
    if(day<10){
      day='0'+day
    }
    if(hour<10){
      hour='0'+hour
    }
    if(minute<10){
      minute='0'+minute
    }
    if(second<10){
      second='0'+second
    }
      var currentDateTime = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;
      return currentDateTime
    },
    // 导出
    exportClick(){
      console.log(this.selectedData,"this.selectedData")
      if(this.selectedData.length>0){
        this.$confirm(
        `将选中项目的佐证材料导出？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.isExport=true
       let arr=[]
      this.selectedData.forEach((ele)=>{
        arr.push(ele.batchId.toString())
      })
      let parmas={
        batchIds: arr,
      }
      exportData(parmas)
          .then(res => {
            let {
              data: { statusCode, errors }
            } = res;
            if (statusCode == 200) {
              console.log(res.data,"res.data")
              window.open(this.$BASEURL+res.data.data)
              this.$message.success("导出成功");
            } else {
              this.$message.error(errors);
            }
            this.isExport=false
            this.selectInfo()
          });
        })
        .catch(() => {
          this.isExport=false
          this.$message({
            type: "info",
            message: "已取消导出",
          });
        });
      }else{
          this.$message.error('请选择需导出的项目')
          return;
      }
    },
    // 关闭
    handleClose() {
      this.visibleDetail=false
    },
    // 保存
    handleAdd() {
      let parmas={
        batchId: this.batchId,
        deletePointFileList: this.deletePointFileList,
        addExtendFileIds: this.addExtendFileIds
      }
      saveInfo(parmas)
          .then(res => {
            let {
              data: { statusCode, errors }
            } = res;
            if (statusCode == 200) {
              this.$message.success("保存成功");
              this.visibleDetail=false
              this.leftClick(this.isActive,this.myForm.practiceType);
            } else {
              this.$message.error(errors);
            }
            this.visibleDetail = false;
          })
          .catch(() => {
            this.visibleDetail = false;
          });
    },
    selectionChange(val) {
      this.selectedData = val;
    },
    // 查看详情
    detailClick(val){
      this.title= val.name+'-佐证资料'
      this.getList(val)
      this.getTree(val.batchId)
      this.visibleDetail = true;
      this.form.recommendation=val.recommendation
      this.batchId=val.batchId
      this.deletePointFileList=[]
      this.addExtendFileIds=[]
      this.addExtendFiles=[]
    },
    hoverItem(){},
    // 点击整条
    leftClick(index, itemid) {
      this.operateList.map((item) => {
        item.active = 0;
      });
      this.operateList[index].active = 1;
      this.isActive = index;
      if (itemid) {
        this.myForm.practiceType = itemid;
      } else {
        this.myForm.practiceType = "";
      }
      this.selectInfo();
    },
    clickPicture(row){
      let srclist = []
        srclist.push(this.$BASEURL + row)
      this.PictureList = srclist
    },
    clickSnapPicture(row){
      let srclist = []
        srclist.push(row)
      this.PictureList = srclist
    },
     // 获取导出材料
    getTree(val) {
      this.exportInfo = []
      this.CertificationOptions=[]
      batchtTree(val).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          this.exportInfo = result.data || [];
          this.exportInfo.forEach((ele)=>{
            this.CertificationOptions.push({value: ele.pointId,label: ele.pointName})
          })
        }else{
          this.$message.error(result.errors);
        }
      });
    },
    // 获取补充资料
    getList(val) {
    let params ={
        batchid: val.batchId,
        keyword: this.form.keyword
      }
      extendList(params).then((res) => {
         //TODO:调用接口（列表懒加载）
        let result = res.data;
        if (result.statusCode == 200) {
          this.isShow=false
          this.listData2 = result.data || [];
          this.listData2.forEach((ele)=>{
            ele.createTime=  ele.createTime.slice(0,-3)
          })
          this.isShow = true
        }else{
          this.$message.error(result.errors);
        }
      });
    },
    // 获取佐证列表
    selectInfo() {
      let params = {
      ...this.myForm,
      };
      batchList(params).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          this.listData = result.data || [];
          this.listData.forEach((ele)=>{
            ele.batchExcuteTime=  ele.batchExcuteTime.slice(0,-3)
          })
        }else{
          this.$message.error(result.errors);
        }
      });
    },
    // 查询补充资料
    searchClick() {
      let par={
        batchId: this.batchId,
      }
      this.getList(par)
    },
    // 页码
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.selectInfo();
    },
    // 页数
    handleSizeChange(val) {
      this.pagination.currentPage = 1;
      this.pagination.pageSize = val;
      this.selectInfo();
    },
  },
};
</script>
<style lang="scss">
.evidencesClass {
    .itemInfoList{
      height:  550px;
      overflow: auto;
    }
}
.specialType{
  .el-form-item__label{
    font-size: 16px;
  }
}
 .accept{
      text-align: center;
    }
    .infoWrap{
      height: 700px;
      padding: 0 10px;
      font-size: 16px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
    }
    .titleType{
      text-align: center;
      margin-bottom: 10px;
      font-size: 18px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      color: #01cef5;
      line-height: 21px;
    }
    .dividerType{
      width: 0 !important;
      height: 650px;
      margin:0 20px;
      background: #0F2571;
      border: 1px solid;
      border-image: linear-gradient(180deg, rgba(15, 45, 169, 1), rgba(51, 128, 220, 1), rgba(191, 218, 248, 1), rgba(51, 128, 220, 0.89), rgba(10, 31, 115, 0.68)) 1 1;
    }
    .exportFile{
        height: 40px;
        font-size: 16px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        line-height:40px;
    }
     .font16{
        font-size: 16px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        .el-form-item__content {
    line-height: 25px;
}
.el-form-item__label{
  line-height: 27px;
}
    }
    .padding20{
      padding-bottom: 35px;
    }
    .itemType{
      font-size: 16px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      color: #1FC6FF;
      line-height: 40px;
    }
    .itemPro{
      width: 99%;
      // padding: 0 13px;
      height: 46px;
      line-height: 46px;
      background: #103C74;
      border-radius: 0px;
      }
    .itemPro:hover{
      background: #114C85;
      border-radius: 0px;
      opacity: 1;
      font-family: PingFang SC, PingFang SC;
      color: #1FC6FF;
    }
    .iconType{
      margin:0 16px;
    }
    .itemName{
      width:355px;
      overflow: hidden  !important; /* 隐藏超出容器宽度的文本 */
      text-overflow: ellipsis  !important; /* 显示省略号 */
      white-space: nowrap  !important; /* 禁止换行 */
    }
    .warningName{
      color: #FF5B5B;
      width:355px;
      overflow: hidden  !important; /* 隐藏超出容器宽度的文本 */
      text-overflow: ellipsis  !important; /* 显示省略号 */
      white-space: nowrap  !important; /* 禁止换行 */
    }
    .itemName[title]:hover:after, .warningName[title]:hover:after{
				content: attr(title);
				color: #fff;
				padding: 4px 8px;
				position: absolute;
				left: 0;
				top: -160%;
				z-index: 20;
				white-space: nowrap;
				background-color: rgba(37, 39, 42, .85);
			}
    .infoType{
      .el-form-item__label{
        text-align: left;
      }
    }
    .snapImg{
        // background: url(../../../../assets/imgBg.png) no-repeat;
        width: 99%;
        margin: 0 auto;
        // height: 308px;
        background-size: 100% 100%;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        position: relative;
      img{
        padding: 1% 0;
        width: 99%;
        height: 98%;
        border-radius: 6px;
        // border: 1px solid #2F35B2;
      }
    }
    .snapImg2{
      img{
        // background: url(../../../../assets/imgBg.png) no-repeat;
        padding: 7px;
        background-size: 100% 100%;
        width: auto;
        border-radius: 6px;
        object-fit: scale-down;
        max-width: 500px;
        max-height: 500px;
        // border: 1px solid #2F35B2;
      }
    }
    .snapTime{
      text-align: center;
      font-size: 16px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      color: #1EDBF9;margin:20px 0
    }
    .contentStyle {
      background: #0b3472;
      box-sizing: border-box;
    }
    .device-management-wrap {
      display: flex;
      .title {
        font-size: 16px;
        font-weight: 600;
        height: 42px;
        background: linear-gradient(270deg, #103594 0%, #1e57b2 100%);
        padding: 0 16px;
        line-height: 42px;
      }
      .left {
        width: 350px;
        border: 1px solid #365f7d;
        padding:20px ;

        .title {
          color: #fff;
          height: 42px;
          padding: 0 16px;
          line-height: 42px;
          font-weight: 600;
          border-bottom: 2px solid #365f7d;
          display: flex;
          align-items: center;
          padding: 0 20px;
          justify-content: space-between;
        }

        .item {
          height: 50px;
          // border-bottom: 1px solid #365f7d;
          display: flex;
          align-items: center;
          padding: 0 20px;
          margin:10px 20px;
          justify-content: space-between;
          cursor: pointer;
        }

        // .item:hover {
        //   background-color: #1890FF;
        // }

        .isactive {
          background-color: #295AEA;
        }

        .point {
          height: 10px;
          width: 5px;
          background: #61d2f7;
        }

        .statusNo {
          margin: 0 20px;
          width: 100%;
        }

        // .status {
        //   padding: 5px 10px;
        //   // border-radius: 20px;
        //   // border: 1px solid #365f7d;
        //   display: inline-block;
        // }
        .status {
          padding: 5px 10px;
          // border-radius: 20px;
          // border: 1px solid #365f7d;
          display: inline-block;
          float: right;
        }
        .status img {
          display: flex;
          align-items: center;
        }
        .errorlevel {
          padding: 5px 10px;
          border-radius: 20px;
          border: 1px solid #797979;
          background: #797979;
          // margin-left: 90px;
          margin-right: 20px;
          width: 35px;
          //  float: right;
          // display: inline-block;
          float: right;
        }

        .rightlevel {
          padding: 5px 10px;
          border-radius: 20px;
          float: right;
          // margin-right: 40px;
          // margin-left: 40px;
          margin-right: 20px;
          color: #61d2f7;
          border: 1px solid #61d2f7;
          width: 35px;
          // display: inline-block;
        }

        .view {
          width: 50px;
          color: #61d2f7;
          // float: right;
          display: inline-block;
          //  margin-right:20px;
        }

        .text {
          cursor: pointer;
          font-size: 16px;
        }

        .del {
          width: 50px;
          float: right;
          // margin-right:20px;
        }
      }
      .middle {
        flex: 1;
        min-height: 750px;
        border: 1px solid #365f7d;
        margin: 0 30px;
        padding: 20px;
      }
    }
</style>
