/*
 * @Description:
 * @Author:
 * @Date: 2021-12-31 10:32:26
 * @LastEditTime: 2025-08-01 13:56:57
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Usage:
 */
// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
// import "babel-polyfill";
require("es6-promise").polyfill();
import Vue from "vue";
import App from "./App";
import router from "./router";
// import ElementUi from "element-ui";
// import "element-ui/lib/theme-chalk/index.css";
import * as echarts from "echarts";
require("echarts/lib/component/legend");
Vue.prototype.$echarts = echarts;

import china from "@/static/china.json";
echarts.registerMap("china", china);

import "@/pub/styles/common.scss";
import "@/pub/styles/element_css.scss";
import "@/pub/styles/report.scss";
// import "lib-flexible/flexible";
import tableList from "@/pub/components/tableList";
import listTable from "@/pub/components/listTable";
import { loadUtil } from "@/util/date";
import { showMessage, showConfirm } from "@/util/promp";
import {
  getStore,
  setStore,
  removeStore,
  // getNameList,
  downloadFile
} from "@/util/store";
import { BaseUrl } from "@/router/baseUrl";
// import Video from "video.js";
import { getUrlParams } from "@/util/util.js";
import { getProjectId } from "@/util/project.js";
import JKYComponent from "jky-component";
import "jky-component/lib/jky-component.css";
import 'jky-component/lib/styles/common/jky-common.css';
// import 'video.js/dist/video-js.css'
import axios from "@/router/axios";
import jkyUtils from "jky-utils";
jkyUtils.auth.useAxiosAuth(axios, { ip: process.env.npm_config_domin || process.env.npm_config_ip });

//menu
import AsideMenu from 'asidemenu'
import 'asidemenu/lib/asidemenu.css'
import 'asidemenu/lib/styles/common/jky-common.css'

import '@/assets/css/fonts.scss';

Vue.use(AsideMenu);

// 视频播放插件

// import VideoPlayer from "vue-video-player";
// require("video.js/dist/video-js.css");
// require("vue-video-player/src/custom-theme.css");
// Vue.use(VideoPlayer);
const hls = require("videojs-contrib-hls");
Vue.use(hls);

Vue.prototype.$BASEURL = BaseUrl;
Vue.prototype.$showMessage = showMessage;
Vue.prototype.$showConfirm = showConfirm;
// Vue.prototype.getNameList = getNameList;
// Vue.prototype.$video = Video;
// Vue.use(ElementUi);
Vue.use(JKYComponent);
Vue.component("TableList", tableList);
Vue.component("list-table", listTable);
Vue.config.productionTip = false;
// window.getNameList = getNameList;
window.getStore = getStore;
window.setStore = setStore;
window.removeStroe = removeStore;
window.$downloadFile = downloadFile;
const language = getUrlParams().language || localStorage.getItem("locale");
const header = getUrlParams().header || 'true';
Vue.prototype.$isShowHeader = header == 'true' ? true : false;

setStore({ name: "tempUserId", content: getStore({ name: "userId" }) || "" });
setStore({ name: 'language', content: language || "zh" });
// 阿联酋详细翻译，projectId为444、192
const isProjectShow = getProjectId();
setStore({ name: 'isProjectShow', content: isProjectShow });
Vue.prototype.$IsProjectShow = isProjectShow;
// if(isProjectShow){
try {
  if (window.translate) {
    translate.setUseVersion2(); //设置使用v2.x 版本
    translate.nomenclature.append('chinese_simplified', 'english', `
  节电管理=Energy saving management
  节水管理=Water saving management
  视频管理=Video management
  升降机监测=Elevator monitoring
  高支模监测=High - formwork
  标准养护室管理=Standard curing room
  混凝土测温=Concrete temperature
  垃圾处理设备=Garbage disposal
  智慧防疫=Smart antiepidemic
  爱国卫生运动=Patriotic   health
  装配式临建=Prefabricated temporary
  垃圾分类管理=Refuse classification
  食材管理=Food ingredient
  建筑垃圾管理=Construction waste
  机械设备台账=Mechanical Equipment
  监理旁站=Supervision site
  深基坑监测=Deep foundation
  附着式升降脚手架监测=Attached lifting scaffolding
  工具式防护栏杆=Tool type parapet
  健康监测设备管理=Health monitoring
  智能探测设备管理=Intelligent detection
  风险管控=Risk control
  标准库=Building related standards
  `);
    //translate.ignore.class.push('el-dropdown-menu__item');
    // translate.ignore.class.push('text');
    translate.ignore.class.push('areaContent');
    translate.ignore.class.push('middleStyle');
    translate.ignore.class.push('sides');
    translate.ignore.class.push('device-management-wrap');
    translate.ignore.class.push('nav');
    translate.language.translateLanguagesRange = ['chinese_simplified'];
    translate.service.use('client.edge');
    translate.selectLanguageTag.show = false
    translate.listener.start()
    translate.request.listener.delayExecuteTime = 500;
    translate.request.listener.start();
    if (language === 'zh') {
      // 设置本地语种为简体中文
      translate.language.setLocal('english');
      //设置要转换为英文
      translate.changeLanguage('chinese_simplified');
    }
    if (language === 'en') {
      translate.language.setLocal('chinese_simplified');
      //设置要转换为英文
      translate.changeLanguage('english');
    }
    // translate.execute() //进行翻译
  }
} catch (error) { }
loadUtil();
import i18n from './lang/config/index'
/* eslint-disable no-new */
new Vue({
  el: "#app",
  router,
  i18n,
  components: { App },
  beforeCreate() {
    Vue.prototype.$bus = this;
  },
  template: "<App/>"
});
