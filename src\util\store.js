import { validatenull } from "@/util/validate";
// const keyName = "jky-labour-";
const keyName = "";
/**
 * 存储localStorage
 */
export const setStore = (params = {}) => {
  let { name, content, type } = params;
  name = keyName + name;
  const obj = {
    dataType: typeof content,
    content: content,
    type: type,
    datetime: new Date().getTime()
  };
  if (type) window.sessionStorage.setItem(name, JSON.stringify(obj));
  else window.localStorage.setItem(name, JSON.stringify(obj));
};
/**
 * 获取localStorage
 */

export const getStore = (params = {}) => {
  let { name, debug } = params;
  name = keyName + name;
  let obj = {};
  let content;
  obj = window.sessionStorage.getItem(name);
  if (validatenull(obj)) obj = window.localStorage.getItem(name);
  if (validatenull(obj)) return;
  try {
    obj = JSON.parse(obj);
  } catch (e) {
    return obj;
  }
  if (debug) {
    return obj;
  }
  if (obj.dataType === "string") {
    content = obj.content;
  } else if (obj.dataType === "number") {
    content = Number(obj.content);
  } else if (obj.dataType === "boolean") {
    content = eval(obj.content);
  } else if (obj.dataType === "object") {
    content = obj.content;
  }
  return content;
};
/**
 * 删除localStorage
 */
export const removeStore = (params = {}) => {
  let { name, type } = params;
  name = keyName + name;
  if (type) {
    window.sessionStorage.removeItem(name);
  } else {
    window.localStorage.removeItem(name);
  }
};

/**
 * 获取全部localStorage
 */
export const getAllStore = (params = {}) => {
  const list = [];
  const { type } = params;
  if (type) {
    for (let i = 0; i <= window.sessionStorage.length; i++) {
      list.push({
        name: window.sessionStorage.key(i),
        content: getStore({
          name: window.sessionStorage.key(i),
          type: "session"
        })
      });
    }
  } else {
    for (let i = 0; i <= window.localStorage.length; i++) {
      list.push({
        name: window.localStorage.key(i),
        content: getStore({
          name: window.localStorage.key(i)
        })
      });
    }
  }
  return list;
};

/**
 * 清空全部localStorage
 */
export const clearStore = (params = {}) => {
  const { type } = params;
  if (type) {
    window.sessionStorage.clear();
  } else {
    window.localStorage.clear();
  }
};
/**
 * 导出
 */
export const downloadFile = (res, fileName, blob, type) => {
  let types = type || "application/vnd.ms-excel";
  if (!res) {
    return;
  }
  if (window.navigator.msSaveBlob) {
    try {
      window.navigator.msSaveBlob(res, `${fileName || "导出文件"}`);
    } catch (e) {
      console.log(e);
    }
  } else {
    const link = document.createElement("a");
    !blob &&
      (res = window.URL.createObjectURL(new Blob([res], { type: types })));
    link.href = res;
    link.style.display = "none";
    link.setAttribute("download", `${fileName || "导出文件"}.xlsx`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    !blob && window.URL.revokeObjectURL(res);
  }
};
//接口数据请求
function apiPup({
  obj,
  apiFun,
  params,
  listName,
  reqName,
  totalName,
  successMsg,
  errorMsg,
  callback,
  secondListName,
  asyncName,
  isReqData
}) {
  return new Promise(function (reslove, reject) {
    reslove(
      apiFun(params).then(res => {
        let result = res.data;
        if (obj) {
          if (result.statusCode == 200) {
            if (secondListName) {
              obj[listName][secondListName] = reqName
                ? result.data[reqName]
                : result.data || [];
            } else {
              listName &&
                (obj[listName] = reqName
                  ? result.data[reqName]
                  : result.data || []);
            }
            successMsg &&
              obj.$showMessage(
                successMsg.title,
                successMsg.content,
                successMsg.type
              );
            totalName &&
              (obj.pagination.totalSize = result.data[totalName] || 0);
            callback && callback();
            asyncName && (obj[asyncName] = +new Date());
            if (listName) {
              if (secondListName) {
                return obj[listName][secondListName];
              } else {
                return obj[listName];
              }
            } else {
              return res.data.data;
            }
          } else {
            totalName && (obj.pagination.totalSize = 0);
            errorMsg
              ? obj.$showMessage(
                errorMsg.title,
                errorMsg.content,
                errorMsg.type
              )
              : obj.$showMessage();
            return res;
          }
        } else {
          if (!isReqData) {
            return result.data;
          } else {
            return res;
          }
        }
      })
    );
  });
}
/**
 *  必传字段：
 * apiFun 接口方法
 * 非必传字段
 * isReqData 是否返回数据结果 默认 false 返回  true 不返回  用于需要数据处理的情况
 * obj 当前的this对象 非必传 isReqData 为true时 不可传此参数
 * params 接口请求参数  根据接口情况选择必传非必传
 * listName 用于接收数据的key名称 若有obj 为必传
 * secondListName 用于两级嵌套数据的 二级数据key名称
 * reqName  用于数据返回两级嵌套数据的 二级数据key名称
 * totalName 用于分页参数的数据总数
 * successMsg 接口请求成功时自定义文字提示
 * errorMsg 接口请求失败时自定义文字提示
 * title    confirm弹框的自定义title标题
 * msg    confirm弹框的自定义文字提示
 * callback 接口调用成功后的回调函数
 * asyncName 请求完成后是否启用异步加载组件
 *
 * **/
export const apiServe = (
  {
    obj,
    apiFun,
    params,
    listName,
    reqName,
    totalName,
    successMsg,
    errorMsg,
    title,
    msg,
    callback,
    secondListName,
    asyncName,
    isReqData
  },
  confirm
) => {
  let param = {
    obj,
    apiFun,
    params,
    listName,
    reqName,
    totalName,
    successMsg,
    errorMsg,
    callback,
    secondListName,
    asyncName,
    isReqData
  };
  if (confirm) {
    obj.$showConfirm(msg || "确定要删除此项吗？", title || "提示", () => {
      apiPup(param);
    });
  } else {
    return new Promise(function (reslove, reject) {
      reslove(apiPup(param));
    });
  }
};
