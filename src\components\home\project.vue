<template>
  <div class="background-image-container">
    <img
      class="background-image"
      :src="currentImage"
      alt="Project Background"
    >
    <div class="project-stats">
      <div class="stat-item">
        <img
          class="stat-icon"
          src="@/assets/tab/pro_area.png"
          alt="icon"
        />
        <div class="stat-info">
          <div class="stat-label">建筑面积</div>
          <div class="stat-value">{{projectInfoObj.projectArea}}m²</div>
        </div>
      </div>
      <div class="stat-item">
        <img
          class="stat-icon"
          src="@/assets/tab/pro_money.png"
          alt="icon"
        />
        <div class="stat-info">
          <div class="stat-label">合同金额</div>
          <div class="stat-value">{{projectInfoObj.contractAmount / 10000}}w</div>
        </div>
      </div>
      <div class="stat-item">
        <img
          class="stat-icon"
          src="@/assets/tab/pro_day.png"
          alt="icon"
        />
        <div class="stat-info">
          <div class="stat-label">合同总工期</div>
          <div class="stat-value"> {{projectInfoObj.day}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'project',
  props: {
    imageUrl: { type: String, required: true },
    info: { type: Object, required: true }
  },
  data() {
    return { currentImage: '', projectInfoObj: this.info }
  },
  watch: {
    imageUrl(newVal) {

    },
    info(newVal) {
      this.projectInfoObj = newVal;
      this.currentImage = this.$BASEURL + newVal.indexImage;
      // 步骤 1: 获取当前日期
      const currentDate = new Date();

      // 步骤 2: 解析开始日期
      const startDate = new Date(newVal.planBeginTime);

      // 步骤 3: 计算时间差（以毫秒为单位）
      const timeDifference = startDate ? currentDate - startDate : 0;

      // 步骤 4: 将时间差转换为天数
      const oneDayInMilliseconds = 24 * 60 * 60 * 1000;
      const daysDifference = Math.floor(timeDifference / oneDayInMilliseconds);
      this.projectInfoObj.day = daysDifference < 0 ? 0 : daysDifference;
    }
  }
}
</script>

<style lang="scss" scoped>
.background-image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;

  .background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    pointer-events: none;
  }

  .project-stats {
    position: absolute;
    top: 70px;
    display: flex;
    flex-direction: row;
    gap: 48px;
    z-index: 2;
    width: 100%;
    justify-content: center;
    padding-top: 40px;
    // background: linear-gradient(
    //   180deg,
    //   rgba(0, 19, 77, 0.65) 0%,
    //   rgba(0, 19, 77, 0.3) 65%,
    //   rgba(0, 19, 77, 0) 100%
    // );
    border-radius: 0px 0px 0px 0px;
  }

  .stat-item {
    display: flex;
    align-items: center;
    border-radius: 16px;
    padding: 10px 32px 10px 16px;
  }

  .stat-icon {
    width: 75px;
    height: 77px;
    margin-right: 6px;
    filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.12));
  }

  .stat-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .stat-label {
    font-weight: normal;
    font-size: 14px;
    color: #ffffff;
    line-height: 14px;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.25);
    text-align: left;
    font-style: normal;
  }

  .stat-value {
    height: 28px;
    font-family: "YouSheBiaoTiHei", sans-serif;
    font-weight: 400;
    font-size: 28px;
    line-height: 28px;
    text-shadow: 1px 2px 2px rgba(0, 0, 0, 0.25);
    text-align: left;
    font-style: normal;
    text-transform: none;
    color: #ffffff;
    // background: linear-gradient(180deg, #ffffff 10%, #45a0fb 100%);
    // -webkit-background-clip: text;
    // background-clip: text;
    // -webkit-text-fill-color: transparent;
  }
}
</style>