<!--
 * @Description:
 * @Author:
 * @Date: 2021-12-21 15:29:41
 * @LastEditTime: 2022-10-13 11:09:58
 * @LastEditors: guohongyuan
 * @Usage:
-->
<!-- 现场劳务 -->
<template>
  <div class="siteStyle">
    <div class="box" ref="siteRef">
      <div
        id="ringChart3"
        :style="{ height: barHeight + 'px', width: '100%' }"
        class="ringChart"
      ></div>
    </div>
    <div class="legend_div">
      <div :class="this.colorData.length > 5 ? 'wordsLoop' : ''">
        <ul v-for="(item, index) in this.colorData" :key="item.index">
          <!-- :style="{ color: 'item.color' }" -->
          <span
            :style="{ background: colorList[index] }"
            class="barStyle"
          ></span>
          <span class="nameType">{{ item.name }}</span>
          <span>{{ item.value }}</span>
        </ul>
      </div>
      <div v-show="this.colorData.length > 5" class="wordsLoop">
        <ul v-for="(item, index) in this.colorData" :key="item.index">
          <!-- :style="{ color: 'item.color' }" -->
          <span
            :style="{ background: colorList[index] }"
            class="barStyle"
          ></span>
          <span class="nameType">{{ item.name }}</span>
          <span>{{ item.value }}</span>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { drawRing2 } from "@/util/echarts";
import { getData } from "@/api/constructionRecord";
import { getScreenWidth } from "@/util/screen";
export default {
  components: {},
  name: "siteLabor",
  data() {
    return {
      barHeight: null,
      colorList: [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#0099CC",
        "#AA66CC",
        "#9933CC",
        "#99CC00",
        "#669900",
        "#FFBB33",
        "#FF8800",
        "#FF4444",
        "#CC0000",
      ],
      // 空心饼图 工种
      ringParams3: {
        dom: "ringChart3",
        subTitle: 0,
        show: true,
        legend: "60%",
        titleX: "36%",
        nameTitle: "",
        center: ["40%", "50%"],
        total: 0,
        data: [
          {
            value: 5,
            name: "pm",
          },
        ],
        colorArray: ["#F0EF68"],
      },
      colorData: [],
      projectId: "",
      sum: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
    this.getPieCicle();
  },
  methods: {
    setEchartsWidth() {
      // this.barWidth = this.$refs.siteRef.offsetWidth - 20;
      this.barHeight = this.$refs.siteRef.offsetHeight;
      // this.barHeight = getScreenWidth(200);
    },
    // 实心图
    getPieCicle() {
      let params = {
        projectId: this.projectId,
        type: 0,
      };
      getData(params).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          let arr1 = [];
          this.colorData = [];
          // 实现饼状图--工种
          result.data[0].piecharValue.map((item, index) => {
            arr1.push({
              value: item.fieldvalues,
              name: item.field,
            });
            this.sum += item.fieldvalues;
          });
          // 空心饼图 工种
          if (arr1.length == 0) {
            this.ringParams3.data = [{ value: 0, name: "" }];
          } else {
            this.ringParams3.data = arr1;
            arr1.forEach((ele) => {
              this.colorData.push(ele);
            });
            this.colorData.forEach((ele) => {
              if (ele.name) {
                if (ele.name.length > 6) {
                  ele.name = ele.name.slice(0, 5) + "...";
                }
              }
            });
          }
          this.ringParams3.nameTitle = this.sum + "人";
          drawRing2(this.ringParams3);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.siteStyle {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 0 20px;
  display: flex;
  flex-direction: row;
  .nameType {
    width: 100px;
    text-overflow: ellipsis; /* 将文本溢出显示... */
    overflow: hidden; /* 超出隐藏 */
    white-space: nowrap;
  }
  .barStyle {
    color: red;
    width: 22px;
    height: 16px;
    border-radius: 3px;
    margin: 0 6px;
    vertical-align: text-top;
    display: inline-block;
  }
  .legend_div {
    animation: row 10s linear infinite;
  }
  .legend_div:hover {
    animation-play-state: paused;
  }
  .box {
    width: 50%;
    height: 90%;
  }
  .legend_div {
    width: 50%;
    display: inline;
    line-height: 24px;
    height: 90%;
    overflow: hidden;
  }
  .wordsLoop {
    display: inline-block;
    white-space: nowrap;
    animation: 10s wordsLoop linear infinite normal;
  }
}

@keyframes wordsLoop {
  0% {
    transform: translateY(0px);
    -webkit-transform: translateY(0px);
  }
  100% {
    transform: translateY(-100%);
    -webkit-transform: translateY(-100%);
  }
}

@-webkit-keyframes wordsLoop {
  0% {
    transform: translateY(0px);
    -webkit-transform: translateY(0px);
  }
  100% {
    transform: translateY(-100%);
    -webkit-transform: translateY(-100%);
  }
}
</style>
