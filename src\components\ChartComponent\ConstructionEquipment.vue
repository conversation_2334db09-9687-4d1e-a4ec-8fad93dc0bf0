<!--
 @Description: 施工设备监测
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="deviceRecordRef"
      >
        <!-- <p class="titleNum">
          <span>设备总数：</span></br>{{ totalCount }}
        </p> -->
        <div
          id="deviceRecordChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { equipmentStatics } from "@/api/echrtsApi";
export default {
  components: {},
  name: "deviceRecord",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "deviceRecordChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.constructionEquipmentTotal"),
        seriesCenter: ["25%", "58%"],
        richNameWidth: 60,
        noTooltipShow: true, //不显示
        itemStyleEmphasis: {
          label: {
            show: true,
            // position: 'center',
            x: "20%",
            y: "10%",
            textStyle: {
              rich: {
                numText: {
                  color: "#fff",
                  fontSize: 13,
                  width: 30,
                  textAlign: "center",
                },
                text: {
                  color: "#fff",
                  fontSize: 13,
                  padding: [0, 0, 10, 0],
                  width: 30,
                  textAlign: "center",
                },
              },
            },
            formatter: function (params) {
              return `{text| ${params.name}}\n{numText|${params.value}}`;
            },
          },
        },
        costomLegendFormatter:function(name){
          return name;
        },
      },
      totalCount: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.deviceRecordRef.offsetWidth;
      this.barHeight = this.$refs.deviceRecordRef.offsetHeight;
    },
    getBarData() {
      equipmentStatics()
        .then((res) => {
          const { status, data } = res;
          if (status == 200) {
            this.setEcharts(data);
          }
        })
        .catch(() => { });
    },
    setEcharts(val) {
      let dataList = val.data;
      // this.pieParams.titleInfor.text = totalCount;
      if (dataList.length > 0) {
        let legendFormatter = (name) => {
          const item = dataList.find((i) => {
            return i.name === name;
          });
          const p = item.value;
          let clientWidth = document.documentElement.clientWidth;
          let newName = (newName =
            name.length > 5 ? name.slice(0, 5) + '...' : name);
          if (clientWidth < 1900) {
            newName = name.slice(0, 5);


            this.pieParams.richNameWidth = 60;
          }
          return "{name|" + newName + "}" + "{percent|" + p + "}";
        };
        this.pieParams.legendFormatter = legendFormatter;

        let sum = 0;
        dataList.forEach((ele) => {
          sum = Number(sum) + Number(ele.value);
        });
        this.totalCount = sum;
      }
      this.pieParams.data = dataList;
      drawAnnularChart(this.pieParams);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.titleNum {
  position: absolute;
  left: 5.4vw;
  top: 46%;
  text-align: center;
}
</style>
