<!--
 * @Description: 自动喷淋
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-24 17:08:16
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div class="box" ref="AutomaticSprayRef">
        <div
          id="AutomaticSprayChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>

import { drawLineStackShadow } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getAutomaticSpray } from "@/api/echrtsApi";
export default {
  components: {},
  name: "AutomaticSpray",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      userId: "",
      barWidth: null,
      barHeight: null,
      barParams: {
        dom: "AutomaticSprayChart",
        xAxisData: [],
        seriesData: [],
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        yminInterval: 1,
        unit: "",
        grid: {
          top: "18%",
          left: "12%",
          right: "5%",
          bottom: "10%"
        },
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.userId = getStore({
      name: "userId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.AutomaticSprayRef.offsetWidth;
      this.barHeight = this.$refs.AutomaticSprayRef.offsetHeight;
    },
    getBarData() {
      getAutomaticSpray(this.userId)
        .then((res) => {
          console.log(res);
          const {
            data: { Series, xAxis },
            status,
          } = res;
          if (status == 200) {
            let nameV = [];
            xAxis.data.forEach((ele) => {
              nameV.push(ele.substring(5, 10));
            });
            this.barParams.xAxisData = nameV;
            this.barParams.seriesData = Series;
            console.log(this.barParams);
            
            drawLineStackShadow(this.barParams);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
