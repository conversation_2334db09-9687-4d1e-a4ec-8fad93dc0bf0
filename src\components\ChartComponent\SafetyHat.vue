<!--
 * @Description: 智能安全帽
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-28 18:25:53
 * @LastEditors: dong<PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div class="box" ref="SafetyHatRef">
        <div
          id="SafetyHatChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
      <p class="countStyleOne">
        <span>智能安全帽数量：{{ totalCount }}</span>
        <span>在线数量：{{ onlineCount }}</span>
      </p>
    </div>
  </div>
</template>
<script>

import { drawHorizontalBarChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getSmartHelmet } from "@/api/echrtsApi";
export default {
  components: {},
  name: "SafetyHat",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      barParams: {
        dom: "SafetyHatChart",
        xAxisData: [],
        seriesData: [],
        boundaryGap: true,
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        yminInterval: 1,
        grid:{
          borderWidth: 0,
          top: '15%',
          left: '25%',
          right: '15%',
          bottom: '3%'
        },
        series_label_normal_position:'left',
        tooltipFormatter: function (val) {
          let msg = "";
          if (val.length) {
            val.forEach((ele) => {
              msg += `${ele.marker}${ele.name}
              <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
              <b>${ele.data}</b>次`;
            });
          }
          return msg;
        },
      },
      totalCount: 0,
      onlineCount: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.SafetyHatRef.offsetWidth - 40;
      this.barHeight = this.$refs.SafetyHatRef.offsetHeight;
    },
    getBarData() {
      getSmartHelmet()
        .then((res) => {
          const { data, statusCode } = res.data;
          if (statusCode == 200) {
            let seriesData = [
              {
                // name: '报警次数',
                data: data.list[0].data,
                type: "bar",
                itemStyle: {
                  normal: {
                    color: "#4e81e4",
                  },
                },
              },
            ];
            this.barParams.xAxisData = data.names.map((item) => {
              switch (item) {
                case "脱帽报警":
                  item = this.$t("customization.unHatAlarm");
                  break;
                case "围栏报警":
                  item = this.$t("customization.fenceAlarm");
                  break;
                case "温度报警":
                  item = this.$t("customization.temperatureAlarm");
                  break;
                case "跌落报警":
                  item = this.$t("customization.fallAlarm");
                  break;
              }
              return item;
            });
            this.barParams.seriesData = seriesData;
            this.totalCount = data.totalCount;
            this.onlineCount = data.onlineCount;
            drawHorizontalBarChart(this.barParams);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
}
</style>
