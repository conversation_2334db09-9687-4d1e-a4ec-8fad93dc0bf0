<template>
  <el-row class="upload">
    <el-col v-if="dialog">
      <el-dialog
        :visible.sync="dialogVisible"
        :width="width"
        @close="dialogCloseFun"
        :modal-append-to-body="false"
      >
        <el-row>
          <el-upload
            :disabled="disabled"
            class="upload-demo"
            ref="upload"
            :data="params"
            :http-request="httpRequest"
            :list-type="listType"
            :multiple="multiple"
            :action="action"
            :headers="headers"
            :on-error="handleError"
            :on-change="handleChange"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :file-list="fileList"
            :auto-upload="autoUpload"
            :limit="limit"
            :on-exceed="onExceed"
          >
            <el-button
              slot="trigger"
              :disabled="disabled"
              size="small"
              type="primary"
            >选取文件</el-button>
            <el-button
              style="margin-left: 10px"
              size="small"
              type="primary"
              @click="submitUpload"
              :disabled="disabled"
            >确定</el-button>
            <div
              slot="tip"
              class="el-upload__tip"
            ></div>
            <slot name="wraning"></slot>
          </el-upload>
        </el-row>
      </el-dialog>
    </el-col>
    <el-col v-else>
      <el-col v-if="listType == 'picture' || listType == 'picture-card'">
        <el-upload
          class="avatar-uploader"
          :disabled="disabled"
          :data="params"
          :http-request="httpRequest"
          :action="action"
          :headers="headers"
          :multiple="multiple"
          :on-error="handleError"
          :auto-upload="autoUpload"
          :on-change="handleChange"
          :file-list="fileList"
          :list-type="listType"
          :show-file-list="showFileList"
          :on-success="handleAvatarSuccess"
          :on-remove="handleRemove"
          :before-upload="beforeAvatarUpload"
          :limit="limit"
          :on-exceed="onExceed"
        >
          <div v-if="listType == 'picture-card'">
            <div
              slot="file"
              slot-scope="{ file }"
              class="imglist"
            >
              <span class="el-upload-list__item-actions">
                <span
                  class="el-upload-list__item-delete"
                  @click="handleRemove(file)"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <i
              slot="default"
              class="el-icon-plus"
            ></i>
          </div>
          <div v-else>
            <img
              v-if="imageUrl"
              :src="imageUrl"
              class="avatar"
            />
            <span v-else>
              <i
                v-if="headerImg"
                class="img"
              ></i>
              <i
                v-else
                class="el-icon-plus avatar-uploader-icon"
              ></i>
            </span>
          </div>
        </el-upload>
      </el-col>
      <el-col
        v-else
        class="docu-upload"
      >
        <el-upload
          class="avatar-uploader"
          :disabled="disabled"
          :data="params"
          :http-request="httpRequest"
          :on-change="handleChange"
          :multiple="multiple"
          :on-error="handleError"
          :action="action"
          :headers="headers"
          :auto-upload="autoUpload"
          :file-list="fileList"
          :show-file-list="showFileList"
          :on-success="handleAvatarSuccess"
          :on-preview="onPreviewFile"
          :on-remove="handleRemove"
          :on-exceed="onExceed"
          :before-upload="beforeAvatarUpload"
          :limit="limit"
        >
          <el-button
            slot="trigger"
            :disabled="disabled"
            size="small"
            type="primary"
          >{{ uploadBtnName }}</el-button>
        </el-upload>
      </el-col>
    </el-col>
  </el-row>
</template>
<script>
import Axios from '@/router/axios';
import { BASE_PLATFORMLOGO_URL } from '@/util/constant.js';
import { downloadFileUrl } from '@/util/util';
export default {
  props: {
    storageName: String,
    headerImg: Boolean,
    isFile: Boolean,
    borderNone: Boolean,
    isSaveData: Boolean,
    multipleParams: Boolean,
    formFile: String,
    callback: Function,
    params: Object,
    httpurl: {
      type: String,
      default: '',
    },
    fileUrl: {
      type: [String, Array],
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    uploadBtnName: {
      type: String,
      default: '选取文件',
    },
    width: {
      type: String,
      default: '30%',
    },
    limit: Number,
    listType: {
      type: String,
      default: 'text',
    },
    headers: Object,
    dialog: {
      type: Boolean,
      default: true,
    },
    showFileList: {
      type: Boolean,
      default: true,
    },
    autoUpload: {
      type: Boolean,
      default: false,
    },
    multiple: {
      // 是否支持多文件上传
      type: Boolean,
      default: false,
    },
    method: {
      type: String,
      default: 'post',
    },
    action: {
      type: String,
      default: 'https://jsonplaceholder.typicode.com/posts/',
    },
    filePath: String,
  },
  data() {
    return {
      dialogVisible: false,
      fileList: [],
      file: {},
      imageUrl: '',
      fileName: '',
      resultData: '',
    };
  },
  watch: {
    httpurl: {
      handler(val) {
        if (val) {
          this.imageUrl = BASE_PLATFORMLOGO_URL + val;
        }
      },
      immediate: true,
    },
    fileUrl: {
      handler(val) {
        if (val) {
          if (Object.prototype.toString.call(val) === '[object Array]')
            this.fileList = val.map((item) => {
              return { name: item, url: this.$BASEURL + item };
            });
          else this.fileList = [{ name: val.split('/').pop(), url: val }];
        }
      },
      immediate: true,
    },
  },
  methods: {
    httpRequest(param) {
      let fileObj = param.file; // 相当于input里取得的files
      let fd = new FormData(); // FormData 对象
      fd.append(this.formFile || 'file', fileObj); // 文件对象
      //额外参数
      if (this.headers) {
        for (let k in this.headers) {
          fd.append(k, this.headers[k]);
        }
      }
      this.listType === 'picture' &&
        (this.imageUrl = URL.createObjectURL(fileObj));
      if (this.action && !this.isFile) {
        Axios({
          url: this.action,
          method: this.method,
          data: fd,
        }).then((res) => {
          let result = res;
          if (result.data.statusCode === 200) {
            this.resultData = result.data.data[0];
            this.$showMessage('成功', '导入成功', 'success');
            this.dialogVisible = false;
            this.$emit('imgUrl', result.data.data[0]);
            this.isSaveData &&
              setStore({
                name: this.storageName || 'uploadData',
                content: result.data.data[0],
                type: 'session',
              });
            if (this.callback) {
              this.callback(result.data);
            }
          } else {
            if (this.$route.name === 'roster') {
              // 花名册页面导入失败message使用后端返回的errors字段内容
              this.$showMessage('失败', result.data.errors);
            } else this.$showMessage('失败', '导入失败');
          }
        });
      }
      this.$emit('httpRequest', fd, param);
    },

    handleAvatarSuccess(res, file) {
      this.listType === 'picture' &&
        (this.imageUrl = URL.createObjectURL(file.raw));
      this.dialogVisible = false;
      this.$emit('handleAvatarSuccess', res, file);
      if (res.statusCode === 200) {
        this.$showMessage('成功', '导入成功', 'success');
      } else {
        this.$showMessage(
          '失败',
          res.errors || '导入失败，请稍后重试~',
          'error'
        );
      }
    },
    // 点击文件列表中已上传的文件
    onPreviewFile() {
      let fileItem = this.fileList[0];
      if (fileItem && fileItem.raw) {
        let reader = new FileReader();
        reader.readAsArrayBuffer(fileItem.raw);
        reader.onload = () => {
          let blob = new Blob([reader.result]);
          downloadFileUrl(blob, fileItem.name);
        };
      } else {
        // downloadFileUrl(fileItem.url, fileItem.name);
        window.open(this.$BASEURL + fileItem.url)
      }
    },
    beforeAvatarUpload(file) {
      if (this.listType === 'picture' || this.listType === 'picture-card') {
        const typeArr = ['image/png', 'image/gif', 'image/jpeg', 'image/jpg'];
        const isJPG = typeArr.indexOf(file.type) !== -1;
        const isLt3M = file.size / 1024 / 1024 < 3;
        if (!isJPG) {
          this.$message.error('只能上传图片!');
        }
        if (!isLt3M) {
          this.$message.error('上传头像图片大小不能超过 3MB!');
        }
        return isJPG && isLt3M;
      }
    },
    dialogCloseFun() {
      this.dialogVisible = false;
      this.$emit('dialogCloseFun');
      this.$nextTick(() => {
        this.fileList = [];
      });
    },
    submitUpload() {
      if (this.fileList.length > 0) {
        this.$refs.upload.submit();
        this.$emit('submitUpload', this.$refs.upload.submit);
      }
      this.$nextTick(() => {
        this.fileList = [];
      });
      this.dialogVisible = false;
    },
    handleRemove(file, fileList) {
      if (file === 1) {
        this.fileName = '';
        this.fileList = [];
      }
      this.$emit('handleRemove', file, fileList);
    },
    handlePreview(file) {
      this.$emit('handleRemove', file);
    },
    handleError(err, file, fileList) {
      this.$showMessage();
      this.$emit('handleRemove', err, file, fileList);
    },
    handleChange(file) {
      if (!this.multiple) {
        this.fileList = [];
        this.fileList.push(file);
      }
      this.file = file;
      this.fileName = file.name;
      this.$emit('handleChange', file);
    },
    onExceed(file, fileList) {
      this.$emit('onExceed', file, fileList);
    },
  },
};
</script>
<style lang="scss">
.upload .docu-upload {
  .avatar-uploader {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border: 1px solid #fff;
    .el-upload-list {
      max-width: calc(100% - 90px);
    }
  }
}
.el-upload--picture-card {
  width: 0.8rem;
  height: 0.8rem;
  line-height: 0.8rem;
}
.el-upload-list--picture-card .el-upload-list__item {
  width: 0.8rem;
  height: 0.8rem;
}
</style>
<style lang="scss" scoped>
.avatar-uploader-icon {
  width: 1.5rem;
  height: 1.5rem;
  line-height: 1.5rem;
  font-size: 30px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid #ccc;
}
.img {
  display: inline-block;
  width: 100px;
  height: 100px;
  background: url("./../../assets/cardimg.png") no-repeat center;
  background-size: 100% 100%;
}
.avatar {
  width: 100px;
  height: 100px;
}
</style>
