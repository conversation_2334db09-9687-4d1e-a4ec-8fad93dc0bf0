<template>
  <div
    class="baidumap"
    id="allmap"
  >
  </div>
</template>
<script>
import BaiduMap from 'BaiduMap'
export default {
  name: 'bamap',
  components: {},
  data() {
    return {
    }
  },
  mounted: function () {
    document.getElementsByClassName("baidumap")[0].style.height = getStore(
      { name: "routeContentNum" }
    ) - 60 + 'PX';
    this.initMap()
  },
  methods: {
    initMap() {
      var map = new BaiduMap.Map('allmap') // 创建地图实例,这里用到了id选择器
      console.log('----map-----', map)
      var point = new BaiduMap.Point(116.404, 39.915) // 创建点坐标
      map.centerAndZoom(point, 11) // 初始化地图，设置中心点坐标和地图级别
      var marker = new BaiduMap.Marker(point) // 创建标注
      map.addOverlay(marker)    // 将标注添加到地图中

      map.enableScrollWheelZoom(true) // 开启鼠标滚轮缩放
      map.addControl(new BaiduMap.NavigationControl()) // 添加平移缩放控件
      map.addControl(new BaiduMap.ScaleControl()) // 添加比例尺控件
      map.addControl(new BaiduMap.OverviewMapControl()) // 添加缩略地图控件
      //map.setMapStyle({ style: 'midnight' }) //地图风格

      var localSearch = new BaiduMap.LocalSearch(map);
      localSearch.enableAutoViewport(); //允许自动调节窗体大小
      var infoWindow = new BaiduMap.InfoWindow('<p>北京市</p>')
      // 鼠标移上标注点要发生的事
      marker.addEventListener('mouseover', function () {
        this.openInfoWindow(infoWindow)
      })

      // 鼠标移开标注点要发生的事
      marker.addEventListener('mouseout', function () {
        //this.closeInfoWindow(infoWindow)
      })
      //信息窗口内容
      // let content = `<div style="margin:0;line-height:20px;padding:2px;"><img src="${this.content.imgUrl}" alt="" style="float:right;zoom:1;overflow:hidden;width:100px;height:100px;margin-left:3px;"/>
      // 地址：${this.content.address}<br/>电话：${this.content.phone}<br/>简介：${this.content.introduction}
      // 	</div>
      // `;
      //信息窗口标题
      let title = {
        // title  : this.content.title,      //标题
        width: 290,             //宽度
        height: 105,              //高度
        panel: "panel",         //检索结果面板
        enableAutoPan: true,     //自动平移
        searchTypes: [
          BMAPLIB_TAB_FROM_HERE, //从这里出发
          BMAPLIB_TAB_SEARCH,   //周边检索
          BMAPLIB_TAB_TO_HERE,  //到这里去
        ]
      }
      // //创建一个信息窗口实例
      // let searchInfoWindow = new BMapLib.SearchInfoWindow(map,content, title);
      // searchInfoWindow.open(marker);
      // //点击红点弹出信息窗口
      // marker.addEventListener("click", function(e){
      // 	searchInfoWindow.open(marker);
      // })
      // console.log(BMap)
    }
  }
};
</script>

<style>
.baidumap {
  width: 100%;
  /* height: 700px; */
}
#allmap {
  /* height:700px; */
  width: 100%;
}
</style>
