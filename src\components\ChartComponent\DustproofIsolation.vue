<!--
 * @Description: 防尘隔离
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-08-01 15:57:05
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <!-- <p class="countStyle">
        {{ $t("customization.totalNum") }}：{{ totalCount }}
      </p> -->
      <div
        class="box"
        ref="DustproofIsolationRef"
      >
        <div
          id="DustproofIsolationChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getDustproofIsolation } from "@/api/echrtsApi";
export default {
  components: {},
  name: "DustproofIsolation",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "DustproofIsolationChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.dustIsolationTotal"),
        seriesCenter: ["25%", "58%"],
        richNameWidth: 60,
        noTooltipShow: true, //不显示
        itemStyleEmphasis: {
          label: {
            show: true,
            // position: 'center',
            x: "20%",
            y: "10%",
            textStyle: {
              rich: {
                numText: {
                  color: "#fff",
                  fontSize: 13,
                  width: 30,
                  textAlign: "center",
                },
                text: {
                  color: "#fff",
                  fontSize: 13,
                  padding: [0, 0, 10, 0],
                  width: 30,
                  textAlign: "center",
                },
              },
            },
            formatter: function (params) {
              return `{text| ${params.name}}\n{numText|${params.value}}`;
            },
          },
        },
        costomLegendFormatter:function(name){
          return name;
        },
      },
      totalCount: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.DustproofIsolationRef.offsetWidth;
      this.barHeight = this.$refs.DustproofIsolationRef.offsetHeight;
    },
    getBarData() {
      getDustproofIsolation()
        .then((res) => {
          const { status, data } = res;
          if (status == 200) {
            this.setEcharts(data);
          }
        })
        .catch(() => { });
    },
    setEcharts(val) {
      let dataList = val;
      // this.pieParams.titleInfor.text = totalCount;
      if (dataList.length > 0) {
        let legendFormatter = (name) => {
          const item = dataList.find((i) => {
            return i.name === name;
          });
          const p = item.value;
          let clientWidth = document.documentElement.clientWidth;
          let newName = (newName =
            name.length > 12 ? name.slice(0, 12) + "..." : name);
          if (clientWidth < 1900) {
            newName = name.slice(0, 5) + "...";
            this.pieParams.richNameWidth = 60;
          }
          return "{name|" + newName + "}" + "{percent|" + p + "}";
        };
        this.pieParams.legendFormatter = legendFormatter;

        let sum = 0;
        dataList.forEach((ele) => {
          sum = Number(sum) + Number(ele.value);
        });
        this.totalCount = sum;
      }
              this.pieParams.data = dataList.map((item) => {
                switch (item.name) {
                  case "电动绿网防尘天幕":
            item.name = this.$t("customization.electNet");
            break;
          case "防尘隔离棚":
            item.name = this.$t("customization.dustIsolationShed");
            break;
          case "电动防尘天幕":
            item.name = this.$t("customization.dustIsolationCanopy");
            break;
        }
        return item;
      });

      drawAnnularChart(this.pieParams);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
