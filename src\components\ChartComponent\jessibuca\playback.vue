<template>
  <div class="player-option-box">
    <div>
      <!-- <el-button-group>
        <el-button
          size="mini"
          class="iconfont icon-zanting"
          title="开始"
          @click="gbPause()"
        >哪里</el-button>
        <el-button
          size="mini"
          class="iconfont icon-kaishi"
          title="暂停"
          @click="gbPlay()"
        ></el-button>
        <el-dropdown
          size="mini"
          title="播放倍速"
          @command="gbScale"
        >
          <el-button size="mini">
            倍速 <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="0.25">0.25倍速</el-dropdown-item>
            <el-dropdown-item command="0.5">0.5倍速</el-dropdown-item>
            <el-dropdown-item command="1.0">1倍速</el-dropdown-item>
            <el-dropdown-item command="2.0">2倍速</el-dropdown-item>
            <el-dropdown-item command="4.0">4倍速</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          size="mini"
          class="iconfont icon-xiazai1"
          title="下载选定录像"
          @click="downloadRecord()"
        ></el-button>
        <el-button
          v-if="sliderMIn === 0 && sliderMax === 86400"
          size="mini"
          class="iconfont icon-slider"
          title="放大滑块"
          @click="setSliderFit()"
        ></el-button>
        <el-button
          v-if="sliderMIn !== 0 || sliderMax !== 86400"
          size="mini"
          class="iconfont icon-slider-right"
          title="恢复滑块"
          @click="setSliderFit()"
        ></el-button>
      </el-button-group> -->
    </div>

    <!-- :disabled="detailFiles.length === 0" -->
    <el-slider
      class="playtime-slider"
      v-model="playTime"
      id="playtimeSlider"
      :min="sliderMIn"
      :max="sliderMax"
      :range="true"
      :format-tooltip="playTimeFormat"
      @change="playTimeChange"
      :marks="playTimeSliderMarks"
    >
    </el-slider>

  </div>
</template>

<script>
export default {
  data() {
    return {
      playTime: null,
      sliderMIn: 0,
      sliderMax: 86400,
      playTimeSliderMarks: {
        0: "00:00",
        3600: "01:00",
        7200: "02:00",
        10800: "03:00",
        14400: "04:00",
        18000: "05:00",
        21600: "06:00",
        25200: "07:00",
        28800: "08:00",
        32400: "09:00",
        36000: "10:00",
        39600: "11:00",
        43200: "12:00",
        46800: "13:00",
        50400: "14:00",
        54000: "15:00",
        57600: "16:00",
        61200: "17:00",
        64800: "18:00",
        68400: "19:00",
        72000: "20:00",
        75600: "21:00",
        79200: "22:00",
        82800: "23:00",
        86400: "24:00",
      },
    }
  },
  methods: {
    playTimeChange(val) {
      console.log(val)

      let startTimeStr = moment(new Date(this.chooseDate + " 00:00:00").getTime() + val[0] * 1000).format("YYYY-MM-DD HH:mm:ss");
      let endTimeStr = moment(new Date(this.chooseDate + " 00:00:00").getTime() + val[1] * 1000).format("YYYY-MM-DD HH:mm:ss");

      this.setTime(startTimeStr, endTimeStr)

      this.playRecord();
    },
    gbPlay() {
      console.log('前端控制：播放');
      this.$axios({
        method: 'get',
        url: '/api/playback/resume/' + this.streamId
      }).then((res) => {
        this.$refs["recordVideoPlayer"].play(this.videoUrl)
      });
    },
    gbPause() {
      console.log('前端控制：暂停');
      this.$axios({
        method: 'get',
        url: '/api/playback/pause/' + this.streamId
      }).then(function (res) { });
    },
    gbScale(command) {
      console.log('前端控制：倍速 ' + command);
      this.$axios({
        method: 'get',
        url: `/api/playback/speed/${this.streamId}/${command}`
      }).then(function (res) { });
    },
    downloadRecord: function (row) {
      if (!row) {
        let startTimeStr = moment(new Date(this.chooseDate + " 00:00:00").getTime() + this.playTime[0] * 1000).format("YYYY-MM-DD HH:mm:ss");
        let endTimeStr = moment(new Date(this.chooseDate + " 00:00:00").getTime() + this.playTime[1] * 1000).format("YYYY-MM-DD HH:mm:ss");
        console.log(startTimeStr);
        console.log(endTimeStr);
        row = {
          startTime: startTimeStr,
          endTime: endTimeStr
        }
      }
      if (this.streamId !== "") {
        this.stopPlayRecord(() => {
          this.streamId = "";
          this.downloadRecord(row);
        })
      } else {
        this.$axios({
          method: 'get',
          url: '/api/gb_record/download/start/' + this.deviceId + '/' + this.channelId + '?startTime=' + row.startTime + '&endTime=' +
            row.endTime + '&downloadSpeed=4'
        }).then((res) => {
          if (res.data.code === 0) {
            let streamInfo = res.data.data;
            this.$refs.recordDownload.openDialog(this.deviceId, this.channelId, streamInfo.app, streamInfo.stream, streamInfo.mediaServerId);
          } else {
            this.$message({
              showClose: true,
              message: res.data.msg,
              type: "error",
            });
          }
        });
      }
    },
    setSliderFit() {
      if (this.sliderMIn === 0 && this.sliderMax === 86400) {
        if (this.detailFiles.length > 0) {
          let timeForFile = this.getTimeForFile(this.detailFiles[0]);
          let lastTimeForFile = this.getTimeForFile(this.detailFiles[this.detailFiles.length - 1]);
          let timeNum = timeForFile[0].getTime() - new Date(this.chooseDate + " " + "00:00:00").getTime()
          let lastTimeNum = lastTimeForFile[1].getTime() - new Date(this.chooseDate + " " + "00:00:00").getTime()

          this.playTime = parseInt(timeNum / 1000)
          this.sliderMIn = parseInt(timeNum / 1000 - timeNum / 1000 % (60 * 60))
          this.sliderMax = parseInt(lastTimeNum / 1000 - lastTimeNum / 1000 % (60 * 60)) + 60 * 60

          this.playTime = [this.sliderMIn, this.sliderMax];
        }
      } else {
        this.sliderMIn = 0;
        this.sliderMax = 86400;
      }
    },
  }
}
</script>