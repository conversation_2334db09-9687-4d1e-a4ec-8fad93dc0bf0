<!--
 * @Description: 质量检查
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-25 15:20:57
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">
      {{ moduleName }}
    </div>
    <div class="areaContent">
      <div
        class="box"
        ref="QualityTestRef"
      >
        <div
          id="QualityTestChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>

       <p class="dateStyle">
        <span
          :class="barActive == items.value ? 'active' : null"
          v-for="(items, key) in barList"
          :key="key"
          @click="setActive(items.value)"
        > {{ items.name }}</span>
      </p>
    </div>
  </div>
</template>
<script>

import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getQualityTest } from "@/api/echrtsApi";
export default {
  components: {},
  name: "QualityTest",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "QualityTestChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.qualityTestTotal"),
        seriesCenter: ["25%", "58%"],
        titleInfor: {
          text: "0",
          subtext: this.$t("customization.qualityTotal"),
          x: "30%",
          y: "30%",
          textAlign: "center",
          textStyle: {
            color: "#fff",
            fontSize: 20,
            // fontStyle: 'bolder',
          },
          subtextStyle: {
            color: "#fff",
            fontSize: 12,
          },
          // left: 'center',
        },
        tooltipFormatter: `{b}<br /> ${this.$t("number")}：{c}<br />${this.$t(
          "Proportion"
        )}：{d}%`,
        richNameWidth: 56,
        costomLegendFormatter: function (name) {
          return name;
        },
      },
      barList: [
        {
          name: this.$t("customization.week"),
          value: 1,
        },
        {
          name: this.$t("customization.month"),
          value: 2,
        },
        {
          name: this.$t("customization.year"),
          value: 3,
        },
      ],
      barActive: 1,
      dataAllList: [{}, {}, {}],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    // bar选择
    setActive(val) {
      this.barActive = val;
      const { dataAllList } = this;
      this.setData(dataAllList[val - 1]);
    },
    setEchartsWidth() {
      this.barWidth = this.$refs.QualityTestRef.offsetWidth - 40;
      this.barHeight = this.$refs.QualityTestRef.offsetHeight;
    },
    getBarData() {
      getQualityTest(1)
        .then((res) => {
          const { statusCode, data } = res.data;
          if (statusCode == 200) {
            this.setData(data);
            this.dataAllList[0] = data;
          }
        })
        .catch(() => { });
      getQualityTest(2)
        .then((res) => {
          const { statusCode, data } = res.data;
          if (statusCode == 200) {
            this.dataAllList[1] = data;
          }
        })
        .catch(() => { });
      getQualityTest(3)
        .then((res) => {
          const { statusCode, data } = res.data;
          if (statusCode == 200) {
            this.dataAllList[2] = data;
          }
        })
        .catch(() => { });
    },
    setData(val) {
      let { list: dataList, totalCount } = val;
      if (dataList.length > 0) {
        let legendFormatter = (name) => {
          const item = dataList.find((i) => {
            return i.name === name;
          });
          const p = item.value;
          let clientWidth = document.documentElement.clientWidth;
          let newName = name.length > 7 ? name.slice(0, 8) + "..." : name;
          if (clientWidth < 1900) {
            newName = name.slice(0, 5) + "...";
            this.pieParams.richNameWidth = 56;
          }
          return "{name|" + newName + "}" + "{percent|" + p + "}";
        };
        this.pieParams.legendFormatter = legendFormatter;
      }

      this.pieParams.titleInfor.text = totalCount || 0;
      this.pieParams.data =
        dataList.map((item) => {
          switch (item.name) {
            case "合格":
              item.name = this.$t("customization.qualified");
              break;
            case "不合格已整改":
              item.name = this.$t("customization.unQuaDone");
              break;
            case "不合格待整改":
              item.name = this.$t("customization.unQuaUn");
              break;
          }
          return item;
        }) || [];
      drawAnnularChart(this.pieParams);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
