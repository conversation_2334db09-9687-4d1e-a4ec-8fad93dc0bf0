<!--
 * @Description: 视频管理
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-24 16:03:59
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text"><span @click="goUrl">{{ moduleName }}</span>
      <div class="barStyle">
        <el-select
          v-model="videoValue"
          placeholder="请选择"
          @change="videoChange"
          id="select"
        >
          <el-option
            v-for="item in videoOptions"
            :key="item.id"
            :label="item.deviceName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="areaContent">
      <div
        class="box"
        ref="VideoRef"
      >
        <Player
          v-if="curPlayVideo.deviceSerial  && curPlayVideo.platformType <= 1"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
          :key="curPlayVideo.id"
          :videoId="curPlayVideo.id"
        />
        <JessibucaPlayer
          v-if="curPlayVideo.deviceSerial && curPlayVideo.platformType == 2"
          ref="recordVideoPlayer"
          :videoUrl="jessVideoUrl"
          :videoId="curPlayVideo.id"
          :error="videoError"
          :message="videoError"
          :showRecordBtn="true"
          :showPreset="true"
          @screenshot="shot"
          fluent
          autoplay
          live
        ></JessibucaPlayer>
      </div>
    </div>
  </div>
</template>
<script>
import { gitVideoList } from '@/api/video.js';
import Player from '@/components/videoPage/ezuikit/index.vue'
import JessibucaPlayer from './jessibuca';
export default {
  components: {
    Player,
    JessibucaPlayer
  },
  name: "SmartSmoke",
  props: {
    moduleName: String,
  },
  data() {
    return {
      userId: "",
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      videlList: '',
      videoOptions: [],
      videoValue: '',
      videoUrl: '',
      curPlayVideo: {},
      jessVideoUrl: '',
    };
  },
  created() {
    this.userId = getStore({ name: 'userId' });
    this.projectId = getStore({
      name: "projectId"
    });
    this.companyId = getStore({
      name: "companyId"
    });
    this.getAllVideo();
  },
  watch: {
    "$i18n.locale"(val) {
      if (val) {
        this.language = val
        if (this.$IsProjectShow) {
          if (this.language === 'zh') {
            translate.changeLanguage('chinese_simplified')
          }
          if (this.language === 'en') {
            translate.changeLanguage('english')
          }
          translate.execute() //进行翻译
          setTimeout(() => {
            this.getAllVideo();
          }, 100)

        }
      }
    },
  },
  mounted() {
    this.setEchartsWidth();
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.VideoRef.offsetWidth - 40;
      this.barHeight = this.$refs.VideoRef.offsetHeight;
    },
    videoChange(val) {
      const arr = this.videoOptions.filter(item => { return item.id === val })
      this.curPlayVideo = arr && arr[0]
      this.jessVideoUrl = this.curPlayVideo.hdLiveEzOpenUrl
    },
    shot(e) {
      var base64ToBlob = function (code) {
        let parts = code.split(';base64,');
        let contentType = parts[0].split(':')[1];
        let raw = window.atob(parts[1]);
        let rawLength = raw.length;
        let uInt8Array = new Uint8Array(rawLength);
        for (let i = 0; i < rawLength; ++i) {
          uInt8Array[i] = raw.charCodeAt(i);
        }
        return new Blob([uInt8Array], {
          type: contentType
        });
      };
      let aLink = document.createElement('a');
      let blob = base64ToBlob(e); //new Blob([content]);
      let evt = document.createEvent("HTMLEvents");
      evt.initEvent("click", true, true); //initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
      aLink.download = '截图';
      aLink.href = URL.createObjectURL(blob);
      aLink.click();
    },
    videoError: function (e) {
      console.log("播放器错误：" + JSON.stringify(e));
    },
    // 获取视频列表
    getAllVideo() {
      gitVideoList({
        projectId: this.projectId,
        pageSize: 99999,
        pageIndex: 1,
        name: '',
      }).then((res) => {
        this.videoOptions = res.data.data.videos;
        this.curPlayVideo = this.videoOptions.length && this.videoOptions[0]
        this.videoValue = this.videoOptions.length && this.videoOptions[0].id
        this.jessVideoUrl = this.videoOptions.length && this.videoOptions[0].hdLiveEzOpenUrl
      });
    },
    goUrl() {//跳转视频监控
      window.open(
        `${window.location.protocol}//${window.location.hostname}:8100/?userId=${this.userId} &projectId=${this.projectId}&companyId=${this.companyId}`
      );
    }
  }
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

//改变el-select的宽高
.barStyle {
  right: 28px !important;
  top: 6px !important;
}
.barStyle /deep/ {
  #select {
    max-width: 180px;
    min-width: 160px;
    height: 18px;
    position: relative;
    top: -5px;
  }
  .el-input--suffix {
    height: 24px;
    border: 0;
  }
  .el-input__icon {
    line-height: 24px;
    position: relative;
    top: -5px;
  }
  .el-select .el-input__inner {
    background: url("~@/assets/rectangle_select.png") no-repeat !important;
    background-size: 100% 100% !important;
  }
}
</style>
