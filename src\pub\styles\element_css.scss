/* 改变主题色变量 */
$--color-primary: #0b3472;
$--color-bg: rgb(13, 1, 44);
$--color-border: rgb(13, 1, 44);
$--color-opt: #d2ce1a;
$--color-font: #fff;
/* 改变 icon 字体路径变量，必需 */
$--font-path: '~element-ui/lib/theme-chalk/fonts';
@import "~element-ui/packages/theme-chalk/src/index";

.bg {
  background: $--color-bg;
}

.font-color-white {
  color: $--color-font;
}

.font-color-opt {
  color: $--color-opt;
}

.el-button {
  background: $--color-bg;
  border: 1px solid $--color-border;
}

.list-table {
  .el-button {
    background: none;
    border: none;
  }

  .el-dropdown {
    color: $--color-opt;
  }

  .el-dropdown-menu {
    background: $--color-primary;
  }

}

.table-bottom-page {
  .el-pagination__editor.el-input .el-input__inner {
    height: 26px;
  }
}

.el-input.is-disabled .el-input__inner,
.el-table,
.el-table td,
.el-table th,
.el-select-dropdown,
.el-pagination__sizes .el-input__inner,
.el-pager li,
.el-pagination__jump .el-input__inner,
.el-dialog,
.el-input .el-input__inner,
.el-select .el-input__inner,
.el-range-input,
.el-date-editor,
.el-picker-panel__content,
.available,
.el-tree,
.el-popper,
.el-dropdown-menu__item,
.el-pagination button:disabled {
  background: $--color-primary;
  color: $--color-font;
}

.el-table th .cell,
.el-table-column--selection .cell {
  text-align: center;
}

.el-upload-list,
.el-icon-close,
.el-upload-list__item-name,
.el-upload-list__item-name .el-icon-document,
.el-radio__label,
.el-dialog__body,
.el-table td a,
.el-select-dropdown__item,
.el-pagination__total,
.el-pagination__jump,
.el-icon-close:before,
.el-date-picker__header .el-buttonn,
.el-date-picker__header-label,
.el-date-range-picker__header .el-button,
.el-range-separator,
.el-range-input,
.el-range__icon,
.el-picker-panel__icon-btn,
.el-date-table th,
.el-date-table td.today span,
.el-form-item__label,
.el-pager li.active {
  color: $--color-font !important;
}

.el-date-table td.today {
  border-radius: 50%;
}

// .el-date-table td:hover{
//     color: rgb(13, 1, 44) !important;
// }
.el-date-table td:hover,
.el-upload-list__item:hover,
.el-table--striped .el-table__body tr.el-table__row--striped td,
.el-table tr:hover>td,
.el-tree .el-tree-node__content:hover,
.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content,
.el-select-dropdown__item:hover,
.el-dropdown-menu__item:hover,
.el-select-dropdown__item.hover,
.el-date-table .in-range,
.el-date-table .in-range div,
.el-date-table td.current:not(.disabled) span,
.el-select-dropdown__item.selected {
  border-radius: 0 !important;
  background: $--color-bg !important;
}

.el-table td .el-button {
  color: $--color-opt;
}

.el-table td,
.el-table th {
  padding: 0 !important;
}

.el-table th {
  height: 35px;
}

.el-table .cell {
  height: 30px;
  padding: 0 5px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: $--color-bg;
  border-radius: 3px;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: $--color-primary;
  border-radius: 6px;
}

//滚动条的滑块
::-webkit-scrollbar-thumb {
  background-color: $--color-bg;
  border-radius: 6px;
}

.el-dialog__body,
.el-dialog__header {
  background: url('./../../assets/hmc_tc_bg.png') repeat center;
  border: 1px solid #2f65ed;
  //  box-shadow: 10px 10px 10px 3px royalblue;
}

.el-dialog__header {
  border-bottom: none;
}

.el-dialog__body {
  border-top: none;
}

.el-input {
  border: 1px solid #fff;
}

.el-input__inner {
  border: none;
}

.el-range-editor.el-input__inner,
.el-range-editor.is-active:hover {
  border: 1px solid #fff;
}

.edit-cell .list-table {
  .el-input__suffix {
    top: 5px;
  }

  .el-input__inner {
    height: 20px;
  }

  .cell {
    padding: 2px 5px;
  }

  .el-input__suffix {
    display: none;
  }
}

.el-pagination__editor.el-input {
  border: none !important;
}