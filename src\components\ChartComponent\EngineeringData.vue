<!--
 * @Description: 工程资料
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-24 17:18:23
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div
      class="areaContent"
      @mouseenter="mouseenterEvent"
      @mouseleave="mouseleaveEvent"
    >
      <div class="box" ref="EngineeringDataRef">
        <div
          id="EngineeringDataChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>

import { drawLineStackShadow } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getEngineering } from "@/api/echrtsApi";
import * as echarts from "echarts";
export default {
  components: {},
  name: "EngineeringData",
  props: {
    moduleName: String,
    allInfor: {
      type: Object,
    },
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      userId: "",
      token: "",
      barWidth: null,
      barHeight: null,
      lineParams: {
        dom: "EngineeringDataChart",
        xAxisData: [],
        seriesData: [],
        // isMoreLine: true,
        yminInterval: 1,
        boundaryGap: true,
        grid: {
          top: "15%",
          left: "15%",
          right: "5%",
          bottom: "10%"
        },
        tooltipFormatter: (val) => {
          let msg = "";
          if (val.length) {
            msg = `${val[0].axisValue}<br/>${val[0].marker} ${this.$t("number")}
              <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
              <b>${val[0].data}</b>`;
          }
          return msg;
        },
      },
      timerFlag: null,
      arr: [],
      xInfor: [],
      yInfor: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.userId = getStore({ name: "userId" });
    this.token = getStore({ name: "token" });
    this.getData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.EngineeringDataRef.offsetWidth;
      this.barHeight = this.$refs.EngineeringDataRef.offsetHeight;
    },
    getData() {
      const { projectId, companyId, userId, allInfor, token } = this;

      getEngineering({
        diffDays: 10,
        token: token,
        zhType: allInfor.moduleSetting,
        companyId: companyId,
        zhgdUserId: userId,
        projectId: projectId,
      })
        .then((res) => {
          let result = res.data;
          const {
            data: { xAxis, series },
            statusCode,
          } = result;
          if (statusCode == 200) {
            this.xInfor = xAxis.Data;
            this.yInfor = series;
            this.rotationMethods(xAxis.Data, series);
          }
        })
        .catch(() => {});
    },
    rotationMethods() {
      const { xInfor: names, yInfor: series } = this;
      let lengthNumber = Math.ceil(names.length / 7);
      // let counts = series.Data
      // counts.Data
      let index = 1;
      this.activeData(names.slice((index - 1) * 7, index * 7), {
        Name: series[0].Name,
        Data: series[0].Data.slice((index - 1) * 7, index * 7),
      });
      this.timerFlag = setInterval(() => {
        this.currentIndex = index;
        index++;
        if (index > lengthNumber) {
          index = 1;
        }
        this.activeData(names.slice((index - 1) * 7, index * 7), {
          Name: series[0].Name,
          Data: series[0].Data.slice((index - 1) * 7, index * 7),
        });
      }, 2000);
    },
    // 鼠标移入
    mouseenterEvent() {
      if (this.timerFlag) {
        clearInterval(this.timerFlag);
        this.timerFlag = null;
      }
    },
    // 鼠标移出
    mouseleaveEvent() {
      const { xInfor: names, yInfor: series } = this;
      let lengthNumber = Math.ceil(names.length / 7);
      let index = this.currentIndex;
      this.timerFlag = setInterval(() => {
        this.currentIndex = index;
        index = index + 1;
        if (index > lengthNumber) {
          index = 1;
        }
        this.activeData(names.slice((index - 1) * 7, index * 7), {
          Name: series[0].Name,
          Data: series[0].Data.slice((index - 1) * 7, index * 7),
        });
      }, 2000);
    },
    activeData(xAxisData, series) {
      let seriesData = [];
      seriesData.push({
        name: series.Name,
        data: series.Data,
        type: "line",
        lineStyle: {
          normal: {
            color: "#4f89ef",
          },
        },
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "#0e4d98",
            },
            {
              offset: 1,
              color: "#0b3574",
            },
          ]),
        },
        label: {
          normal: {
            show: true,
            position: "top",
            textStyle: {
              fontSize: 10,
            },
            formatter: function (num) {
              return num.value > 0 ? num.value : "";
            },
          },
        },
      });
      this.lineParams.xAxisData = xAxisData;
      this.lineParams.seriesData = seriesData;
      
      drawLineStackShadow(this.lineParams);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
