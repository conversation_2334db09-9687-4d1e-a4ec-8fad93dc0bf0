/*
 * @Description: 
 * @Author: 
 * @Date: 2022-07-22 10:39:35
 * @LastEditTime: 2023-02-01 11:27:49
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Usage: 
 */
import Axios from '@/router/axios'
// 获取视频监控列表
export function gitVideoList(query) {
  return Axios({
    url: `/api/video-module/list`,
    method: 'post',
    data: query
  })
}

/**
 * 获取视频详情，萤石assetsToken通过视频详情接口获取 -- 刘贺
 * <AUTHOR>
 */
export function getVideoDetail(videoId) {
  return Axios({
    url: `/api/video-module/${videoId}`,
    method: "get",
  });
}
