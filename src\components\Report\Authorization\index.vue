<template>
  <el-row>
    <div id="nav-bar">
      <div class="nav">
        <span
          v-for="item in list"
          :key="item.id"
          :class="item.actived === 1 ? 'active' : ''"
          @click="itemClick(item)"
        >
          {{ item.name }}
        </span>
      </div>
      <el-col>
        <component :is="componentId"></component>
      </el-col>
    </div>
    <div class="btn-box">
      <div
        class="goback"
        v-if="isShow"
        @click="goBack"
      >
        <img src="@/assets/goback.png" />
        <span>返回驾驶舱</span>
      </div>
      <div
        v-show="tempShow"
        class="goback"
      >
        <img src="@/assets/openyq.png" />
      </div>
      <div
        class="goback"
        v-if="isCompanyShow"
        @click="goCompany"
      >
        <img src="@/assets/goback.png" />
        <span>返回企业级</span>
      </div>
    </div>
  </el-row>
</template>
<script>
import evidencesItem from './evidencesItem/index.vue';
import setManage from './setManage/index.vue';
import { COMPANY_URL, PROJECT_URL } from '@/util/constant'
export default {
  name: "App",
  components: {
    evidencesItem,
    setManage
  },
  data() {
    return {
      loading: false,
      isShow: true,
      tempShow: false,
      isCompanyShow: false,
      list: [
        {
          id: "evidencesItem",
          name: "佐证资料",
          actived: 1,
          isClick: true,
        },
        {
          id: "setManage",
          name: "设置中心",
          actived: 0,
          isClick: false,
        },

      ],
      actived: false,
      componentId: 'evidencesItem'
    };
  },
  watch: {
  },
  mounted() {
  },
  created() {
    if (getStore({ name: "userType" }) == 7) {
      this.isCompanyShow = true;
    }
  },
  methods: {
    itemClick(val) {
      this.list.forEach((ele) => {
        if (ele.id == val.id) {
          ele.actived = 1;
          ele.isClick = true;
        } else {
          ele.actived = 0;
          ele.isClick = false;
        }
      });
      this.componentId = val.id

    },
    goBack() {
      const userId = getStore({
        name: "userId"
      });
      const projectId = getStore({
        name: "projectId"
      });
      const companyId = getStore({
        name: "companyId"
      });
      location.href =
        `${PROJECT_URL}/?userId=${userId}&companyId=${companyId}&projectId=${projectId}#/constructionRecord`
    },
    goCompany() {
      const userId = getStore({ name: "userId" });
      const companyId = getStore({ name: "companyId" });
      const projectId = 0
      const language = getStore({ name: "language" });
      location.href = `${projectId ? PROJECT_URL : COMPANY_URL}/?userId=${userId}&companyId=${companyId}&projectId=${projectId}&language=${language}`;
    },
  },
};

</script>

<style lang="scss" scoped>
#nav-bar {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  // z-index: 11;
  background: url(../../../assets/bg.png) no-repeat center;
  background-size: 100% 100%;
  width: 100%;
  text-align: center;
  margin-left: 2%;
  margin-top: 10px;
  .nav {
    padding: 20px 0;
    padding-left: 10px;
    box-sizing: border-box;
    overflow-x: auto;
    span {
      // width: 140px;
      // height: 50px;
      padding: 10px 10px;
      line-height: 40px;
      text-align: center;
      margin-right: 20px;
      background: #22386d;
      color: #61d2f7;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
    }
    .active {
      background: #2f65ec !important;
    }
  }
}
</style>
