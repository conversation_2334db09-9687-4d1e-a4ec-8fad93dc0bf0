<!--
 * @Description: 升降机监测
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-08-01 15:12:23
 * @LastEditors: dongqi<PERSON>qian
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="ElevatorSurveyRef"
      >
        <div
          id="ElevatorSurveyChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
      <p class="barCStyle">
        <span
          :class="barActive == items ? 'active' : null"
          @click="setActive(items)"
          v-for="(items, key) in barList"
          :key="key"
        >{{ items }}</span>
      </p>
      <!-- <p class="countStyle">{{
            $t(`total`)
          }}{{ barActive }} {{
            $t(`Accumulated`)
          }}：{{ totalCount }}</p> -->
    </div>
  </div>
</template>
<script>

import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getElevatorSurvey } from "@/api/echrtsApi";
export default {
  components: {},
  name: "ElevatorSurvey",
  props: {
    moduleName: String
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "ElevatorSurveyChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.elevatorMonitoringTotal"),
        seriesCenter: ["25%", "58%"],
        richNameWidth: 40,
        noTooltipShow: true, //不显示
        itemStyleEmphasis: {
          label: {
            show: true,
            // position: 'center',
            x: "20%",
            y: "10%",
            textStyle: {
              rich: {
                numText: {
                  color: "#fff",
                  fontSize: 13,
                  width: 30,
                  textAlign: "center"
                },
                text: {
                  color: "#fff",
                  fontSize: 13,
                  padding: [0, 0, 10, 0],
                  width: 30,
                  textAlign: "center"
                }
              }
            },
            formatter: function (params) {
              return `{text| ${params.name}累计：${params.value
                }}\n{numText|占比： ${params.percent || 0}%}`;
            }
          }
        }
        , costomLegendFormatter: function (name) {
          return name;
        },
      },
      forcastStatistic: [], // 预警数据
      alarmStatistic: [], // 报警数据
      forcastNumber: 0,
      alarmNumber: 0,
      barList: [this.$t("earlywarning"), this.$t("giveAlarm")],
      barActive: this.$t("earlywarning"),
      totalCount: 0,
      language: 'zh'
    };
  },
  watch: {
    "$i18n.locale"(val) {
      if (val && this.$IsProjectShow) {
        this.language = val
        this.languageChange();
      }
    },
  },
  created() {
    this.projectId = getStore({
      name: "projectId"
    });
    this.companyId = getStore({
      name: "companyId"
    });
    this.language = getStore({
      name: "language"
    });
    if (this.$IsProjectShow) {
      this.languageChange()
    } else {
      this.getBarData();
    }
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    languageChange() {
      if (this.language == 'en') {
        this.pieParams = {
          dom: "ElevatorSurveyChart",
          data: [],
          nameTitle: null,
          seriesLabel: false,
          subtext: "总预警数",
          seriesCenter: ["25%", "50%"],
          richNameWidth: 40,
          noTooltipShow: true, //不显示
          itemStyleEmphasis: {
            label: {
              show: true,
              // position: 'center',
              x: "20%",
              y: "10%",
              textStyle: {
                rich: {
                  numText: {
                    color: "#fff",
                    fontSize: 13,
                    width: 30,
                    textAlign: "center"
                  },
                  text: {
                    color: "#fff",
                    fontSize: 13,
                    padding: [0, 0, 10, 0],
                    width: 30,
                    textAlign: "center"
                  }
                }
              },
              formatter: function (params) {
                return `{text| ${params.name}Accumulated：${params.value
                  }}\n{numText|Proportion： ${params.percent || 0}%}`;
              }
            }
          }
        }
      } else {
        this.pieParams = {
          dom: "ElevatorSurveyChart",
          data: [],
          nameTitle: null,
          seriesLabel: false,
          seriesRadius: ["54%", "84%"],
          seriesCenter: ["30%", "55%"],
          richNameWidth: 40,
          noTooltipShow: true, //不显示
          itemStyleEmphasis: {
            label: {
              show: true,
              // position: 'center',
              x: "20%",
              y: "10%",
              textStyle: {
                rich: {
                  numText: {
                    color: "#fff",
                    fontSize: 13,
                    width: 30,
                    textAlign: "center"
                  },
                  text: {
                    color: "#fff",
                    fontSize: 13,
                    padding: [0, 0, 10, 0],
                    width: 30,
                    textAlign: "center"
                  }
                }
              },
              formatter: function (params) {
                return `{text| ${params.name}累计：${params.value
                  }}\n{numText|占比： ${params.percent || 0}%}`;
              }
            }
          }
          , costomLegendFormatter: function (name) {
            return name;
          },
        }
      }
      this.barList[0] = this.$t("earlywarning");
      this.barList[1] = this.$t("giveAlarm");
      this.barActive = this.$t("earlywarning");
      this.getBarData();
    },
    setEchartsWidth() {
      this.barWidth = this.$refs.ElevatorSurveyRef.offsetWidth;
      this.barHeight = this.$refs.ElevatorSurveyRef.offsetHeight;
    },
    getBarData() {
      getElevatorSurvey(1)
        .then(res => {
          const {
            statusCode,
            data: { dataList, totalCount }
          } = res.data;
          if (statusCode == 200) {
            this.forcastStatistic = dataList;
            this.forcastNumber = totalCount;
            this.totalCount = totalCount;
            this.setEcharts(dataList);
          }
        })
        .catch(() => { });
      getElevatorSurvey(2)
        .then(res => {
          const {
            statusCode,
            data: { dataList, totalCount }
          } = res.data;
          if (statusCode == 200) {
            this.alarmNumber = totalCount;
            this.alarmStatistic = dataList;
          }
        })
        .catch(() => { });
    },
    setActive(val) {
      const { forcastStatistic, alarmStatistic } = this;
      this.barActive = val;
      if (val == this.$t("earlywarning")) {
        this.setEcharts(forcastStatistic);
        this.totalCount = this.forcastNumber;
      } else {
        this.setEcharts(alarmStatistic);
        this.totalCount = this.alarmNumber;
      }
    },
    setEcharts(val) {
      let dataList = val;
      dataList.forEach(ele => {
        switch (ele.name) {
          case "载重":
            ele.name = this.$t("customization.elevatorLoad");
            break;
          case "倾斜":
            ele.name = this.$t("customization.elevatorIncline");
            break;
          case "速度":
            ele.name = this.$t("customization.elevatorSpeed");
            break;
          case "高度":
            ele.name = this.$t("customization.elevatorHeight");
            break;
        }
      });
      // this.pieParams.titleInfor.text = totalCount;
      this.pieParams.data = dataList;

      drawAnnularChart(this.pieParams);
    }
  }
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
