<!--
 * @Description: 现场试验
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-25 15:36:53
 * @LastEditors: dong<PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">
      {{ moduleName }}
    </div>
    <div class="areaContent">
      <div
        class="box"
        ref="FieldTestRef"
      >
        <div
          id="FieldTestChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>

      <p class="dateStyle">
        <span
          :class="barActive == items.value ? 'active' : null"
          v-for="(items, key) in barList"
          :key="key"
          @click="setActive(items.value)"
        > {{ items.name }}</span>
      </p>
      <!-- <p class="countStyle">
        {{ $t("customization.testTotal") }}：{{ totalCount }}
      </p> -->
    </div>
  </div>
</template>
<script>
import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getFieldTest } from "@/api/echrtsApi";
export default {
  components: {},
  name: "FieldTest",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "FieldTestChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.fieldTestTotal"),
        seriesCenter: ["25%", "58%"],
        tooltipFormatter: `{b}<br /> ${this.$t("number")}：{c}<br />${this.$t(
          "Proportion"
        )}：{d}%`,
        richNameWidth: 70,
        legendTop: '10px',
        costomLegendFormatter: function (name) {
          return name;
        },
      },
      barList: [
        {
          name: this.$t("customization.week"),
          value: 1,
        },
        {
          name: this.$t("customization.month"),
          value: 2,
        },
        {
          name: this.$t("customization.year"),
          value: 3,
        },
      ],
      barActive: 1,
      dataAllList: [{}, {}, {}],
      totalCount: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    // bar选择
    setActive(val) {
      this.barActive = val;
      const { dataAllList } = this;
      this.setData(dataAllList[val - 1]);
    },
    setEchartsWidth() {
      this.barWidth = this.$refs.FieldTestRef.offsetWidth;
      this.barHeight = this.$refs.FieldTestRef.offsetHeight;
    },
    getBarData() {
      getFieldTest(1)
        .then((res) => {
          const { statusCode, data } = res.data;
          if (statusCode == 200) {
            this.setData(data);
            this.dataAllList[0] = data;
          }
        })
        .catch(() => { });
      getFieldTest(2)
        .then((res) => {
          const { statusCode, data } = res.data;
          if (statusCode == 200) {
            this.dataAllList[1] = data;
          }
        })
        .catch(() => { });
      getFieldTest(3)
        .then((res) => {
          const { statusCode, data } = res.data;
          if (statusCode == 200) {
            this.dataAllList[2] = data;
          }
        })
        .catch(() => { });
    },
    setData(val) {
      let { list: dataList, totalCount } = val;
      if (dataList.length > 0) {
        let legendFormatter = (name) => {
          const item = dataList.find((i) => {
            return i.name === name;
          });
          const p = item.value;
          let clientWidth = document.documentElement.clientWidth;
          let newName = name.length > 7 ? name.slice(0, 7) + "..." : name;
          if (clientWidth < 1900) {
            newName = name.slice(0, 5) + "...";
            this.pieParams.richNameWidth = 60;
          }
          return "{name|" + newName + "}" + "{percent|" + p + "}";
        };
        this.pieParams.legendFormatter = legendFormatter;

        // let legendFormatter = name => {
        //   const item = dataList.find(i => {
        //     return i.name === name;
        //   });
        //   const p = item.value;
        //   return "{name|" + name + "}" + "{percent|" + p + "}";
        // };
        // this.pieParams.legendFormatter = legendFormatter;
      }
      this.totalCount = totalCount || 0;

      this.pieParams.data =
        dataList.map((item) => {
          switch (item.name) {
            case "建筑给排水及采暖工程":
              item.name = this.$t("customization.buildAndWater");
              break;
            case "建筑电气工程":
              item.name = this.$t("customization.buildElect");
              break;
            case "节能工程":
              item.name = this.$t("customization.savePower");
              break;
            case "通风与空调工程":
              item.name = this.$t("customization.windAndAir");
              break;
            case "通用表格":
              item.name = this.$t("customization.commonTable");
              break;
          }
          return item;
        }) || [];
      drawAnnularChart(this.pieParams);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
