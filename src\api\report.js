/*
 * @Description:一键填报
 * @Author:
 * @Date: 2022-06-30 17:21:48
 * @LastEditTime: 2023-12-26 18:06:15
 * @LastEditors: Jky200guo <EMAIL>
 * @Usage:
 */
import Axios from "@/router/axios";
// 功能：授权_获取项目是否授权一键填报
export function getAgreement() {
  return Axios({
    url: `/api/ocrp-practice-module/ocrp-agreement`,
    method: "get"
  });
}
// 功能：授权_保存项目一键授权
export function saveAgreement(query) {
  return Axios({
    url: `/api/ocrp-practice-module/save-ocrp-agreement`,
    method: "post",
    data: query
  });
}
// 一键填报类型
export function typeEnum() {
  return Axios({
    url: `/api/ocrp-practice-module/practice-type-enum`,
    method: "get"
  });
}
// 功能：通过类别ID，获取类别下的菜单列表
export function menuList(query) {
  return Axios({
    url: `/api/ocrp-practice-module/menu-list-by-type/${query}`,
    method: "get"
  });
}
// 功能：通过做法ID，获取做法认证下的菜单列表,包含是否授权
export function practiceList(query) {
  return Axios({
    url: `/api/ocrp-practice-module/menu-list-by-practice-id/${query}`,
    method: "get"
  });
}
// 功能：保存项目认证点抓拍模块
export function saveMenu(query) {
  return Axios({
    url: `/api/ocrp-practice-module/save-snap-practice-menu`,
    method: "post",
    data: query
  });
}
// 功能：获取做法列表
export function getList(query) {
  return Axios({
    url: `/api/ocrp-practice-module/practice-list`,
    method: "post",
    data: query
  });
}
// 佐证资料接口
// 功能：获取批次列表
export function batchList(query) {
  return Axios({
    url: `/api/supporting-materials-module/batch-list`,
    method: "post",
    data: query
  });
}
// 功能：获取批次认证点树形结构
export function batchtTree(query) {
  return Axios({
    url: `/api/supporting-materials-module/batch-point-tree?batchid=${query}`,
    method: "get"
  });
}
// 功能:获取批次下的作证材料列表
export function extendList(query) {
  return Axios({
    url: `/api/supporting-materials-module/batch-extend-file-list?batchid=${query.batchid}&keyword=${query.keyword}`,
    method: "get"
  });
}
// 功能:创建作证材料
export function creatFile(query) {
  return Axios({
    url: `/api/supporting-materials-module/extend-file`,
    method: "post",
    data: query
  });
}

// 功能:删除作证材料
export function delData(query) {
  return Axios({
    url: `/api/supporting-materials-module/extend-file?batchid=${query.batchid}&extendfileid=${query.extendfileid}`,
    method: "delete",
  });
}

// 功能:编辑作证材料
export function editeData(query) {
  return Axios({
    url: `/api/supporting-materials-module/edit-extend-file`,
    method: "put",
    data: query
  });
}
// 功能:批次信息保存
export function saveInfo(query) {
  return Axios({
    url: `/api/supporting-materials-module/save`,
    method: "post",
    data: query
  });
}
// 功能：刷新并获取最新文件截图信息
export function refreshFile(query) {
  return Axios({
    url: `/api/supporting-materials-module/refresh-snap-file?batchid=${query.batchid}&pointfileid=${query.pointfileid}`,
    method: "get"
  });
}
// 功能：重拍信息
export function reshotInfo(query) {
  return Axios({
    url: `/api/supporting-materials-module/reshot-snap-file?batchid=${query.batchid}&pointfileid=${query.pointfileid}`,
    method: "post",
    // data: query
  });
}
export const importImgData = `/api/common-file-upload-module/uplaod-file-stream`
 // 上传附件
 export function  uploadFile(data) {
  return Axios({
    url: `/api/common-file-upload-module/uplaod-file-stream`,
    method: 'post',
    headers: {
      'content-type': 'multipart/form-data',
    },
    data: data.fd,
    onUploadProgress: data.onProgress || null,
  });
}
// 导出pdf
export function exportData(query) {
  return Axios({
    url: `/api/supporting-materials-module/pdf-export`,
    method: "post",
    data: query
  });
}
