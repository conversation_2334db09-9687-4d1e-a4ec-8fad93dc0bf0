import Axios from "@/router/axios";
import axios from "axios";
export function xxx(id) {
  return Axios({
    url: `/xxx/${id}`,
    method: "get"
  });
}
//报警列表
export function warningList(query, headers = {}) {
  return Axios({
    url: `/api/waiting-processing-management-module/web-warning-get-list`,
    method: "post",
    data: query,
    headers,
  });
}

export function todoList(data) {
  return axios({
    url: "/api/todo/list",
    method: "post",
    data: data
  });
}
//报警消息模块
export function warningModules() {
  return Axios({
    url: `/api/waiting-processing-management-module/web-modules-for-warning`,
    method: "get",
  });
}

/**
 * 获取token 新接口
 */
export function getTokenNew() {
  return Axios({
    url: "/company/gettoken",
    method: "post",
  });
}

// 已读
export function isRead(query) {
  return Axios({
    url: `/api/waiting-processing-management-module/common-warning-set-message-is-read`,
    method: "post",
    data: query,
  });
}