<!--
 * @Description: 节电管理
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-24 16:56:12
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div class="box" ref="WaterSaveRef">
        <div
          id="WaterSaveChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawLineStackShadow } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getPowerData } from "@/api/echrtsApi";
export default {
  components: {},
  name: "WaterSave",
  props: {
    moduleName: String
  },
  watch: {
    "$i18n.locale"(val) {
      if (val&&this.$IsProjectShow) {
        this.languageChange();
      }
    },
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      lineParams: {
        dom: "WaterSaveChart",
        xAxisData: [],
        seriesData: [],
        isMoreLine: true,
        grid: {
          top: "16%",
          left: "18%",
          right: "5%",
          bottom: "10%"
        },
        unit: "m³",
        tooltipFormatter: function(val) {
          let msg = `${val[0].axisValue}`;
          if (val.length) {
            val.forEach(ele => {
              msg += `<br/>${ele.marker}${ele.seriesName}
              <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
              <b>${ele.data}</b>m³`;
            });
          }
          return msg;
        }
      },
      arr: []
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId"
    })|| getUrlParams().projectId;
    this.companyId = getStore({
      name: "companyId"
    })|| getUrlParams().companyId;
    if(this.$IsProjectShow){
        this.languageChange()
    }else{
      this.getData();
    }
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function() {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    languageChange() {
      this.getData()
    },
    setEchartsWidth() {
      this.barWidth = this.$refs.WaterSaveRef.offsetWidth;
      this.barHeight = this.$refs.WaterSaveRef.offsetHeight;
    },
    getData() {
      getPowerData(this.projectId)
        .then(res => {
          let result = res.data;
          const { data, statusCode } = result;
          if (statusCode == 200) {
            let xAxisData = [];
            var str1="办公区",str2="施工区",str3="生活区"
            if(this.$IsProjectShow){
              str1=this.$t(`Administrativearea`)
              str2=this.$t(`Constructionarea`)
              str3=this.$t(`livingquarters`)
            }
            let seriesData = [
              {
                name: str1,
                type: "line",
                data: [],
                lineStyle: {
                  normal: {
                    color: "#73D13D"
                  }
                },
                itemStyle: {
                  color: "#73D13D",
                  borderColor: "#73D13D"
                }
              },
              {
                name: str2,
                type: "line",
                data: [],
                lineStyle: {
                  normal: {
                    color: "#18FFB3"
                  }
                },
                itemStyle: {
                  color: "#18FFB3",
                  borderColor: "#18FFB3"
                }
              },
              {
                name: str3,
                type: "line",
                data: [],
                lineStyle: {
                  normal: {
                    color: "#1890FF"
                  }
                },
                itemStyle: {
                  color: "#1890FF",
                  borderColor: "#1890FF"
                }
              }
            ];
            for (var items in data) {
              xAxisData.unshift(items.substring(5, 10));
              seriesData[0].data.unshift(data[items][1]);
              seriesData[1].data.unshift(data[items][2]);
              seriesData[2].data.unshift(data[items][3]);
            }
            this.lineParams.xAxisData = xAxisData;
            this.lineParams.seriesData = seriesData;
            drawLineStackShadow(this.lineParams);
          }
        })
        .catch(() => {});
    }
  }
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
