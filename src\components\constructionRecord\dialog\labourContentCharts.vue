<template>
  <div class="chart-overview-wrap">
    <div class="chart-wrap">
      <div class="wrap">
        <!-- 空心饼图 人数-->
        <div id="pieCharts11" class="pie-chart" style="width: 30%"></div>
        <!-- 空心饼图 年龄 -->
        <div id="pieChartCitle11" class="pie-chart" style="width: 40%"></div>
        <!-- 空心饼图  省份 -->
        <div id="pieChart22" class="pie-chart" style="width: 30%"></div>
      </div>
    </div>
    <div class="chart-wrap">
      <!-- 折线图 工种-->
      <div id="lineChart11" class="pie-chart"></div>
    </div>
    <div class="line-chart-wrap">
      <!--折线图 人数 -->
      <div id="lineChart33" class="pie-chart"></div>
    </div>
  </div>
</template>
<script>
import { drawRing2, drawLine3 } from "../Echarts/echarts1";
import { getDataDailog } from "@/api/constructionRecord";
export default {
  data: () => ({
    barWidth: 600,
    barHeight: 500,
    barShow: true,
    // nameTitle: "今日工种",  isMoreLine: false,
    // 折线图 工种
    pipParams1: {
      dom: "lineChart11",
      nameTitle: "今日工种",
      xAxisData: [],
      seriesData: [],
    },
    // 折线图 人数       // nameTitle: "近七日人数",  isMoreLine: false,
    pipParams2: {
      dom: "lineChart33",
      nameTitle: "近七日人数",
      xAxisData: [],
      seriesData: [],
    },
    // // 折线图 污染数
    // pipParams3: {
    //   dom: "lineChart3",
    //   nameTitle: "最近24小时污染浓度",
    //   xAxisData: [],
    //   isMoreLine: true,
    //   seriesData: [],
    // },
    // 空心饼图 人数
    pieParams1: {
      dom: "pieCharts11",
      nameTitle: "今日人数",
      subTitle: 5,
      show: true,
      titleX: "center",
      data: [],
      center: ["50%", "50%"],
      colorArray: ["#38A1F7"],
    },
    // 空心饼图 省份籍贯
    pieParams2: {
      dom: "pieChart22",
      nameTitle: "省份",
      // subTitle: 0,
      show: true,
      titleX: "center",
      data: ["混凝土", "瓦匠"],
      center: ["50%", "50%"],
      colorArray: ["#38A1F7"],
    },
    // 空心饼图 年龄
    pieChartCitle1: {
      dom: "pieChartCitle11",
      // subTitle: 0,
      show: true,
      legend: "90%",
      titleX: "center",
      nameTitle: "按年龄",
      center: ["50%", "50%"],
      total: 0,
      data: [],
      colorArray: ["#F0EF68"],
    },
    projectId: "",
    pieHollowData: null, //空心饼图
    pieCicleData: null, // 实心饼图
  }),
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
  },
  mounted() {
    this.getPieCicle();
  },
  methods: {
    // 实心图
    getPieCicle() {
      let params = {
        projectid: this.projectId,
        type: 0,
      };
      getDataDailog(params).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          let arr1 = [];
          let arr2 = [],
            arr22 = [];
          let arr3 = [],
            arr33 = [];
          let arr4 = [];
          // 饼状图--年龄
          result.data.item2[2].piecharValue.map((item) => {
            arr1.push({ value: item.fieldvalues, name: item.field });
          });
          // 折线图--工种
          result.data.item2[0].piecharValue.map((item) => {
            arr2.push(item.fieldvalues);
            arr22.push(item.field);
          });
          // 折线图--七日人数
          result.data.item2[3].piecharValue.map((item) => {
            arr3.push(item.fieldvalues);
            arr33.push(item.field);
          });
          // 籍贯
          if (result.data.item2[1].piecharValue) {
            result.data.item2[1].piecharValue.map((item) => {
              arr4.push({ value: item.fieldvalues, name: item.field });
            });
          }
          // 空心饼图 年龄
          if (arr1.length == 0) {
            this.pieChartCitle1.data = [{ value: 0, name: "" }];
          } else {
            this.pieChartCitle1.data = arr1;
          }
          drawRing2(this.pieChartCitle1);
          // 空心饼图籍贯
          this.pieParams2.data = arr4;
          drawRing2(this.pieParams2);
          this.pieParams1.subTitle = result.data.item1;
          drawRing2(this.pieParams1);
          //  折线图 人数
          if (arr3.length == 0) {
            this.pipParams2.seriesData = [{ value: 0, name: "" }];
          } else {
            this.pipParams2.seriesData = arr3;
            this.pipParams2.xAxisData = arr33;
          }
          // 折线图 工种
          this.pipParams1.seriesData = arr2;
          this.pipParams1.xAxisData = arr22;
          drawLine3(this.pipParams1);
          drawLine3(this.pipParams2);
        }
      });
    },
    // getOverviewLine() {
    //   this.pipParams3.seriesData = [];
    //   getData(this.ProjectID).then((res) => {
    //     let result = res.data;
    //     if (result.statusCode == 200) {
    //       this.pipParams1.xAxisData = result.data.chartTimeData;
    //       this.pipParams2.xAxisData = result.data.chartTimeData;
    //       this.pipParams3.xAxisData = result.data.chartTimeData;
    //       this.pipParams1.seriesData[0].data = result.data.chartWindSpeedData;
    //       this.pipParams2.seriesData[0].data = result.data.chartTemperatureData;
    //       this.pipParams3.seriesData.push({
    //         name: "PM2.5",
    //         data: result.data.chartPM25Data,
    //       });
    //       this.pipParams3.seriesData.push({
    //         name: "PM10",
    //         data: result.data.chartPM10Data,
    //       });
    //       drawLine(this.pipParams1);
    //       drawLine(this.pipParams2);
    //       drawLine(this.pipParams3);
    //     }
    //   });
    // },
    // 获取参数配置
    getCOnfig() {
      this.checkedLis = [];
      getCOnfig(this.projectId).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          // TODO 暂时这样 需要修改
          if (result.data.pM25) {
            this.showPM25 = true;
          } else {
            this.showPM25 = false;
          }
          if (result.data.pM10) {
            this.showPM10 = true;
          } else {
            this.showPM10 = false;
          }
          if (result.data.noise) {
            this.showNoise = true;
          } else {
            this.showNoise = false;
          }
          if (result.data.temperature) {
            this.showTemp = true;
          } else {
            this.showTemp = false;
          }
          if (result.data.windSpeed) {
            this.showWind = true;
          } else {
            this.showWind = false;
          }
          this.getPieHollow();
          this.getOverviewLine();
          this.getPieCicle({ form: new Date().format() });
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.chart-overview-wrap {
  background: rgba(9, 35, 92, 0.6);
  border: 1px solid #365f7d;
  .chart-wrap {
    display: flex;
    // justify-content: space-between;
    .wrap {
      width: 100%;
      position: relative;
      .pie-chart {
        width: 100%;
        float: left;
        // margin-left: 0px;
        // height: 330px;
        // min-width: 240px;
        // margin-top: 10px;
        div {
          // width: 100% !important;
        }
      }
    }
    .title {
      color: #fff;
      font-size: 20px;
      font-weight: 500;
      position: absolute;
      top: 40px;
      left: 150px;
    }
  }
  .pie-chart {
    width: 100%;
    height: 330px;
    margin-top: 10px;
  }
  .line-chart-wrap {
    display: flex;
    margin-top: 60px;
  }
}
.content-echarts {
  // background: rgba(9, 35, 92, 0.6);
  border: 1px solid #365f7d;
  .content-box {
    display: flex;

    .box {
      width: 29%;
      min-height: 500px;
    }
    .el-button {
      background: blue;
      color: #fff;
    }
  }
}
</style>
