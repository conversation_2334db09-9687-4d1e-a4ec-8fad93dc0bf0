<!-- 视屏监控 -->
<template>
  <div class="root">
    <div class="wrap">
      <div class="tip">
        若录制视频并下载到本地，请用海康播放器播放！
        <a
          target="_blank"
          href="https://www.hikvision.com/cn/support/Downloads/Desktop-Application/Hikvision-Player/"
          style="color: #5cdbd3"
        >
          点击下载
        </a>
      </div>
      <!-- 右上角点击处 -->
      <div class="change_Num">
        <p :class="{ active: curMatrix === 1 }" @click="curMatrix = 1">1X1</p>
        <p :class="{ active: curMatrix === 2 }" @click="curMatrix = 2">2X2</p>
        <p :class="{ active: curMatrix === 3 }" @click="curMatrix = 3">3X3</p>
      </div>

      <Matrix1 v-if="curMatrix === 1 && dataList.length" :dataList="dataList" />
      <Matrix
        :key="2"
        v-if="curMatrix === 2 && dataList.length"
        :matrixNum="2"
        :dataList="dataList"
      />
      <Matrix
        :key="3"
        v-if="curMatrix === 3 && dataList.length"
        :matrixNum="3"
        :dataList="dataList"
      />
    </div>
  </div>
</template>

<script>
import { getList } from "@/api/VideoSurveillance.js";

import Matrix from "./commonMatrix.vue";
import Matrix1 from "./matrix1";

export default {
  data() {
    return {
      curMatrix: 1,
      dataList: [],
    };
  },
  components: {
    Matrix1,
    Matrix,
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });

    this.getMonitor();
  },
  mounted() {},
  methods: {
    // 渲染数据
    async getMonitor() {
      let { videos } = await getList({
        projectId: this.projectId,
        pageSize: 99999,
      });

      this.dataList = videos;
    },
  },
};
</script>

<style lang="scss" scoped>
.root,
.wrap {
  width: 100%;
  height: 100%;
  position: relative;
  // overflow: hidden;
}

.tip {
  position: absolute;
  top: -36px;
  left: 0;
}

/deep/ .el-card__body {
  padding: 0;
}

.change_Num > p {
  cursor: pointer;
}

.change_Num {
  width: 200px;
  height: 40px;
  line-height: 40px;
  display: flex;
  justify-content: space-between;
  position: absolute;
  right: 64px;
  top: -56px;
}

.change_Num > p {
  font-size: 20px;
}

.change_Num .active {
  color: cyan;
}
</style>
<style type="text/css">
video {
  object-fit: fill;
}
</style>
