<!--
 * @Description: 智能眼镜
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-08-01 16:03:54
 * @LastEditors: dong<PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="SmartGlassesRef"
      >
        <div
          id="SmartGlassesChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>

import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getSmartGlasses } from "@/api/echrtsApi";
export default {
  components: {},
  name: "SmartGlasses",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "SmartGlassesChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext:this.$t("customization.smartGlassesTotal"),
        seriesCenter: ["25%", "58%"],
        richNameWidth: 40,
        legendTop: '10px',
        noTooltipShow: true, //不显示
        itemStyleEmphasis: {
          label: {
            show: true,
            // position: 'center',
            x: "20%",
            y: "10%",
            textStyle: {
              rich: {
                numText: {
                  color: "#fff",
                  fontSize: 13,
                  width: 30,
                  textAlign: "center",
                },
                text: {
                  color: "#fff",
                  fontSize: 13,
                  padding: [0, 0, 0, 0],
                  width: 30,
                  textAlign: "center",
                },
              },
            },
            formatter: (params) => {
              return `{text| ${params.name}\n${params.value
                }}\n{numText|${this.$t("Proportion")}： ${params.percent || 0}%}`;
            },
          },
        },
        costomLegendFormatter:function(name){
          return name;
        },
      },
      totalCount: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.SmartGlassesRef.offsetWidth;
      this.barHeight = this.$refs.SmartGlassesRef.offsetHeight;
    },
    getBarData() {
      const { projectId, companyId } = this;
      getSmartGlasses({
        projectId,
        companyId,
      })
        .then((res) => {
          const { statusCode, data } = res.data;
          if (statusCode == 200) {
            this.setEcharts(data);
          }
        })
        .catch(() => { });
    },
    setEcharts(val) {
      let dataList = val;
      // this.pieParams.titleInfor.text = totalCount;
      let sum = 0;
      dataList.forEach((ele) => {
        sum = sum + Number(ele.value);
      });

      let legendFormatter = (name) => {
        let newName = name.length > 15 ? name.slice(0, 15) + "..." : name;
        return "{name|" + newName + "}";
      };
      this.pieParams.legendFormatter = legendFormatter;

      this.totalCount = sum;
      this.pieParams.data = dataList.map((item) => {
        switch (item.name) {
          case "隐患识别":
            item.name = this.$t("customization.hazardIdentify");
            break;
          case "人脸识别":
            item.name = this.$t("customization.faceIdentify");
            break;
          case "远程技术指导":
            item.name = this.$t("customization.remoteTeach");
            break;
          case "隐蔽工程记录":
            item.name = this.$t("customization.hiddRec");
            break;
          case "关键工序记录":
            item.name = this.$t("customization.keyRec");
            break;
        }
        return item;
      });
      console.log(this.pieParams,"智能眼镜")
      drawAnnularChart(this.pieParams);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
