<!--
 * @Description: 智能巡检
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-24 17:24:00
 * @LastEditors: dong<PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="SafetyInspectionRef"
      >
        <div
          id="SafetyInspectionChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>

import { drawCustomBar } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getSafetyInspection } from "@/api/echrtsApi";
export default {
  components: {},
  name: "SafetyInspection",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      lineParams: {
        dom: "SafetyInspectionChart",
        xAxisData: [],
        seriesData: [],
        boundaryGap: true,
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        axisLabelFormatter: function (value) {
          var res = value;
          if (res.length > 3) {
            res = res.substring(0, 3) + "..";
          }
          return res;
        },
        dataZoom: [],
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.SafetyInspectionRef.offsetWidth;
      this.barHeight = this.$refs.SafetyInspectionRef.offsetHeight;
    },
    getData() {
      getSafetyInspection(this.projectId)
        .then((res) => {
          let result = res.data;
          const {
            data: { names, totalCount, unqualifiedCount },
            statusCode,
          } = result;
          totalCount.name =
            getStore({ name: "language" }) === "en"
              ? "qualified"
              : totalCount.name;
          unqualifiedCount.name =
            getStore({ name: "language" }) === "en"
              ? "unqualified"
              : unqualifiedCount.name;
          if (statusCode == 200) {
            // let xAxisData = [];
            let seriesData = [
              {
                type: "bar",
                ...totalCount,
                itemStyle: {
                  normal: {
                    color: "#54c4a1",
                  },
                },
              },
              {
                type: "bar",
                ...unqualifiedCount,
                itemStyle: {
                  normal: {
                    color: "#c65f4f",
                  },
                },
              },
            ];

            // this.lineParams.dataZoom = [
            //   {
            //     show: true,
            //     type: "slider",
            //     // handleSize: 32, // 两边的按钮大小
            //     startValue: 0, // 重点在这   -- 开始的值
            //     endValue: 2, // 重点在这   -- 结束的值
            //     // height: 10, // 滑动条组件高度
            //     // bottom: 0, // 距离图表区域下边的距离
            //   },
            //   {
            //     zoomLock: true, // 这个开启之后只能通过鼠标左右拉动，不能滚动显示
            //     type: "inside",
            //   },
            // ];

            this.lineParams.xAxisData = names.map((item) => {
              return getStore({ name: "language" }) === "en"
                ? "Suspended scaffolding"
                : item;
            });
            this.lineParams.seriesData = seriesData;
            drawCustomBar(this.lineParams);
          }
        })
        .catch(() => { });
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
