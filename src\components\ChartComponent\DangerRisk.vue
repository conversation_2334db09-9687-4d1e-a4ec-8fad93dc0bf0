<!--
 * @Description: 隐患、风险
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-24 16:46:52
 * @LastEditors: dong<PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text" v-if="titleName.length">
      <span
        :class="nameActive == key ? 'textSpan textSpanActive' : 'textSpan'"
        v-for="(items, key) in titleName"
        :key="key"
        @click="setData(key)"
        >{{ items }}</span
      >
    </div>
    <div class="areaContent">
      <div class="box" ref="DangerRiskRef">
        <div
          id="DangerRiskChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>

import { drawLineStackShadow } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getRiskData, getHazardData } from "@/api/echrtsApi";
export default {
  components: {},
  name: "DangerRisk",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      lineParams: {
        dom: "DangerRiskChart",
        xAxisData: [],
        seriesData: [],
        isMoreLine: true,
        grid: {
          top: "18%",
          left: "12%",
          right: "5%",
          bottom: "10%"
        },
        // tooltipFormatter: function (val) {
        //   let msg = `${val[0].axisValue}`;
        //   if (val.length) {
        //     val.forEach((ele) => {
        //       msg += `<br/>${ele.marker}${ele.seriesName}
        //       <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
        //       <b>${ele.data}</b>m³`;
        //     });
        //   }
        //   return msg;
        // },
      },
      dangerData: {}, // 隐患数据
      riskData: {}, // 风险数据
      nameActive: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getData();
  },
  computed: {
    titleName() {
      return this.moduleName.split("、");
    },
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.DangerRiskRef.offsetWidth;
      this.barHeight = this.$refs.DangerRiskRef.offsetHeight;
    },
    getData() {
      getHazardData({
        projectList: [this.projectId],
      }).then((resData) => {
        let result = resData.data;
        const { data, statusCode } = result;
        if (statusCode == 200) {
          if (data.length > 0) {
            let name = [];
            let data1 = []; // 重大隐患
            let data2 = []; // 一般隐患
            let dataList = [];
            data.forEach((items) => {
              name.push(items.itemName);
              data1.push(items.greatCount);
              data2.push(items.generalCount);
            });

            dataList = [
              {
                name: this.$t("customization.importDanger"),
                data: data1,
                type: "line",
              },
              {
                name: this.$t("customization.smallDanger"),
                data: data2,
                type: "line",
              },
            ];
            this.dangerData = {
              name,
              dataList,
            };
            this.setEcharts(this.dangerData);
          }
        }
      });
      getRiskData({
        projectIdList: [this.projectId],
      })
        .then((res) => {
          let result = res.data;
          console.log(result);
          const {
            data: {
              itemNameList,
              oneQtyList,
              twoQtyList,
              threeQtyList,
              fourQtyList,
            },
            statusCode,
          } = result;
          if (statusCode == 200) {
            let seriesData = [
              {
                name: "Ⅰ级",
                data: oneQtyList,
                type: "line",
              },
              {
                name: "Ⅱ级",
                data: twoQtyList,
                type: "line",
              },
              {
                name: "Ⅲ级",
                data: threeQtyList,
                type: "line",
              },
              {
                name: "Ⅳ级",
                data: fourQtyList,
                type: "line",
              },
            ];
            this.riskData = {
              name: itemNameList,
              dataList: seriesData,
            };
          }
        })
        .catch(() => {});
    },
    setEcharts(val) {
      this.lineParams.xAxisData = val.name;
      this.lineParams.seriesData = val.dataList;
      
      drawLineStackShadow(this.lineParams);
    },
    // 切换数据
    setData(val) {
      this.nameActive = val;
      const { dangerData, riskData } = this;
      if (val == 0) {
        this.setEcharts(dangerData);
      } else if (val == 1) {
        this.setEcharts(riskData);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.textSpan {
  display: inline-block;
  margin-right: 10px;
  color: #8196b6;
  font-size: 16px;
}
.textSpanActive {
  color: #fff;
  font-size: 18px;
}
</style>
