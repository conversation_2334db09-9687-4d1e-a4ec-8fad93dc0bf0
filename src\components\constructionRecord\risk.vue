<!-- 风险 -->
<template>
  <div class="box" ref="riskBoxRef">
    <div class="classBox" v-show="hazardShow">
      <div
        id="lineChart"
        :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
      ></div>
    </div>
    <div class="classBox" v-show="riskShow">
      <div
        id="lineChart2"
        :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
      ></div>
    </div>
  </div>
</template>

<script>
import { drawLine2 } from './Echarts/echarts';
import { changeData, riskChangeData } from '@/api/constructionRecord';
// import { getScreenWidth } from '@/util/screen';
export default {
  components: {},
  props: {
    hazardShow: {
      type: Boolean,
      default: true,
    },
    riskShow: {
      type: Boolean,
      default: false,
    },
  },
  name: 'risk',
  data() {
    return {
      barWidth: null,
      barHeight: null,
      lineParams: {
        dom: 'lineChart',
        xAxisData: [],
        legendData: [],
        seriesData: [],
      },
      lineParams2: {
        dom: 'lineChart2',
        xAxisData: [],
        legendData: [],
        seriesData: [],
      },
      projectIdList: [],
      projectId: 0,
    };
  },
  watch: {
    riskShow(val) {
      if (val) {
        this.getChangeDatas();
      } else {
        this.getRiskChangeDatas();
      }
    },
  },
  created() {
    this.projectId = getStore({
      name: 'projectId',
    });
    this.projectIdList.push(this.projectId);
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener('resize', function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
    this.getChangeDatas();
    this.getRiskChangeDatas();
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.riskBoxRef.offsetWidth - 40;
      this.barHeight = this.$refs.riskBoxRef.offsetHeight;
      // this.barWidth = getScreenWidth(w);
      // this.barHeight = getScreenWidth(h);
    },
    // 风险变化趋势 多折线图
    getChangeDatas() {
      let params = {
        projectIdList: this.projectIdList,
        startDate: '',
        endDate: '',
      };
      changeData(params)
        .then((res) => {
          let result = res.data;
          this.lineParams2.xAxisData = [];
          this.lineParams2.legendData = [];
          this.lineParams2.seriesData = [];
          if (result.statusCode == 200) {
            this.lineParams2.xAxisData = result.data.itemNameList;
            this.lineParams2.legendData = ['I级', 'II级', 'III级', 'IV级'];
            this.lineParams2.seriesData = [
              {
                name: 'I级',
                type: 'line',
                stack: 'Total',
                data: result.data.oneQtyList,
              },
              {
                name: 'II级',
                type: 'line',
                stack: 'Total',
                data: result.data.twoQtyList,
              },
              {
                name: 'III级',
                type: 'line',
                stack: 'Total',
                data: result.data.threeQtyList,
              },
              {
                name: 'IV级',
                type: 'line',
                stack: 'Total',
                data: result.data.fourQtyList,
              },
            ];
            drawLine2(this.lineParams2);
          }
        })
        .catch(() => {});
    },
    // 隐患变化趋势 多折线图
    getRiskChangeDatas() {
      let params = {
        projectList: this.projectIdList,
        startDate: '',
        endDate: '',
      };
      riskChangeData(params)
        .then((res) => {
          let result = res.data;
          this.lineParams.xAxisData = [];
          this.lineParams.legendData = [];
          this.lineParams.seriesData = [];
          if (result.statusCode == 200) {
            let arr1 = [],
              arr2 = [];
            if (result.data && result.data.length > 0) {
              result.data.forEach((ele) => {
                this.lineParams.xAxisData.push(ele.itemName);
                arr1.push(ele.greatCount);
                arr2.push(ele.generalCount);
              });
              this.lineParams.legendData = ['重大隐患', '一般隐患'];
              this.lineParams.seriesData = [
                {
                  name: '重大隐患',
                  type: 'line',
                  stack: 'Total',
                  data: arr1,
                },
                {
                  name: '一般隐患',
                  type: 'line',
                  stack: 'Total',
                  data: arr2,
                },
              ];
            }
            drawLine2(this.lineParams);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  box-sizing: border-box;
  .classBox{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
  }
}
</style>
