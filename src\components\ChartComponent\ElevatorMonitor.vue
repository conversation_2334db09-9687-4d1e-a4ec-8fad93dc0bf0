<!--
 * @Description: 吊篮监测
 * @Author:
 * @Date：2025-07-16 
 * @LastEditors:zhenghaoyuan
 * @LastEditTime:2025-07-16
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">

      <IconLayout
        :showTitle="true"
        :titleObj="titleObj"
        :titleIcon="titleIcon"
        :iconList="iconList2x2"
        :cols="2"
      />
    </div>
  </div>
</template>
<script>
import { drawBarLineTotal } from "@/components/constructionRecord/Echarts/echartsOne.js";
import IconLayout from './iconLayout.vue';
import { getBaskData } from "@/api/echrtsApi";
export default {
  components: { IconLayout },
  name: "SchemeManage",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,

      titleIcon:"",
      titleObj: {
        text: '', value: 0
      },
      iconList3x3: [
       
      ],
      iconList2x2: [
       
      ]
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
  },
  methods: {
    getBarData() {
      getBaskData()
      .then((res)=>{
        const {
            data: { name, data },
            statusCode,
          } = res.data;
          if(statusCode==200){
            this.titleIcon=require('../../assets/customize/ElevatorMonitor/totalcount.png');
            this.titleObj.text="告警总次数";
            if(Array.isArray(data)&&data.length>0){
              data.forEach((count,index)=>{
                this.titleObj.value+=count;
                this.iconList2x2.push({
                  icon: require(`../../assets/customize/ElevatorMonitor/monitor${index}.png`),
                  text: name[index],
                  value: count
                })
              });
            }
          }
      })
      .catch(()=>{});
    },
    setEcharts(val) {

    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
}
.area {
  overflow: hidden;
}
</style>
