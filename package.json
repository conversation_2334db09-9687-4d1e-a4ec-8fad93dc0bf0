{"name": "labor-management-web", "version": "1.0.0", "description": "智慧工地公司首页", "author": "wanglei", "private": true, "scripts": {"dev": "cross-env NODE_ENV=development NODE_ALIAS=development webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev --ip=**************", "npm": "npm install", "test": "cross-env NODE_ENV=test NODE_ALIAS=test node build/build.js", "build": "cross-env NODE_ENV=production NODE_ALIAS=production node build/build.js", "prepare": "husky install"}, "dependencies": {"babel-polyfill": "^6.26.0", "compression-webpack-plugin": "^1.1.12", "es6-promise": "^4.2.8", "moment": "^2.30.1", "nprogress": "^0.2.0", "postcss-px2rem": "^0.3.0", "v-viewer": "^1.6.4", "videojs-contrib-hls": "^5.15.0", "vue-i18n": "^8.28.2", "vue-photo-preview": "^1.1.3", "vuedraggable": "^2.24.3"}, "devDependencies": {"@babel/eslint-parser": "^7.19.1", "@vue/cli-plugin-eslint": "^5.0.8", "asidemenu": "git+http://************:8000/front/asidemenu.git#feat-UI", "autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-component": "^1.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.6.0", "cross-env": "^7.0.3", "crypto-js": "^4.0.0", "css-loader": "^0.28.11", "eslint": "^8.23.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-vue": "^9.5.1", "extract-text-webpack-plugin": "^3.0.0", "ezuikit-js": "7.6.4", "file-loader": "^1.1.11", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "husky": "^8.0.0", "jky-component": "git+http://************:8000/front/assets.git#feat-UI", "jky-utils": "^1.0.6", "less": "^4.1.1", "less-loader": "^10.0.1", "lint-staged": "^13.0.3", "node-notifier": "^5.1.2", "node-sass": "^4.14.1", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.6", "postcss-url": "^7.2.1", "pre-commit": "^1.2.2", "rimraf": "^2.6.0", "sass": "^1.35.2", "sass-loader": "^7.3.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "style-loader": "^3.0.0", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-aliplayer-v2": "^1.3.0", "vue-directive-image-previewer": "^2.2.2", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "lint-staged": {"*.{js,jsx,tsx,ts,vue}": ["prettier --write", "eslint --ext .js,.jsx,.ts,.tsx"]}}