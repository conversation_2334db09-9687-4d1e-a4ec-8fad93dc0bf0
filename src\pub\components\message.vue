<template>
  <div style="width:100%">
    <NavButton></NavButton>
    <div class="message_wrap">
      <el-tabs
        v-model="activeName"
        type="card"
        @tab-click="handleClick"
      >
        <el-tab-pane
          label="全部"
          name="first"
        ></el-tab-pane>
        <el-tab-pane
          label="未读"
          name="second"
        ></el-tab-pane>
        <el-tab-pane
          label="已读"
          name="third"
        ></el-tab-pane>

      </el-tabs>
      <el-row>
        <el-col>
          <el-form
            ref="form"
            :model="searchForm"
            label-width="90px"
          >
            <el-col :span="7">
              <el-form-item label="接收时间:">
                <el-date-picker
                  v-model="searchForm.time"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="报警模块:">
                <el-select
                  placeholder="请选择"
                  v-model="searchForm.module"
                >
                  <el-option
                    v-for="item in moduleOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="6">
              <el-form-item label="消息类型:">
                <el-select
                  placeholder="请选择"
                  v-model="searchForm.type"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="状态:">
                <el-select
                  placeholder="请选择"
                  v-model="searchForm.status"
                >
                  <el-option
                    v-for="item in statusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <el-col
              :span="4"
              :offset="0"
            >
              <el-button
                type="primary"
                class="buttonType"
                @click="getList()"
              >查询</el-button>

            </el-col>
          </el-form>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <TableList
            :border="false"
            :containerHeight="440"
            :tHeader="columns"
            :tableData="listData"
            :mulSelect="true"
            :mulSelectFixed="false"
            @selectChange="selectionChange"
          >
            <template #options="scope">
              <el-button
                type="text"
                @click="detailClick(scope.row)"
              >查看</el-button>
            </template>
          </TableList>
        </el-col>
      </el-row>
      <div class="pagination">
        <bottom-page
          :pagination="pagination"
          v-on:currentChange="handleCurrentChange"
          v-on:sizeChange="handleSizeChange"
        ></bottom-page>
      </div>

    </div>
  </div>
</template>
<script>
import NavButton from '@/pub/components/navButton';
import BottomPage from "@/pub/components/BottomPage";
import { getStore, setStore } from "@/util/store";
import { warningList, warningModules, getTokenNew, isRead } from '@/api/todo'
import { envValue } from '@/util/dictionaries';
const deepAlamTypes = [
  '地表沉降速率报警',
  '地表沉降累计位移报警',
  '建筑物沉降速率报警',
  '建筑物沉降累计位移报警',
  '冠梁水平位移速率报警',
  '冠梁水平累计位移速率报警',
  '冠梁水平累计位移速率报警',
  '冠梁竖向位移速率报警',
  '冠梁竖向累计位移速率报警',
  '深层土体水平位移速率报警',
  '深层土体水平累计位移报警',
];
export default {
  name: "warning",
  components: {
    NavButton,
    BottomPage
  },
  data() {
    return {
      activeName: 'first',
      listData: [

      ],
      options: [{ label: '全部', value: '' }, { label: '已读', value: '' }, { label: '未读', value: '' }],
      statusOptions: [{ label: '全部', value: 0 }, { label: '已完成', value: 3 }, { label: '进行中', value: 2 }],
      moduleOptions: [{ label: '全部', value: '' }],
      searchForm: {
        time: "",
        beginTime: "",
        endTime: "",
        searchKey: "",
        isRead: null,
        module: '',
        checkVisibility: false,
        type: '',
        status: '报警',
      },
      pagination: {
        //分页默认值
        totalSize: 0,
        pageIndex: 1,
        pageSize: 10,
        position: "center",
        layout: "total,pager,sizes,prev,next,jumper",
      },
      columns: [
        {
          prop: "index",
          label: "序号",
          type: "codeNum",
          width: 80
        },
        {
          prop: "desc",
          label: "消息名称",
        },
        {
          prop: "module",
          label: "报警模块",
        },
        {
          prop: "warningTime",
          label: "接收时间",
          width: 260
        },

        {
          prop: 'options',
          type: "slot",
          label: '操作',
          width: 150
        },
      ],
      projectId: 0,
      userId: 0,
      companyId: 0,
      projectName: 0,
    };
  },
  created() {
    this.userId = getStore({ name: 'userId' }) || getStore({ name: 'UserId' });
    this.companyId =
      getStore({ name: 'companyId' }) || getStore({ name: 'CompanyId' });
    this.projectId =
      getStore({ name: 'projectId' }) || getStore({ name: 'ProjectId' });
    this.projectName = getStore({ name: 'projectName' });
    this.getModules();
    this.getList();
  },
  methods: {
    handleClick(val) {
      this.searchForm.isRead = val.name == 'second' ? false : val.name == 'third' ? true : null;
      this.getList()
    },
    async getModules() {
      let { data } = await warningModules();
      let res = data.data;
      this.moduleOptions = [{ label: '全部', value: '' }];
      res.map((item, index) => {
        if (item) {
          let obj = { value: item, lable: item }

          this.moduleOptions.push(obj)
          this.moduleOptions[index].label = item;
          this.moduleOptions[index].value = item;
        }
      })

    },
    async getList() {
      this.searchForm.beginTime = this.searchForm.time[0]
      this.searchForm.endTime = this.searchForm.time[1]
      let params = {
        ...this.pagination,
        ...this.searchForm,
      }
      let { data } = await warningList(params);
      let res = data.data;
      this.listData = res.items;
      this.pagination.totalSize = res.totalCount;
    },
    selectionChange() { },
    detailClick(val) {
      isRead({
        ids: [val._id],
      }).then((res) => {
        console.log(res, 'res-------')
        if (res.status == 200) {
          this.$root.$children[0].getList(true)
        }
      });
      let env = envValue[window.location.hostname];
      switch (val.module) {
        case '吊篮监测':
          window.open(`${window.location.protocol}//${window.location.hostname}:8305/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/deviceManagement`);
          break;
        case '卸料平台监测':
          window.open(`${window.location.protocol}//${window.location.hostname}:8791/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/operationalAspect`);
          break;
        case '塔吊监测':
          window.open(`${window.location.protocol}//${window.location.hostname}:8503/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/waring`);
          break;
        case '龙门吊监测':
          window.open(`${window.location.protocol}//${window.location.hostname}:8793/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/equipmentInformation`);
          break;
        case '升降机监测':
          window.open(`${window.location.protocol}//${window.location.hostname}:8300/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/operatingCondition`);
          break;
        case '附着式升降脚手架监测':
          window.open(`${window.location.protocol}//${window.location.hostname}:20000/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}`);
          break;
        case '高支模监测':
          window.open(`${window.location.protocol}//${window.location.hostname}:8505/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/polices`);
          break;
        case '基坑监测':
          const isMatched = deepAlamTypes.includes(val.type);
          if (isMatched) {
            // 10种报警类型中的，跳转第三方检查测报警
            window.open(`${window.location.protocol}//${window.location.hostname}:8792/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}&alarmType=alarmRecord#/warnRecord`);
          } else {
            //实时监测报警记录
            window.open(`${window.location.protocol}//${window.location.hostname}:8792/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}&alarmType=thirdAlarm#/warnRecord`);
          }
          break;
        case '配电箱监测':
          window.open(`${window.location.protocol}//${window.location.hostname}:8111/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/alarmRecord`);
          break;
        case '环境监测':
          window.open(`${window.location.protocol}//${window.location.hostname}:8302/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}`);
          break;
        case '标准养护室':
          window.open(`${window.location.protocol}//${window.location.hostname}:8606/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/monitoringAlarm`);
          break;
        case '混凝土测温':
          window.open(`${window.location.protocol}//${window.location.hostname}:8303/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/historicalData`);
          break;
        case '车辆冲洗':
          window.open(`${window.location.protocol}//${window.location.hostname}:8600/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/flushRecord`);
          break;
        case '智能安全帽':
          getTokenNew().then((res) => {
            let {
              data: { data, statusCode },
            } = res;
            if (data && statusCode === 200)
              window.open(
                'https://zhgdaqm.jiankeyan.com/?env=' +
                env +
                '&frompage=' +
                window.location.hostname,
                data
              );
          });
          break;
        case '智能烟感':
          window.open(`${window.location.protocol}//${window.location.hostname}:8150/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/alarmRecord`);
          break;
        case '动火管理':
          //人员端
          window.open(`${window.location.protocol}//${window.location.hostname}:8834/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/index`);
          break;
        case 'AI识别':
          window.open(`${window.location.protocol}//${window.location.hostname}:8100/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/AI`);
          break;
        case '质量检查':
          window.open(`${window.location.protocol}//${window.location.hostname}:8201/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/dailyCheck`);
          break;
        case '安全检查':
          window.open(`${window.location.protocol}//${window.location.hostname}:8096/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/dailyCheck`);
          break;
        case 'xxx':
          break;
        case 'xxx':
          break;
        case 'xxx':
          break;
        case 'xxx':
          break;
        case 'xxx':
          break;
        case 'xxx':
          break;
        case 'xxx':
          break;
        case 'xxx':
          break;
        case 'xxx':
          break;
        case 'xxx':
          break;
        default:
          break
      }

    },
    handleCurrentChange(val) {
      this.pagination.pageIndex = val;
      this.getList();
    },
    handleSizeChange(val) {
      this.pagination.pageIndex = 1;
      this.pagination.pageSize = val;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
.message_wrap {
  background: #0b3472;
  padding: 20px 50px 0;
  width: 90%;
  margin: 0 auto;
}
.pagination {
  text-align: right;
  padding: 10px 0 0 0;
}
/deep/ .el-tabs__item {
  color: #fff !important;
}
/deep/.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  color: #409eff !important;
}
/deep/ .message_wrap {
  .el-input__inner {
    border: 1px solid #fff !important;
    background: transparent !important;
  }
  .el-select {
    .el-input__inner {
      border: 0 !important;
    }
  }
}

/deep/ .el-table td,
/deep/ .el-table th {
  border-right: 1px solid #ebeef5;
}
/deep/ .el-table td:nth-child(1),
/deep/ .el-table th:nth-child(1) {
  border-left: 1px solid #ebeef5;
}

/deep/ .el-table th {
  border-top: 1px solid #ebeef5;
}
</style>
