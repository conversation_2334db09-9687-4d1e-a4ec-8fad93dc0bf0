/*
 * @Description:
 * @Author:
 * @Date: 2021-12-24 10:17:16
 * @LastEditTime: 2025-07-12 14:33:57
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Usage:
 */
import Axios from "@/router/axios";
import axios from "axios";
// 首页底部菜单列表(省级菜单之后不用这个接口)
export function getBarMenus(userid) {
  return Axios({
    url: `/login/bar-menus/${userid}`,
    method: "get"
  });
}
// （省级菜单》之后使用以下首页底部菜单列表接口
// 公司级账号权限》首页底部菜单列表
export function getBottomMenu(projectid) {
  return Axios({
    url: `/login/project-template-bar-menus/?projectid=${projectid}`,
    method: "get"
  });
} // 项目级账号权限》首页底部菜单列表
export function getProBottomMenu() {
  return Axios({
    url: `/login/project-template-bar-menus`,
    method: "get"
  });
}
// 埋点注入
export function buryPoint(data) {
  return Axios({
    url: `/api/event-tracking-platform-module/inject`,
    method: "post",
    data: data
  });
}
// 根据用户所在企业和项目获取用户单点信息
export function getGlassInfo(query) {
  return Axios({
    url: `/other-operate/bar`,
    method: "post",
    data: query
  });
}

/**
 * @description: 获取公司信息
 * @param {*} companyid
 * @return {*}
 */
export function getCompanyInfo(companyid) {
  return Axios({
    url: `/company/company-info/${companyid}`,
    method: "get"
  });
}

/**
 * @description: 获取token（为工程资料链接获取的）
 * @param {*} userid
 * @return {*}
 */
export function getToken(userid) {
  return Axios({
    url: `/company/gettoken/${userid}`,
    method: "post"
  });
}

/**
 * 获取token 新接口
 */
export function getTokenNew() {
  return axios({
    url: "/company/gettoken",
    method: "post"
  });
}

// BIM token
export function getModleToken(companyid) {
  return axios({
    url: `/api/model-manage-module/token/${companyid}`,
    method: "post"
  });
}

// BIM bucket
export function getAccount(companyid) {
  return axios({
    url: `/api/model-manage-module/account/${companyid}`,
    method: "get"
  });
}

// BIM 首页模型
export function getDefaultModel(data) {
  return axios({
    url: `/api/model-manage-module/default-model`,
    method: "post",
    data
  });
}
