<template>
  <div class="ezuikit">
    <div id="video-container"></div>
  </div>
</template>

<script>
import EZUIKit from "ezuikit-js";

import { videoControlPTZ, videoControlMove, getYSPlayUri } from "./api.js";
import DirectionControl from "./DirectionControl";

import {
  addVideoPreset,
  getVideoPresetList,
  getVideoDetail,
} from "../../../api/VideoSurveillance";

import JkyUtils from 'jky-utils'

export default {
  name: "EZUIKitJs",
  data: function () {
    return {};
  },
  props: {
    videoId: {
      type: [String, Number],
      retuired: true,
    },
    source: {
      type: String,
      required: true,
    },
  },
  watch: {
  },
  mounted() {
    this.initDatas(this.videoId);
  },
  created() {},
  methods: {
    // 初始化页面数据
    initDatas(videoId) {
      this.getVideoDetail(videoId);
    },

    // 初始化视频基本信息
    async getVideoDetail(videoId) {
      if (!videoId) return;

      const res = await getVideoDetail(videoId);
      let data = res.data || {};

      data = data.data || {};
      this.videoInfo = data;
      this.getYSPlayUri(data);
    },

    async getYSPlayUri(data) {
      const { token, deviceSerial } = data;
      let res = await JkyUtils.videoPTZ.getYSPlayUri({ accessToken: token, deviceSerial });
      res = res.data;
      if (res.code !== "200") {
        return this.$message.error("获取数据错误，请稍后再试");
      }

      const _data = res.data || {};
      this.initVideo({ url: _data.url, accessToken: token, deviceSerial });
    },

    initVideo({ url, accessToken, deviceSerial }) {
      let that = this;

      that.player = new EZUIKit.EZUIKitPlayer({
        autoplay: false, //默认播放
        id: "video-container", // 视频容器ID
        accessToken: accessToken,
        url: url,
        startTalk: () => that.player.startTalk(),
        stopTalk: () => that.player.stopTalk(),
        audio: 0, //是否开启声音  0 - 关闭 1 - 开启
        width: 800,
        height: 550,
        themeData: {
          autoFocus: 5,
          poster:
            "https://resource.eziot.com/group1/M00/00/89/CtwQEmLl8r-AZU7wAAETKlvgerU237.png",
          header: {
            color: "#1890ff",
            activeColor: "#FFFFFF",
            backgroundColor: "#000000",
            btnList: [
              {
                iconId: "deviceID",
                part: "left",
                defaultActive: 0,
                memo: "顶部设备名称",
                isrender: 1,
              },
              {
                iconId: "deviceName",
                part: "left",
                defaultActive: 0,
                memo: "顶部设备ID",
                isrender: 1,
              },
              {
                iconId: "cloudRec",
                part: "right",
                defaultActive: 0,
                memo: "头部云存储回放",
                isrender: 0,
              },
              {
                iconId: "rec",
                part: "right",
                defaultActive: 0,
                memo: "头部本地回放",
                isrender: 0,
              },
            ],
          },
          footer: {
            color: "#FFFFFF",
            activeColor: "#1890FF",
            backgroundColor: "#00000021",
            btnList: [
              {
                iconId: "play",
                part: "left",
                defaultActive: 1,
                memo: "播放",
                isrender: 1,
              },
              {
                iconId: "capturePicture",
                part: "left",
                defaultActive: 0,
                memo: "截屏按钮",
                isrender: 1,
              },
              {
                iconId: "sound",
                part: "left",
                defaultActive: 0,
                memo: "声音按钮",
                isrender: 1,
              },
              {
                iconId: "pantile",
                part: "left",
                defaultActive: 0,
                memo: "云台控制按钮",
                isrender: 1,
              },
              {
                iconId: "recordvideo",
                part: "left",
                defaultActive: 0,
                memo: "录制按钮",
                isrender: 1,
              },
              {
                iconId: "talk",
                part: "left",
                defaultActive: 0,
                memo: "对讲按钮",
                isrender: 1,
              },
              {
                iconId: "zoom",
                part: "left",
                defaultActive: 0,
                memo: "电子放大",
                isrender: 1,
              },
              {
                iconId: "hd",
                part: "right",
                defaultActive: 0,
                memo: "清晰度切换按钮",
                isrender: 1,
              },
              {
                iconId: "webExpend",
                part: "right",
                defaultActive: 0,
                memo: "网页全屏按钮",
                isrender: 1,
              },
              {
                iconId: "expend",
                part: "right",
                defaultActive: 0,
                memo: "全局全屏按钮",
                isrender: 1,
              },
            ],
          },
        },
      });
    },
  },
  beforeDestroy() {
    this.player.stop(); //关闭视频流
  },
};
</script>

<style scoped lang="scss"></style>
