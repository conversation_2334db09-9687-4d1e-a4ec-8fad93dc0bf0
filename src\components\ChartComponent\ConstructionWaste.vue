<!--
 * @Description: 建筑垃圾管理
 * @Author:
 * @Date：2025-07-16 
 * @LastEditors:zhenghaoyuan
 * @LastEditTime:2025-07-16
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
        <div class="areaContent">

      <IconLayout
        :showTitle="false"
        :titleObj="titleObj"
        :titleIcon="titleIcon"
        :iconList="iconList2x2"
        :cols="2"
      />
    </div>
  </div>
</template>
<script>
import { drawPie } from "@/components/constructionRecord/Echarts/echartsOne.js";
import IconLayout from './iconLayout.vue';
import { getBuildWaster } from "@/api/echrtsApi";
export default {
  components: { IconLayout },
  name: "SchemeManage",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,

      titleIcon:"",
      titleObj: {
        text: '', value: 0
      },
      iconList3x3: [
       
      ],
      iconList2x2: [
       
      ]
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
  },
  methods: {
    getBarData() {
      getBuildWaster()
      .then((res)=>{
        const { status, data } = res;
          console.log(res,"建筑垃圾管理")
          if(status==200){
            
            if(Array.isArray(data)&&data.length>0){
              data.forEach((item,index)=>{
                var iconIndex=index%data.length;
                this.iconList2x2.push({
                  icon: require(`../../assets/customize/ConstructionWaste/waste${iconIndex}.png`),
                  text: item.name,
                  value: item.value
                })
              });
            }
          }
      })
      .catch(()=>{});
    },
    setEcharts(val) {

    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
}
.area {
  overflow: hidden;
}
</style>
