import Axios from '@/router/axios'
// 字典
export function nameDicList(query){
    let url = query.isUrl ? `labor-m/dic-option/${query.dicstr}` : '/labor-m/dic-option'
    return Axios({
      url: url,
      method: 'post',
      data: query
    })
  }
// 人名名称接口
export function nameDataList(query){
    return Axios({
      url: "/labor-m/name-list",
      method: 'post',
      data: query
    })
  }
// 工种名称接口
export function workTypeDataList(query){
    return Axios({
      url: "/labor-m/work-type-list",
      method: 'post',
      data: query
    })
  }
// 班组名称接口
export function tameDatalist(query){
    return Axios({
      url: "/labor-m/tame-list",
      method: 'post',
      data: query
    })
  }