<!--
 * @Description: 基坑监测
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-24 17:35:13
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div class="box" ref="FoundationPitRef">
        <div
          id="FoundationPitChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
      <p class="countStyleOne">
        <span>{{ $t("customization.totalPoint") }}：{{ pointNum }}</span>
        <span>{{ $t("customization.totalAlarm") }}：{{ alarmNum }}</span>
      </p>
    </div>
  </div>
</template>
<script>

import { drawCustomBarSingle } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getDeepExcavation } from "@/api/echrtsApi";
export default {
  components: {},
  name: "FoundationPit",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      alarmNum: 0,
      pointNum: 0,
      barParams: {
        dom: "FoundationPitChart",
        xAxisData: [],
        seriesData: [],
        boundaryGap: true,
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        yminInterval: 1,
        axisLabelFormatter: function (value) {
          var res = value;
          if (res.length > 2) {
            res = res.substring(0, 3) + "..";
          }
          return res;
        },
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.FoundationPitRef.offsetWidth;
      this.barHeight = this.$refs.FoundationPitRef.offsetHeight;
    },
    getBarData() {
      getDeepExcavation()
        .then((res) => {
          const { data, statusCode } = res.data;
          if (statusCode == 200) {
            let seriesData = [
              {
                data: data.ydata[0].data,
                type: "bar",
                itemStyle: {
                  normal: {
                    color: "#4e81e4",
                  },
                },
              },
            ];
            this.alarmNum = data.alarmNum;
            this.pointNum = data.pointNum;
            this.barParams.xAxisData = data.xdata.map((item) => {
              switch (item) {
                case "锚索轴力":
                  item = this.$t("customization.anchor");
                  break;
                case "水平位移监测":
                  item = this.$t("customization.horazitalMove");
                  break;
                case "周边建筑物倾斜风险":
                  item = this.$t("customization.arroundBuildTile");
                  break;
              }
              return item;
            });
            this.barParams.seriesData = seriesData;
            console.log(this.barParams,'基坑监测')
            drawCustomBarSingle(this.barParams);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
