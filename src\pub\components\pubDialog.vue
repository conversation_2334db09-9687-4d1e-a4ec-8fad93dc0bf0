<template>
    <el-row class="pub-dialog top-btn-area" :loading="dialogLoading">
        <el-dialog
                :title="dialogTitle"
                :visible.sync="dialogVisible"
                @close="dialogCloseFun"
                size="small">
            <slot></slot>
            <el-col class="dialog_content" v-if="dialogContent">
                {{dialogContent}}
            </el-col>
            <el-col class="textAlignCenter padding_bottom10" v-if="dialogContent">
                <el-button type="primary"
                           class="search-btn"
                           @click="sureClick">确定
                </el-button>
            </el-col>
        </el-dialog>
    </el-row>
</template>
<script>
    export default {

        props: {
            dialogTitle: {
                type: String,
                default: ''
            },
            dialogContent: {
                type: String,
                default: ''
            }
        },
        data() {
            return {
                dialogLoading: false,
                dialogVisible: false,
            }
        },
        mounted() {
        },
        created() {
        },
        methods: {
            //关闭按钮
            dialogCloseFun() {
                this.$emit('dialogCloseFun');
            },
            //确定按钮
            sureClick() {
                this.dialogVisible = false;
            }
        }
    }
</script>
<style scoped>
    .dialog_content {
        height: 200px;
        overflow: auto;
        margin: 10px 0;
        border: 1px solid #ccc;
        padding: 5px;
    }
</style>