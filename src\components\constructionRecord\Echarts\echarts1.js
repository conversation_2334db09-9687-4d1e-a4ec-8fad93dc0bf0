import * as echarts from 'echarts';
import {
	call
} from 'file-loader';


// 饼图
export const drawPie = function(...params) {

	let dom = params[0].dom,
		data = params[0].data,
		nameTitle = params[0].nameTitle,
		colorArray = params[0].colorArray || [],
		callback = params[0].callback;
	let id = document.getElementById(dom)
	let myChart = echarts.init(id)
	let option = {
		title: {
			text: nameTitle,
			left: 'center',
			textStyle: {
				color: '#fff'
			}
		},
		tooltip: {
			trigger: 'item'
		},
		legend: {
			orient: 'vertical',
			left: 'right',
			textStyle: {
				color: '#fff'
			}
		},
		series: [{
			name: nameTitle,
			type: 'pie',
			radius: '50%',
			data: data,
			itemStyle: {
				normal: {
					borderWidth: 1, // 每一个类型之间空隙
					borderColor: '#fff',
					// color: function (params) {
					//     const colorList = colorArray
					//     return colorArray.length > 0 ? colorList[params.dataIndex] : '#EA8187'
					// }
				}
			},
			emphasis: {
				itemStyle: {
					color: '#fff',
					borderWidth: 1,
					shadowBlur: 10,
					shadowOffsetX: 0,
					shadowColor: 'rgba(255, 255, 255, 0.5)'
				}
			}
		}]
	}
	myChart.setOption(option)
	if (callback) {
		myChart.on('click', function(params) {
			return callback(params)
		})
	}
}

export const drawLine = function(...params) {
	let xAxisData = params[0].xAxisData,
		seriesData = params[0].seriesData
	let callback = params[0].callback;
	let isMoreLine = params[0].isMoreLine || false
	let newLegend = (function() {
		let arr = []
		if (isMoreLine) {
			seriesData.forEach(ele => {
				arr.push(ele.name)
			})
		} else {
			arr.push(seriesData.name)
		}
		return arr
	})()
	let legendData = params[0].legendData || newLegend
	let dom = params[0].dom
	let pernum = params[0].pernum || 5
	let toolboxShow = params[0].toolboxShow || false
	let nameTitle = params[0].nameTitle
	let dataZoomEnd = (pernum / xAxisData.length) * 10
	let id = document.getElementById(dom)
	let myChart = echarts.init(id)
	let option = {
		title: {
			text: nameTitle,
			left: 'center',
			textStyle: {
				color: '#fff'
			}
		},
		tooltip: {
			trigger: 'axis'
		},
		legend: {
			data: legendData,
			// left: 'right',
			orient: 'horizontal', // 'vertical'
			x: 'right', // 'center' | 'left' | {number},
			y: 10, // 'center' | 'bottom' | {number}
			// backgroundColor: '#fff',
			// borderColor: 'rgba(178,34,34,0.8)',
			// borderWidth: 4,
			padding: 10, // [5, 10, 15, 20]
			itemGap: 20,
			textStyle: {
				color: '#fff'
			},
			// selected: {
			//     '降水量': false
			// },

		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '10%',
			containLabel: true
		},
		toolbox: {
			show: toolboxShow,
			feature: {
				saveAsImage: {}
			}
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			axisLine: {
				show: true
			},
			data: xAxisData
		},
		yAxis: {
			type: 'value',
			axisLine: {
				show: true
			},
		},
		series: function() {
			let obj = {
				type: 'line',
				stack: '总量',
			}
			let result = []
			if (isMoreLine) {
				seriesData.forEach(ele => {
					result.push(Object.assign({}, obj, ele))
				})
			} else {
				result.push(Object.assign({}, obj, seriesData))
			}
			return result
		}(),
		// 缩放平移组件
		dataZoom: [{
				type: 'inside', //slider有滑块，inside内置
				disabled: false, //是否停止组件功能
				xAxisIndex: 0, //x轴,可以用数组表示多个轴
				zoomLock: true, //是否锁定区域大小（true,只能平移不能缩放）
				preventDefaultMouseMove: false,
				filterMode: "empty",
				startValue: 0, //一行有几个（起始数组下标）
				endValue: dataZoomEnd, //一行有几个（结束数组下标）
				start: null,
				end: null,
			},
			{
				handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
				handleSize: '50%',
				handleStyle: {
					color: "#294b97",
					shadowBlur: 3,
					shadowColor: 'rgba(0, 0, 0, 0.6)',
					shadowOffsetX: 2,
					shadowOffsetY: 2
				}
			}
		]
	}
	myChart.setOption(option)
	if (callback) {
		myChart.on('click', function(params) {
			return callback(params)
		})
	}
}

export const drawRing2 = function(...params) {
	//console.log(params);
	let dom = params[0].dom,
		data = params[0].data,
		nameTitle = params[0].nameTitle,
		subTitle = params[0].subTitle,
		center = params[0].center,
		show = params[0].show,
		legend = params[0].legend,
		titleX = params[0].titleX,
		colorArray = params[0].colorArray || [],
		callback = params[0].callback;
	let id = document.getElementById(dom);
	let myChart = echarts.init(id);
	let option = {
		tooltip: {
      trigger: "item",
      formatter: '{b}: {c}'
		},

    title: {
			show: show,
			text: nameTitle,
			subtext: subTitle,
			x: titleX,
			y: "center",
			textStyle: {
				//文字颜色
				color: "#fff",
				fontStyle: "normal",
				fontWeight: "bold",
			},
			subtextStyle: {
				color: "#fff",
				fontSize: 24
			}
		},
		// legend: {
		// 	x: legend,
		// 	y: "center",
		// 	orient: "vertical",
		// 	textStyle: {
		// 		//图例文字的样式
		// 		color: "#ccc",
		// 		fontSize: 16
		// 	},
		// 	// padding:-2
		// },
		// 设置环形中间的数据
		//   graphic: [{
		//     type: 'text',
		//     left: '34%',
		//     top: '55%',
		//     z: 10,
		//     style: {
		//         fill: '#1a1a1a',
		//         text: gailanTotal,
		//         font: '16px Microsoft YaHei'
		//     }
		// }],
		series: [{
			// name: "环境监测",
			type: "pie",
			radius: ["50%", "70%"],
			center: center || ["35%", "50%"],
			avoidLabelOverlap: false,
			label: {
        // show: false,
        formatter: '{b}：{c}  ',
				// position: "center"
			},
			// emphasis: {
			// 	label: {
			// 		show: true,
			// 		fontSize: "40",
			// 		fontWeight: "bold"
			// 	}
			// },
			labelLine: {
				show: false
			},
			data: data
		}]
	};
	myChart.setOption(option);
	if (callback) {
		myChart.on("click", function(params) {
			return callback(params);
		});
	}
};

// 基础折线图
export const drawLine3 = function(...params) {
	let dom = params[0].dom,
		seriesData = params[0].seriesData || [],
		xAxisData = params[0].xAxisData || [],
		nameTitle = params[0].nameTitle || [],
		encode = params[0].encode || {},
		pernum = params[0].pernum || 5,
		callback = params[0].callback;
	// 计算数据窗口范围的结束百分比
	// let dataZoomEnd = (pernum / data.length) * 100
	let id = document.getElementById(dom)
	let myChart = echarts.init(id)
	let option = {
		title: {
			text: nameTitle,
			left: 'center',
			textStyle: {
				color: '#fff'
			}
		},
		tooltip: {
			trigger: 'axis'
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '10%',
			containLabel: true
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			axisLine: {
				show: true
			},
			data: xAxisData,
			splitLine: {
				show: false
			}, //去除网格线
			// splitArea : {show : true},//保留网格区域
			axisLine: {
				lineStyle: {
					type: 'solid',
					color: '#fff', //左边线的颜色
					width: '1' //坐标线的宽度
				}
			},
			axisLabel: {
				textStyle: {
					color: '#fff', //坐标值得具体的颜色

				}
			}
		},
		yAxis: {
			type: 'value',
			splitLine: {
				show: true
			}, //去除网格线
			// splitArea : {show : true},//保留网格区域
			axisLine: {
				lineStyle: {
					type: 'solid',
					color: '#fff',
					width: '1'
				}
			},
			axisLabel: {
				textStyle: {
					color: '#fff'
				}
			}
		},
		series: [{
			data: seriesData,
			type: 'line'
		}]
	};
	myChart.setOption(option)
	//柱形图点击事件
	if (callback) {
		myChart.on('click', function(params) {
			return callback(params)
		})
	}
}
