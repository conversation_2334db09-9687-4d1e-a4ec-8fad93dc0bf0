<template>
  <div class="service_box">
    <img
      v-show="!IsExpand"
      @click="toggle"
      src="../../assets/service.png"
      class="service_more"
    />
    <img
      v-show="IsExpand"
      @click="toggle"
      src="../../assets/service_more.png"
      class="service_more"
    />
    <a
      :class="IsExpand ? 'question_box btn_box active' : 'btn_box '"
      href=" "
      style=""
    >
      <img src="../../assets/question.png" />
    </a>
    <a
      :class="IsExpand ? 'px_box btn_box active' : 'btn_box '"
      href=" "
      style=""
    >
      <img src="../../assets/pxkj.png" />
    </a>
    <a
      :class="IsExpand ? 'phone_box btn_box active' : 'btn_box '"
      href="javascript:;"
      @click="handleShowUs"
    >
      <img src="../../assets/connect_us.png" />
    </a>
    <div
      class="tip_ser_box"
      style="display: none"
      v-show="IsShowUs && IsExpand"
    >
      <p class="tel_number">************</p>
      <p class="ser_time">在线时间：08:30-18:00</p>
      <span class="connect_btn">联系客服</span>
    </div>
  </div>
</template>
<script>
export default {
  name: '',
  components: {},
  data() {
    return {
      IsExpand: false,
      IsShowUs: false,
    };
  },
  mounted: function () {},
  methods: {
    toggle() {
      this.IsExpand = !this.IsExpand;
    },
    handleShowUs() {
      this.IsShowUs = !this.IsShowUs;
      // this.IsShowUs ? (this.IsExpand = false) : "";
    },
  },
};
</script>

<style lang="scss" scoped>
.service_box {
  width: 80px;
  height: 80px;
  position: fixed;
  bottom: 37px;
  right: 30px;
  z-index: 3;
  .service_more {
    position: relative;
    z-index: 99;
  }

  img {
    width: 80px;
    cursor: pointer;
    transition: all 0.5s;
    -webkit-transform-origin: 50% 50%;
    -ms-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
  }
  .btn_box {
    position: absolute;
    z-index: 2;
    // display: none;
    left: 0;
    top: 0;
    transition: 0.8s;
    opacity: 0;
    img {
      // width: 100%;
      cursor: pointer;
      transition: all 0.5s;
      -webkit-transform-origin: 50% 50%;
      -ms-transform-origin: 50% 50%;
      transform-origin: 50% 50%;
    }
  }
  .question_box.active {
    left: -110px;
    opacity: 1;
  }
  .px_box.active {
    left: -80px;
    top: -80px;
    opacity: 1;
  }
  .phone_box.active {
    top: -110px;
    opacity: 1;
  }
  .tip_ser_box {
    width: 13vw;
    height: 15.1vh;
    background: #044184;
    background: url(../../assets/ser_back_img.png) no-repeat center;
    background-size: cover;
    position: absolute;
    right: 0;
    z-index: 999;
    padding: 10px;
    text-align: left;
    top: -259.036px;
    right: 0px;
    .tel_number {
      font-size: 30px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: rgba(255, 255, 255, 1);
      margin: 0;
      text-align: left;
    }
    .ser_time {
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      margin: 0;
      margin-top: 14px;
      text-align: left;
    }
    .connect_btn {
      width: 120px;
      height: 40px;
      line-height: 40px;
      margin-top: 14px;
      display: inline-block;
      background: url(../../assets/qq_img.png) no-repeat left;
      background-position: 10px;
      background-color: rgba(0, 150, 255, 1);
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      padding-left: 41px;
      cursor: pointer;
    }
  }
}
</style>
