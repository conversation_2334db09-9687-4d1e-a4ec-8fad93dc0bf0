<!-- 数据分析 “体温”图表内容 -->
<template>
  <div class="box">
    <div class="listBox">
      <div style="display: inline-block; width: 30%">总进度</div>
      <el-progress
        :text-inside="true"
        :percentage="percentage"
        :stroke-width="24"
        style="display: inline-block; width: 60%"
        :color="customColorMethods"
      ></el-progress>
    </div>
    <div class="listBox" v-for="(item, index) in eachProgress" :key="index">
      <div style="display: inline-block; width: 30%">
        {{ eachProgress[index].type }}
      </div>
      <el-progress
        :text-inside="true"
        :stroke-width="24"
        :percentage="eachProgress[index].count"
        style="display: inline-block; width: 60%"
        :color="customColorMethod"
      ></el-progress>
    </div>
  </div>
</template>
<script>
import { drawPie2, drawLine } from '@/util/echarts';
import { getPieList } from '@/api/constructionRecord';
// 左上角进度条
import { getAllProgress } from '../../api/constructionRecord';
//各个进度
import { getProgress } from '../../api/constructionRecord';

export default {
  components: {},
  name: 'scheduleManagement',
  data() {
    return {
      scheduleAllData: [],
      scheduleNum: [],
      eachProgress: [],
      eachName: [],
      eachNum: [],
      barWidth: 280,
      barHeight: 280,
      pipParams: {
        dom: 'pipChart',
        // nameTitle: "隐患等级",
        data1: ['一般隐患', '重大隐患'],
        data2: [],
        callback: (params) => {},
      },
      // projictid: "",
      projectId: '',
      percentage: 0,
      // percentages:10,
      //
      customColors: [
        {
          color: '#f56c6c',
        },
        {
          color: '#e6a23c',
        },
        {
          color: '#5cb87a',
        },
        {
          color: '#1989fa',
        },
        {
          color: '#6f7ad3',
        },
      ],
    };
  },
  created() {
    this.projectId = getStore({
      name: 'projectId',
    });
    this.getprojects();
    this.getEachProgress();
  },
  computed: {},
  methods: {
    // 左上角总进度条数据
    getprojects() {
      getAllProgress(this.projectId).then((res) => {
        const result = res.data;
        if (result.statusCode == 200 && result.data.length) {
          this.scheduleAllData = res.data.data;
          for (var i = 0; i < this.scheduleAllData.length; i++) {
            this.scheduleNum.push(this.scheduleAllData[i].count);
          }
          this.percentage = Number(
            (
              (this.scheduleNum[0] /
                (this.scheduleNum[0] + this.scheduleNum[1])) *
              100
            ).toFixed(2)
          );
          // console.log(this.percentage);
        }
      });
    },
    //各个进度
    getEachProgress() {
      getProgress(this.projectId).then((res) => {
        if (res.data.statusCode == 200) {
          this.eachProgress = res.data.data;
          // console.log(this.eachProgress);
          for (var j = 0; j < this.eachProgress.length; j++) {
            this.eachNum += this.eachProgress[j];
          }
        }
      });
    },
    customColorMethod(percentage) {
      if (this.eachNum * 0.3 < percentage) {
        return '#67c23a';
      } else if (this.eachNum * 0.7 < percentage) {
        return '#e6a23c';
      } else {
        return '#999999';
      }
    },
    customColorMethods(percentage) {
      // console.log(percentage);
      if (percentage < 30) {
        return '#999999';
      } else if (percentage > 30 && percentage < 70) {
        return '#e6a23c';
      } else {
        return '#67c23a';
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 88%;
  height: 88%;
  margin-left: 8%;
  overflow: auto;
  // width: 88%;
  // height: 70%;
  // /* min-height: 300px; */
  // margin-left: 10%;
  // overflow: auto;
  // /* margin-top: 14px; */
  // position: absolute;
  // top: 50px;
  .listBox {
    margin-top: 10px;
  }
}

>>> .el-progress-bar__innerText {
  color: rgb(9, 33, 107);
}
</style>
