<template>
  <el-dialog
    v-if="visibleDialog"
    :visible.sync="visibleDialog"
    :append-to-body="true"
    width="75%"
    @close="closeDialog"
  >
    <el-row>
      <el-col>
        <el-input
          icon="el-icon-search"
          type="search"
          style="display: inline-block; width: 150px"
        ></el-input>
        <el-button type="primary" @click="searchInfo()">搜索</el-button>
        <list-table
          :border="true"
          :columns="columns"
          :dataSource="dataList"
          :pagination="pagination"
          :mulSelect="true"
          @selectionChange="selectionChange"
          v-on:currentChange="handleCurrentChange"
          v-on:sizeChange="handleSizeChange"
        >
        </list-table>
      </el-col>
      <el-col align="center" style="padding-top: 10px">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="itemClick">确 定</el-button>
        <!-- <el-button type="primary" @click="addRow">继续添加</el-button> -->
      </el-col>
    </el-row>
  </el-dialog>
</template>

<script>
export default {
  name: "addManager.vue",
  data() {
    return {
      selectedData: [],
      visibleDialog: false,
      dataList: [{ personalName: "aaa",id:1 }, { personalName: "bbb",id:2 }],
      columns: [
        {
          code: "index",
          label: "序号",
          width: 50,
          align: "left",
          type: "num"
        },
        {
          code: "personalName",
          label: "姓名",
          align: "left",
          type: "link",
        },
        {
          code: "sex",
          label: "性别",
          align: "left",
          type: "link",
        },
        { code: "age", label: "年龄", align: "center" },
        { code: "firmName", label: "单位", align: "center" },
        { code: "contactNumber", label: "联系电话", align: "center" },
        { code: "department", label: "部门", align: "center" },
        { code: "test6", label: "职务", align: "center" },
      ],
      pagination: {
        //分页默认值
        totalSize: 0,
        currentPage: 1,
        pageSize: 10,
        position: "center",
        layout: "total,pager,sizes,prev,next,jumper",
      },
    };
  },
  methods: {
    selectionChange(val) {
      this.selectedData = val;
    },
    handleCurrentChange(val) {
      // console.log(val);
    },
    handleSizeChange(val) {
      // console.log(val);
    },
    // 关闭弹框
    closeDialog() {
      this.visibleDialog = false;
      this.$emit("closeDialog");
    },
    itemClick() {
      this.visibleDialog = false;
      this.$emit("addClick", this.selectedData);
    },
    //搜索功能
    searchInfo() {},
    //继续添加按钮，跳转到index界面的新建按钮功能
    addRow() {
      this.$emit("addClick", this.selectedData);
    },
  },
};
</script>
<style  scoped>
.content_table {
  /* background: url(../../assets/tablebg.png) no-repeat center; */
  background-size: 100% 100%;
}
.content_table input {
  width: 150px;
  height: 20px;
  /* background: #55d8ff; */
}

.dialog-footer {
  display: flex;
  justify-content: center;
}
.dialogBtn {
  color: #fff;
}
</style>
