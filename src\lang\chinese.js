export default {
  Handyman: "杂工",
  electricWelder: "电焊工",
  administrativestaff: "管理人员",
  Piledriveroperator: "桩机操作工",
  Generalworkers: "普工",
  Smartconstruction: "智慧建造",
  IntelligentManageability: "智慧管理",
  SmartChuang: "智慧创安",
  Intelligentimprovement: "智慧提质",
  Smartgreening: "智慧增绿",
  SmartHealthCreation: "智慧创卫",
  Watersaving: "节水管理",
  Energysaving: "节电管理",
  Labormanagement: "劳务管理",
  Videomanagement: "视频管理",
  Elevatormonitoring: "升降机监测",
  Towercrane: "塔吊监测",
  Distributionbox: "配电箱监测",
  Intelligentmeasurement: "智能测量",
  Vehiclewashing: "车辆冲洗监测",
  ProjectCarbon: "项目碳管理",
  foreman: "工长",
  qualifiedlevel: "水平仪平均合格率",
  guidingruler: "靠尺平均合格率",
  rangefinder: "测距仪平均合格率",
  anglerule: "角尺平均合格率",
  averageRate: "平均合格率",
  grounding: "接地",
  break: "断开",
  SmokeDetector: "烟感",
  hightemperature: "高温",
  shortcircuit: "短路",
  leakageofelectricity: "漏电",
  overload: "过载",
  outage: "断电",
  Mobilization: "进场",
  Exit: "出场",
  Rinsed: "已冲洗",
  Insufficientflushingtime: "冲洗时间不足",
  Driveoutarea: "直接驶出冲洗区",
  Detourwithoutrinsing: "绕道未冲洗",
  other: "其它",
  qualifiedtrainees: "培训合格人次",
  trainingpeople: "培训中人次",
  Untrainedpersonnel: "未培训人次",
  unqualifiedpersonnel: "不合格人次",
  Artificialcarbonemissions: "人工碳排",
  Materialcarbonemissions: "材料碳排",
  Mechanicalcarbonemissions: "机械碳排",
  Constructionwastedischarge: "建筑垃圾排放",
  Recyclingcarbonemissions: "回收减排",
  Anticollisions: "防碰撞",
  Bricklayer: "砌筑工",
  number: "数量",
  Proportion: "占比",
  Completed: "已完成",
  inprogress: "进行中",
  Notstarted: "未开始",
  Totalnumbertasks: "总任务数",
  people: "人",
  Totalattendancetoday: "今日出勤总人数",
  Administrativearea: "办公区",
  Constructionarea: "施工区",
  livingquarters: "生活区",
  total: "总",
  Accumulated: "累计",
  Averagepassrate: "平均合格率",
  largeScreen: "大屏展示",
  equipmentManagement: "设备管理",
  Operation: "运行情况",
  alarmRecord: "报警记录",
  antiCollision: "防碰撞管理",
  hookVisualization: "吊钩可视化",
  todayAlarm: "今日报警排序",
  earlywarning: "预警",
  giveAlarm: "报警",
  load: "载重",
  WindSpeed: "风速",
  Incline: "倾斜",
  moment: "力矩",
  PleaseSelect: "请选择",
  NoData: "无数据",
  WarningAndAlarmStatistics: "预警报警统计",
  Day: "日",
  Month: "月",
  History: "历史",
  AlarmTypeStatistics: "报警类型统计",
  Whole: "全部",
  ListOfTowerCranes: "塔吊列表",
  Increase: "添加",
  edit: "修改",
  delete: "删除",
  BasicQquipment: "设备基本信息与设置",
  EquipmentModel: "设备型号",
  EquipmentManager: "设备管理人",
  InstallationPosition: "安装位置",
  ConstructionCommissionNumber: "建委编号",
  Telephone: "联系电话",
  DriverInformation: "司机信息",
  Age: "年龄",
  ID: "身份证号",

  WarningThreshold: "预警阈值",
  LoadWarning: "载重预警",
  LoadAlarm: "载重报警",
  WindSpeedWarning: "风速预警",
  WindSpeedAlarm: "风速报警",
  MomentPercentageWarning: "力矩百分比预警",
  MomentPercentageAlarm: "力矩百分比报警",
  TiltAngleWarning: "倾角预警",
  TiltAngleAlarm: "倾角报警",
  AntiCollisionWarning: "防碰撞预警",
  AntiCollisionAlarm: "防碰撞报警",
  LimitValue: "限位值",
  RotationLimitL: "回转限位（左）",
  RotaryLimitR: "回转限位（右）",
  AmplitudeLimitN: "  幅度限位（近）",
  AmplitudeLimitF: "幅度限位（远）",
  HeightLimitU: "	高度限位（上）",
  HeightLimitL: "高度限位（下）",
  RealTimeData: " 塔吊监测实时数据",
  TowerCraneParameters: "塔吊参数",
  BalanceArmLength: "平衡臂长度",
  BoomLength: "吊臂长度",
  BoomHeight: "吊臂高度",
  TowerHeight: "塔身高度",
  TowerCapHeight: "塔帽高度",
  RealTime: "实时数据",
  MomentPercentage: "力矩百分比",
  DipAngle: "倾角",
  MinimumCollisionPrevention: "最小防碰撞",
  Rotation: "回转角度",
  Amplitude: "幅度",
  Height: "高度",
  Date: "日期",
  StartDate: "开始日期",
  To: "至",
  EndDate: "结束日期",
  Query: "查询",
  SerialNumber: "序号",
  LiftingTime: "起吊时间",
  UnloadingTime: "卸吊时间",
  MaximumMoment: "最大力矩百分比",
  LiftingPointHeight: "起吊点高度",
  UnloadingPointHeight: "卸吊点高度",
  MaximumHeight: "最大高度",
  LiftingPointAmplitude: "起吊点幅度",
  UnloadingPointAmplitude: "卸吊点幅度",
  RotationAngleLift: "起吊点回转角度",
  RotationAngleUnloading: "卸吊点回转角度",
  AlarmLevel: "报警级别",
  EarlyWarning: "预警",
  RecordingTime: "记录时间",
  AlarmReason: "报警原因",
  TowerList: "塔吊列表",

  ElevatorMonitoring: "升降机监测",
  Increases: "新增",
  LoadInformationStatistics: "载重信息统计",
  StatisticsOfViolation: "违章类型统计",
  StatisticsOfElevator: "升降机违章记录统计",
  Week: "周",
  RedLight: "红灯",
  Overload: "超载",
  HistoricalOfElevator: "升降机违章记录历史数据",
  ListOfElevators: "升降机列表",
  DeviceName: "设备名称",
  EquipmentNumber: "设备编号",
  CCFN: " 建委备案号",
  Manufacturer: "生产厂家",
  Photo: "照片",
  SelectFile: "选取文件",
  QualificationCertificate: "资格证",
  Cancellation: "取消",
  Determine: "确定",
  PleaseEnter: " 请输入",
  Edits: "编辑",
  SuccessfullySaved: "保存成功",
  SuccessfullyDel: "删除成功",
  Fail: "失败",
  FailSaved: "保存失败",
  AcquisitionFailed: "获取失败",
  Prompt: "提示",
  deleteInfo: "确认要删除本条信息吗？请注意删除后无法恢复数据。",
  Success: "成功",
  UploadSuccessful: "上传成功",
  ImportSuccessful: "导入成功",
  ImportFail: "导入失败",
  MaximumSpeedWarning: "最大速度预警",
  MaximumSpeedAlarm: "最大速度报警",
  MaximumTiltWarning: "最大倾斜预警",
  MaximumTiltAlarm: "最大倾斜报警",
  MaximumLoadWarning: "最大载重预警",
  MaximumLoadAlarm: "最大载重报警",
  MaximumAltitudeWarning: "最大高度预警",
  MaximumHeightAlarm: "最大高度报警",
  RealTimeScreen: "实时画面",
  RealTimeElevator: "升降机监测实时数据",
  Direction: "方向",
  Speed: "速度",
  XInclination: " X倾斜度",
  YInclination: " Y倾斜度",
  RunningState: " 运行状态",
  QueryDate: "查询日期",
  WarningLevel: "预警级别",
  StartingTime: "起点时间",
  EndTime: "终点时间",
  StartingHeight: "起点高度",
  EndHeight: "终点高度",
  MovingDistance: "移动距离",
  AverageSpeed: "平均速度",
  MaximumX: "X向最大倾斜度",
  MaximumY: "Y向最大倾斜度",
  ReasonViolation: "违规原因",
  // 项目首页
  ProjectInformation: "项目信息",
  Personalization: "个性化设置",
  Homepage: "返回驾驶舱",
  Enterprise: "返回企业级",
  OneClickReporting: "一键填报",
  ProjectIntroduction: "项目简介",
  Video: "视频",
  recordVideo: " 若录制视频并下载到本地，请用海康播放器播放！点击下载",
  VideoList: "视频列表",
  OnLine: "在线",
  ManagementOfPresetPoints: "预设点管理",
  Realtime: "实时",
  Playback: "回放",
  Devicename: "设备名称",
  Cameraname: "摄像头名称",
  Placementlocation: "放置地点",
  State: "状态",
  Isitenabled: "是否启用",
  Operate: " 操作",
  Hiddendanger: "隐患",
  Time: "时间",
  Hiddendangername: "隐患名称",
  Hiddendangerlocation: "隐患位置",
  StartTime: "开始时间",
  EndTimes: "结束时间",
  Hiddendangerimages: "隐患图片",
  state: "状态",
  View: "查看",
  Notmanuallyconfirmed: "未人工确认",
  Projecttitle: "工程名称",
  PROJECTNO: "工程编号",
  Projectlocation: "工程地点",
  Detailedaddress: "工程详细地址",
  Projectstatus: "项目状态",
  Projectscale: "工程规模",
  Engineeringtype: "工程类型",
  Setpresetpoints: "设置预设点",
  Presetpointname: "预设点名称",
  enterpreset: "请输入预设点名称",
  deviceOffline: "设备不在线，请检查设备网络或重启设备接入萤石云",
  Adddeviceinformation: "添加设备信息",
  YingshiAPPKey: "萤石APPKey",
  YingshiSecret: "萤石Secret",
  Equipmentserialnumber: "设备序列号",
  Deviceverificationcode: "设备验证码",
  Offline: "离线",
  Enable: "启用",
  Disable: "禁用",
  deleteFile: "此操作将永久删除该文件，是否继续？",
  Cancelleddeletion: "已取消删除",
  Modifyingdeviceinformation: "修改设备信息",
  CameraName: "摄像头名称",
  customization: {
    officeArea: "办公区",
    constructionArea: "施工区",
    livingArea: "生活区",
    alarmsNumber: "报警次数",
    alarmWeight: "重量报警",
    alarmElectricity: "电流报警",
    alarmX: "倾斜X报警",
    alarmY: "倾斜Y报警",
    others: "其他",
    electrical: "电气",
    HVAC: "暖通",
    outWater: "给排水",
    structure: "结构",
    building: "建筑",
    drawing: "图纸",
    changeTalk: "变更洽谈",
    '钢筋/吨': "钢筋/吨",
    '钢管/吨': "钢管/吨",
    '聚合物防水砂浆/立方米': '聚合物防水砂浆/立方米',
    '混凝土/立方米': '混凝土/立方米',
    '加气块/立方米': '加气块/立方米',
    '模板/平方米': '模板/平方米',
    masonry: "砌体及砌筑砂浆",
    concrete: "混凝土",
    WaterproofMaterials: "防水材料",
    ThermalInsulationMaterials: "保温材料",
    ReinforcementAndJoints: "钢筋及接头",
    materialNumer: "物资验收的次数",
    witnessNumber: "需要见证的次数",
    qualified: "合格",
    unQuaDone: "不合格已整改",
    unQuaUn: "不合格待整改",
    safetyTotal: "安全检查总数",
    qualityTotal: "质量检查总数",
    week: "本周",
    month: "本月",
    year: "本年",
    testTotal: "总实验数量",
    buildAndWater: "建筑给排水及采暖工程",
    buildElect: "建筑电气工程",
    savePower: "节能工程",
    windAndAir: "通风与空调工程",
    commonTable: "通用表格",
    smartSmoke: "智能烟感总数量",
    normal: "正常",
    alarm: "报警",
    fault: "故障",
    smartHatNum: "智能安全帽数量",
    onlineNum: "在线数量",
    unHatAlarm: "脱帽报警",
    fenceAlarm: "围栏报警",
    temperatureAlarm: "温度报警",
    fallAlarm: "跌落报警",
    rules: "规章制度",
    constructCase: "施工组织方案",
    techGive: "技术交底",
    constructSpecCase: "施工专项方案",
    anchor: "锚索轴力",
    horazitalMove: "水平位移监测",
    arroundBuildTile: "周边建筑物倾斜风险",
    totalPoint: "总点位数",
    totalAlarm: "总预/报警次数",
    reSaveWeight: "回收质量",
    reSavePercent: "回收占比",
    abandonedWood: "废弃木头",
    abandonedSavePlate: "废弃保温板",
    abandonedBrick: "废旧砖头",
    abandonedConcrete: "废弃混凝土块",
    total: "总",
    cumulative: "累积",
    xAlarm: "X倾斜报警",
    yAlarm: "Y倾斜报警",
    overWeightAlarm: "超重报警",
    xeAlarm: "X倾斜预警",
    yeAlarm: "Y倾斜预警",
    overWeighteAlarm: "超重预警",
    liveAndWork: "生活区、办公区管理",
    cli: "脚手架",
    templateSys: "模板支撑体系",
    safeDefend: "安全防护",
    tempElec: "临时用电",
    towerAndweight: "塔式起重机、起重吊装",
    machineSafe: "机械安全",
    cleanAct: "清洁行动",
    envirProAct: "环境提升行动",
    propagateAcT: "宣传行动",
    clearFour: '除"四害"行动',
    constSafe: "施工安全作业交底",
    safeTra: "安全措施作业交底",
    workTypeb: "工种作业安全交底",
    conEleb: "施工用电安全交底",
    conMac: "施工机械操作交底",
    poleInclination: "立杆倾斜",
    poleForce: "立杆轴力",
    templateDown: "模板沉降",
    horizontalMove: "水平位移",
    sideSite: "旁站数量",
    problemNum: "问题数量",
    totalNum: "总数",
    electNet: "电动绿网防尘天幕",
    soildPen: "防尘隔离棚",
    hazardIdentify: "隐患识别",
    faceIdentify: "人脸识别",
    remoteTeach: "远程技术指导",
    hiddRec: "隐蔽工程记录",
    keyRec: "关键工序记录",
    realTem: "实时温度",
    realHumidity: "实时湿度",
    onecPass: "一次性通过率",
    importDanger: "重大隐患",
    smallDanger: "一般隐患",
    // 安全验收数据项
    livingAreaManagement: "生活区管理",
    safetyAcceptanceTotal: "总数",
    // 安全交底数据项
    constructionWorkDisclosure: "施工作业交底",
    safetyMeasuresDisclosure: "安全措施交底",
    workTypeDisclosure: "工种作业交底",
    constructionElectricityDisclosure: "施工用电交底",
    mechanicalOperationDisclosure: "机械操作交底",
    safetyDisclosureTotal: "总数",
    // 升降机监测数据项
    elevatorLoad: "载重",
    elevatorIncline: "倾斜",
    elevatorSpeed: "速度",
    elevatorHeight: "高度",
    elevatorMonitoringTotal: "总预警数",
    // 高支模监测总数
    highFormworkMonitoringTotal: "总预警数",
    // 爱国卫生运动总数
    patrioticHealthTotal: "活动总数",
    // 塔吊监测
    towerCraneTotal: "总数",
    towerCraneWarning: "报警",
    towerCraneEarly: "预警",
    towerCraneLoad: "载重",
    towerCraneWindSpeed: "风速",
    towerCraneMoment: "力矩",
    towerCraneIncline: "倾角",
    towerCraneAntiCollision: "防碰撞",

    // 防尘隔离
    dustIsolationTotal: "总数",
    dustIsolationShed: "防尘隔离棚",
    dustIsolationCanopy: "电动防尘天幕",

    // 项目碳管理
    projectCarbonTotal: "总数",
    projectCarbonArtificial: "人工碳排",
    projectCarbonMaterial: "材料碳排",
    projectCarbonMechanical: "机械碳排",
    projectCarbonWaste: "建筑垃圾排放",
    projectCarbonRecycle: "回收减排",
    // 智能眼镜
    smartGlassesTotal: "总数",
    // 龙门吊监测
    gantryCraneTotal: "总报警数",
    // 卸料平台监测
    unloadPlatformTotal: "总预警数",
    // 见证取样
    witnessSampTotal: "总预警数",
    // 进度管理
    progressManageTotal: "总任务数",
    // 安全检查
    securityCheckTotal: "安全检查总数",
    // 质量检查
    qualityTestTotal: "质量检查总数",
    // 智能烟感
    smartSmokeTotal: "烟感总数",
    // 现场试验
    fieldTestTotal: "总试验数",
    // 施工设备监测
    constructionEquipmentTotal: "设备总数",
  },
};
