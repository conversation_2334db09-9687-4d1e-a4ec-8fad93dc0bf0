<!--
 * @Description: 进度管理
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-25 15:11:45
 * @LastEditors: dong<PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="ProgressManageRef"
      >
        <div
          id="ProgressManageChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>

import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getProgressManage } from "@/api/echrtsApi";
export default {
  components: {},
  name: "ProgressManage",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "ProgressManageChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.progressManageTotal"),
        seriesCenter: ["25%", "58%"],
        richNameWidth: 40,
        titleInfor: {
          text: "0",
          subtext: this.$t("Totalnumbertasks"),
          x: "30%",
          y: "30%",
          textAlign: "center",
          textStyle: {
            color: "#fff",
            fontSize: 20,
            // fontStyle: 'bolder',
          },
          subtextStyle: {
            color: "#fff",
            fontSize: 12,
          },
          // left: 'center',
        },
        
        tooltipFormatter: `{b}<br /> ${this.$t("number")}：{c}<br />${this.$t(
          "Proportion"
        )}：{d}%`,
        costomLegendFormatter:function(name){
          return name;
        },
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.ProgressManageRef.offsetWidth;
      this.barHeight = this.$refs.ProgressManageRef.offsetHeight;
    },
    getBarData() {
      getProgressManage()
        .then((res) => {
          const {
            statusCode,
            data: { dataList, totalNumber },
          } = res.data;
          if (statusCode == 200) {
            if (dataList.length > 0) {
              let legendFormatter = (name) => {
                const item = dataList.find((i) => {
                  return i.name === name;
                });
                const p = item.value;
                switch (name) {
                  case "已完成":
                    name = this.$t("Completed");
                    break;
                  case "进行中":
                    name = this.$t("inprogress");
                    break;
                  case "未开始":
                    name = this.$t("Notstarted");
                    break;
                }
                return "{name|" + name + "}" + "{percent|" + p + "}";
              };
              this.pieParams.legendFormatter = legendFormatter;
            }

            this.pieParams.titleInfor.text = totalNumber;
            this.pieParams.data = dataList.map((item) => {
              switch (item.name) {
                case "已完成":
                  item.name = this.$t("Completed");
                  break;
                case "进行中":
                  item.name = this.$t("inprogress");
                  break;
                case "未开始":
                  item.name = this.$t("Notstarted");
                  break;
              }
              return item;
            });
            drawAnnularChart(this.pieParams);
          }
        })
        .catch(() => { });
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
