import Axios from '@/router/axios'
// 实时查询折线图
export function getLineData(query) {
  return Axios({
    url: "/api/environment-detection-module/chart-data",
    method: 'post',
    data: query
  })
}
// 图表总览--空心饼图
export function getPieHollow(query) {
  return Axios({
    url: `/api/environment-detection-module/real-time-info/${query}`,
    method: 'get',
  })
}
// 图表总览--实心饼图
export function getPieCicle(query) {
  return Axios({
    url: "/api/environment-detection-module/statistical-pie-chart",
    method: 'post',
    data: query
  })
}
// 图表总览--折线图
export function getOverviewLine(query) {
  return Axios({
    url: `/api/environment-detection-module/statistical-info/${query}`,
    method: 'get',
    data: query
  })
}
