<!--登录界面-->
<template>
  <div class="home-page-wrap">
    <div class="leftStyle">
      <template v-for="(items, key) in componentData">
        <component
          :key="key"
          v-if="leftNumber.includes(items.moduleSort)"
          :is="items.component"
          :moduleName="items.moduleName"
          :allInfor="items"
        />
      </template>
    </div>
    <div class="middle">
      <div class="content">
        <TitleType
          @titleClick="titleClick"
          @titleClickRight="titleClickRight"
          class="btn-wrap"
        ></TitleType>
        <div
          class="pro-info"
          v-show="showPro"
          :style="{ backgroundImage: 'url(' + backgroudImg + ')' }"
        >
          <div
            :class="Isexpand ? 'right_show_div expand' : 'right_show_div '"
            style="display: block"
          >
            <div
              class="left_img"
              @click="toggleInfo"
              v-if="$IsProjectShow&&language=='en'"
            >
              <!-- 阿联酋图片翻译 -->
              <img
                v-if="!Isexpand"
                src="../../assets/btn_xinms.png"
              />
              <img
                v-if="Isexpand"
                src="../../assets/2s.png"
              />
            </div>
            <div
              class="left_img"
              @click="toggleInfo"
              v-else
            >
              <img
                v-if="!Isexpand"
                src="../../assets/btn_xinm.png"
              />
              <img
                v-if="Isexpand"
                src="../../assets/2.png"
              />
            </div>
            <div class="right_list_box detail_list">
              <h5> {{
            $t(`ProjectIntroduction`)
          }}</h5>
              <ul>
                <li>{{
            $t(`Projecttitle`)
          }}：{{ IntroductionList.authorityProjectName }}</li>
                <li>{{
            $t(`PROJECTNO`)
          }}：{{ IntroductionList.authorityProjectCode }}</li>
                <li>
                  {{
            $t(`Projectlocation`)
          }}：{{ IntroductionList.provinceName
                  }}{{ IntroductionList.cityname }}
                </li>
                <li>{{
            $t(`Detailedaddress`)
          }}：{{ IntroductionList.address }}</li>
                <li>{{
            $t(`Projectstatus`)
          }}：{{ IntroductionList.projectStatus }}</li>
                <li>{{
            $t(`Projectscale`)
          }}：{{ IntroductionList.projectArea }}</li>
                <li>
                  {{
            $t(`Engineeringtype`)
          }}：{{ IntroductionList.authorityProjectTypeName }}
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div
          class="video"
          v-show="showVideo"
        >
          <video
            ref="videoPlayer"
            controls
            :src="videoUrl"
            controlslist="nodownload"
            style="width: 100%; height: 100%; object-fit: fill"
          ></video>
          <!-- @timeupdate="updateTime"
          @pause="toPause"-->
          <!-- @play="toPlays" -->
          <!-- :poster=posterUrl -->
          <!-- 展示图postURl -->
          <!-- controls 是否显示控件 -->
          <!-- :class="[porel,fullwidth,mal,fla,mat,'movie-show-video']" -->
        </div>
      </div>
      <div class="bottom">
        <ModuleType
          v-if="$isShowHeader"
          @moduleTypeClick="moduleTypeClick"
          class
        ></ModuleType>
      </div>
    </div>
    <div class="rightStyle">
      <template v-for="(items, key) in componentData">
        <component
          :key="key"
          v-if="rightNumber.includes(items.moduleSort)"
          :is="items.component"
          :moduleName="items.moduleName"
          :allInfor="items"
        />
      </template>
    </div>
    <Customize
      @closeCustomize="closeCustomize"
      :customizeVisible="customizeVisible"
      v-if="customizeVisible"
      :CustomizeList="CustomizeList"
      :personCustomize="personCustomize"
    />
    <!-- <div class="CustomizeShow" @click="setCustomize()">
      <img :src="iconImg" alt="图片" />
      <p>个性化设置</p>
    </div> -->
    <Report ref="Report" />
    <!-- <div class="ReportShow" @click="reportClick()">
      <img :src="iconImg" alt="图片" />
      <p>一键填报</p>
    </div> -->
    <div class="btn-box">
      <div
        class="goback reportBack"
        v-if="isShow"
        @click="setCustomize"
      >
        <img
          src="@/assets/customize/bjIcon.png"
          alt="图片"
        />
        <span class="iconImg"> <img
            :src="iconImg"
            alt="图片"
          /></span><span class="Customize">{{
            $t(`Personalization`)
          }}</span>
      </div>
    </div>
    <!-- 一键填报入口 -->
    <div
      class="btn-box"
      style="margin-top:45px;"
    >
      <div
        class="goback reportBack"
        v-if="isShow"
        @click="reportClick"
      >
        <img
          src="@/assets/customize/bjIcon.png"
          alt="图片"
        />
        <span class="iconImg"> <img
            :src="iconReport"
            alt="图片"
          /></span><span class="Report">{{
            $t(`OneClickReporting`)
          }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { getUrlParams } from "@/util/util.js";
import {
  buryPoint
} from '@/api/home';
import iconImg from "@/assets/customize/icon.png";
import iconReport from "@/assets/customize/report.png";
import ModuleType from "./moduleType";
import Customize from "@/components/Customize/index.vue";
import Report from "@/components/Report/index.vue";
import TitleType from "./titleType";
// import geoJSON from "@/assets/json/geoinfo-all.json";
import { getIntroduction } from "@/api/constructionRecord.js";
import VueAliplayerV2 from "vue-aliplayer-v2";
import { gitVideoList } from "@/api/video.js";
import { getAllModule, userConfigure } from "@/api/customizeApi";
// import GantryCrane from "@/assets/customize/龙门吊监测.png";
// import MaterialManage from "@/assets/customize/材料管理.png";
// import NowMeasure from "@/assets/customize/智能测量.png";
// import WaterSave from "@/assets/customize/节水管理.png";
// import PowerSave from "@/assets/customize/节电管理.png";
// import LabourServices from "@/assets/customize/劳务管理.png";
// import VideoManage from "@/assets/customize/视频监控.png";
// import CarWash from "@/assets/customize/车辆冲洗.png";
// import ProjectCarbon from "@/assets/customize/项目碳管理.png";
// import DistributionBox from "@/assets/customize/配电箱监测.png";
// import TowerCrane from "@/assets/customize/塔吊监测.png";
// import ElevatorSurvey from "@/assets/customize/升降机监测.png";
// import DangerRisk from "@/assets/customize/隐患风险.png";
// import DrawingManage from "@/assets/customize/图纸管理.png";
// import SafetyInspection from "@/assets/customize/智能巡检.png";
// import ElevatorMonitor from "@/assets/customize/吊篮监测.png";
// import QualityAcceptance from "@/assets/customize/质量验收一次性通过率.png";
// import EngineeringData from "@/assets/customize/工程资料.png";
// import DustproofIsolation from "@/assets/customize/防尘隔离.png";
// import SmartGlasses from "@/assets/customize/智能眼镜.png";
// import SiteSupervision from "@/assets/customize/监理旁站.png";
// import HighFormwork from "@/assets/customize/高支模监测.png";
// import SafetyDisclosure from "@/assets/customize/安全交底.png";
// import PatrioticHealth from "@/assets/customize/爱国卫生运动.png";
// import SafetyAcceptance from "@/assets/customize/安全验收.png";
// import AutomaticSpray from "@/assets/customize/自动喷淋.png";
// import UnloadPlatform from "@/assets/customize/卸料平台监测.png";
// import ConstructionWaste from "@/assets/customize/建筑垃圾管理.png";
// import StandardRoom from "@/assets/customize/标养室管理.png";
// import FoundationPit from "@/assets/customize/基坑监测.png";
// import SchemeManage from "@/assets/customize/方案管理.png";
// import SafetyHat from "@/assets/customize/智能安全帽.png";
// import FieldTest from "@/assets/customize/现场试验.png";
// import SmartSmoke from "@/assets/customize/智能烟感.png";
// import QualityTest from "@/assets/customize/质量检查.png";
// import SecurityCheck from "@/assets/customize/安全检查.png";
// import VehicleManage from "@/assets/customize/车辆管理.png";
// import ProgressManage from '@/assets/customize/进度管理.png';
// import MaterialCheck from "@/assets/customize/物资验收.png";
// import WitnessSamp from "@/assets/customize/见证取样.png";
// import SafetyEducation from "@/assets/customize/安全教育.png";
export default {
  name: "constructionRecord",
  data() {
    return {
      iconImg,
      iconReport,
      source: "",
      videoOptions: "",
      videoValue: "",
      videoUrl: "",
      backgroudImg: "",
      showMap: true,
      showPro: true,
      showVideo: false,
      Isexpand: false,
      // addArr: {},
      projectId: 0,
      userId: null,
      IntroductionList: {},
      customizeVisible: false,
      CustomizeList: [],
      personCustomize: [],
      // componentName: qualityAcceptanceOne,
      componentList: [],
      leftNumber: [1, 3, 5],
      rightNumber: [2, 4, 6],
      isShow: true,
      tempShow: false,
      language: 'zh',
      //   zh_enList: [
      //   {value: 'Monitoring of gantry cranes',name: '龙门吊监测'},
      //   {value: 'Material Management',name: '材料管理'},
      //   {value: 'Safety education',name: '安全教育'},
      //   {value: 'Witness sampling',name: '见证取样'},
      //   {value: 'Material acceptance',name: '物资验收'},
      //   {value: 'Progress management',name: '进度管理'},
      //   {value: 'Vehicle management',name: '车辆管理'},
      //   {value: 'Security check',name: '安全检查'},
      //   {value: 'Quality inspection',name: '质量检查'},
      //   {value: 'Intelligent smoke sensing',name: '智能烟感'},
      //   {value: 'Field test',name: '现场试验'},
      //   {value: 'Smart safety helmet',name: '智能安全帽'},
      //   {value: 'Programme Management',name: '方案管理'},
      //   {value: 'Foundation pit monitoring',name: '基坑监测'},
      //   {value: 'Standard maintenance room management',name: '标养室管理'},
      //   {value: 'Construction waste management',name: '建筑垃圾管理'},
      //   {value: 'Unloading platform monitoring',name: '卸料平台监测'},
      //   {value: 'Foundation pit monitoring',name: '基坑监测'},
      //   {value: 'Automatic spray',name: '自动喷淋'},
      //   {value: 'Safety acceptance',name: '安全验收'},
      //   {value: 'Patriotic health campaign',name: '爱国卫生运动'},
      //   {value: 'Safety briefing',name: '安全交底'},
      //   {value: 'High support formwork monitoring',name: '高支模监测'},
      //   {value: 'Supervision site',name: '监理旁站'},
      //   {value: 'Dust isolation',name: '防尘隔离'},
      //   {value: 'Smart glasses',name: '智能眼镜'},
      //   {value: 'Engineering data',name: '工程资料'},
      //   {value: 'One-time pass rate of quality acceptance',name: '质量验收一次性通过率'},
      //   {value: 'Basket monitoring',name: '吊篮监测'},
      //   {value: 'Intelligent inspection',name: '智能巡检'},
      //   {value: 'Drawing management',name: '图纸管理'},
      // ]
      // ImgList: [
      //   {value: 'GantryCrane',name: '龙门吊监测',url: GantryCrane},
      //   {value: 'MaterialManage',name: '材料管理',url: MaterialManage},
      //   {value: 'SafetyEducation',name: '安全教育',url: SafetyEducation},
      //   {value: 'WitnessSamp',name: '见证取样',url: WitnessSamp},
      //   {value: 'MaterialCheck',name: '物资验收',url: MaterialCheck},
      //   {value: 'ProgressManage',name: '进度管理',url: ProgressManage},
      //   {value: 'VehicleManage',name: '车辆管理',url: VehicleManage},
      //   {value: 'SecurityCheck',name: '安全检查',url: SecurityCheck},
      //   {value: 'QualityTest',name: '质量检查',url: QualityTest},
      //   {value: 'SmartSmoke',name: '智能烟感',url: SmartSmoke},
      //   {value: 'FieldTest',name: '现场试验',url: FieldTest},
      //   {value: 'SafetyHat',name: '智能安全帽',url: SafetyHat},
      //   {value: 'SchemeManage',name: '方案管理',url: SchemeManage},
      //   {value: 'FoundationPit',name: '基坑监测',url: FoundationPit},
      //   {value: 'StandardRoom',name: '标养室管理',url: StandardRoom},
      //   {value: 'ConstructionWaste',name: '建筑垃圾管理',url: ConstructionWaste},
      //   {value: 'UnloadPlatform',name: '卸料平台监测',url: UnloadPlatform},
      //   // {value: 'Foundation pit monitoring',name: '基坑监测',url: '@/assets/customize/基坑监测.png'},
      //   {value: 'AutomaticSpray',name: '自动喷淋',url: AutomaticSpray},
      //   {value: 'SafetyAcceptance',name: '安全验收',url: SafetyAcceptance},
      //   {value: 'PatrioticHealth',name: '爱国卫生运动',url: PatrioticHealth},
      //   {value: 'SafetyDisclosure',name: '安全交底',url: SafetyDisclosure},
      //   {value: 'HighFormwork',name: '高支模监测',url: HighFormwork},
      //   {value: 'SiteSupervision',name: '监理旁站',url: SiteSupervision},
      //   {value: 'DustproofIsolation',name: '防尘隔离',url: DustproofIsolation},
      //   {value: 'SmartGlasses',name: '智能眼镜',url: SmartGlasses},
      //   {value: 'EngineeringData',name: '工程资料',url: EngineeringData},
      //   {value: 'QualityAcceptance',name: '质量验收一次性通过率',url: QualityAcceptance},
      //   {value: 'ElevatorMonitor',name: '吊篮监测',url: ElevatorMonitor},
      //   {value: 'SafetyInspection',name: '智能巡检',url: SafetyInspection},
      //   {value: 'DrawingManage',name: '图纸管理',url: DrawingManage},

      //   {value: 'NowMeasure',name: '智能测量',url: NowMeasure},
      //   {value: 'DangerRisk',name: '隐患',url: DangerRisk},
      //   {value: 'PowerSave',name: '节电管理',url: PowerSave},
      //   {value: 'WaterSave',name: '节水管理',url: WaterSave},
      //   {value: 'LabourServices',name: '劳务管理',url: LabourServices},
      //   {value: 'VideoManage',name: '视频监控',url: VideoManage},
      //   {value: 'CarWash',name: '车辆冲洗',url: CarWash},
      //   {value: 'ProjectCarbon',name: '项目碳管理',url: ProjectCarbon},
      //   {value: 'DistributionBox',name: '配电箱监测',url: DistributionBox},
      //   {value: 'TowerCrane',name: '塔吊监测',url: TowerCrane},
      //   {value: 'ElevatorSurvey',name: '升降机监测',url: ElevatorSurvey},
      // ]
    };
  },
  components: {
    VueAliplayerV2,
    TitleType,
    ModuleType,
    Customize, Report
  },
  watch: {
    "$i18n.locale"(val) {
      if (val) {
        this.language = val;
      }
    },
  },
  async created() {
    this.projectId = getStore({
      name: "projectId"
    }) || getUrlParams().projectId;
    this.getAllVideo();
    this.getAllnews();
    // await this.getAddress(geoJSON); //
  },
  mounted() {
    this.language = getStore({ name: "language" });
    this.getCustomizeList();
    this.userId = getStore({
      name: "userId"
    }) || getUrlParams().userId;
    this.getCutomizeInfor();
  },
  computed: {
    componentData() {
      let data = [];
      const { componentList } = this;
      componentList.forEach(element => {
        element.component = this.loadView(element.component);
        data.push(element);
      });
      return data;
    }
  },
  methods: {
    // 一键填报授权
    reportClick() {
      // 埋点注入
      let params = {
        moduleId: 0,
        moduleName: '一键填报',
        logType: '0003-0000-0001',
        extendJson: '',
        origin: 1,
      };
      buryPoint(params).then((res) => {
        console.log(res, '一键填报埋点');
      });
      this.$refs.Report.open()
    },
    // 获取个人配置
    getCutomizeInfor() {
      userConfigure(this.userId, this.projectId).then(res => {
        let {
          data: { statusCode, data }
        } = res;
        if (statusCode == 200) {
          this.personCustomize = JSON.parse(JSON.stringify(data));
          this.componentList = JSON.parse(JSON.stringify(data));
          if (this.$IsProjectShow) {
            this.personCustomize.forEach((ele) => {
              if (ele.moduleName.includes('智能测量')) {
                ele.moduleImg = '实测实量.png'
                ele.name_zh = '实测实量'
                ele.name_en = 'NowMeasure'
                ele.moduleImg = require('../../assets/customize/智能测量.png')
              }
              else if (ele.moduleName.includes('工程资料')) {
                ele.moduleImg = '工程资料.png'
                ele.name_zh = '工程资料'
                ele.name_en = 'EngineeringData'
                ele.moduleImg = require('../../assets/customize/' + ele.moduleImg + '')
              }
              else if (ele.moduleName.includes('隐患')) {
                ele.moduleImg = '隐患风险.png'
                ele.name_zh = '隐患风险'
                ele.name_en = 'DangerRisk'
                ele.moduleImg = require('../../assets/customize/' + ele.moduleImg + '')
              }
              else {
                const spaceIndex = ele.moduleName.indexOf(" ");
                ele.moduleImg = ele.moduleName.slice(0, spaceIndex) + '.png'
                ele.moduleImg = require('../../assets/customize/' + ele.moduleImg + '')
                ele.name_zh = ele.moduleName.slice(0, spaceIndex)
                ele.name_en = ele.component
              }
              // let obj=  this.ImgList.find(item=>item.value===ele.component)
              //               ele.moduleImg= obj.url
            })
          }
        }
      });
    },

    // 路由懒加载
    loadView(view) {

      // view = view == 'VideoManage' ? 'RainWater' : view;
      // // view = view == 'StandardRoom' ? 'ConstructionEquipment' : view;
      // view = view == 'PowerSave' ? 'RainWater' : view;

      return resolve =>
        require([`@/components/ChartComponent/${view}.vue`], resolve);
    },
    // 得到列表
    getCustomizeList() {
      getAllModule(this.projectId).then(res => {
        let {
          data: { statusCode, data }
        } = res;
        if (statusCode == 200) {
          if (this.$IsProjectShow) {
            data.forEach((ele) => {
              if (ele.moduleName.includes('智能测量')) {
                ele.moduleImg = '实测实量.png'
                ele.name_zh = '实测实量'
                ele.name_en = 'NowMeasure'
                ele.moduleImg = require('../../assets/customize/智能测量.png')
              }
              else if (ele.moduleName.includes('工程资料')) {
                ele.moduleImg = '工程资料.png'
                ele.name_zh = '工程资料'
                ele.name_en = 'EngineeringData'
                ele.moduleImg = require('../../assets/customize/' + ele.moduleImg + '')
              }
              else if (ele.moduleName.includes('隐患')) {
                ele.moduleImg = '隐患风险.png'
                ele.name_zh = '隐患风险'
                ele.name_en = 'DangerRisk'
                ele.moduleImg = require('../../assets/customize/' + ele.moduleImg + '')
              }
              else {
                const spaceIndex = ele.moduleName.indexOf(" ");
                ele.moduleImg = ele.moduleName.slice(0, spaceIndex) + '.png'
                ele.moduleImg = require('../../assets/customize/' + ele.moduleImg + '')
                ele.name_zh = ele.moduleName.slice(0, spaceIndex)
                ele.name_en = ele.component
              }
              // let obj=  this.ImgList.find(item=>item.value===ele.component)
              //   ele.moduleImg= obj.url
            })
          }
          this.CustomizeList = data;
          // console.log(this.CustomizeList,"this.CustomizeList")
        }
      });
    },
    // 关闭配置化弹框
    closeCustomize(val) {
      if (val) {
        // 获取所有配置列表
        this.getCustomizeList();
        this.getCutomizeInfor();
      }
      this.customizeVisible = false;
    },
    // 配置化弹框显示
    setCustomize() {
      const { customizeVisible } = this;
      this.customizeVisible = !customizeVisible;
    },
    videoChange() {
      this.source = this.videoValue;
    },
    // 获取视频列表
    getAllVideo() {
      gitVideoList({
        projectId: this.projectId,
        pageSize: 99999,
        pageIndex: 1,
        name: ""
      }).then(res => {
        this.videoOptions = res.data.data.videos;
        this.source =
          res.data.data &&
          res.data.data.videos.length &&
          res.data.data.videos[0] &&
          res.data.data.videos[0].hdAddress;
        this.videoValue = this.source;
      });
    },
    // 获取项目简介
    getAllnews() {
      getIntroduction(this.projectId).then(res => {
        this.IntroductionList = res.data.data;
        // this. backgroudImg= require('../../assets/pro-info.png')
        this.backgroudImg = this.$BASEURL + this.IntroductionList.indexImage;
        this.videoUrl = this.$BASEURL + this.IntroductionList.indexVideo;
        // console.log(this.IntroductionList, "获取项目简介+++++++++++++++++++");
        if (
          this.IntroductionList.cityname === this.IntroductionList.provinceName
        ) {
          this.IntroductionList.cityname = "";
        }
        if (this.IntroductionList.projectStatus === 1) {
          this.IntroductionList.projectStatus = "未开工";
        } else if (this.IntroductionList.projectStatus === 2) {
          this.IntroductionList.projectStatus = "在施";
        } else if (this.IntroductionList.projectStatus === 3) {
          this.IntroductionList.projectStatus = "停工";
        } else if (this.IntroductionList.projectStatus === 4) {
          this.IntroductionList.projectStatus = "竣工";
        } else if (this.IntroductionList.projectStatus === 0) {
          this.IntroductionList.projectStatus = "";
        }
      });
    },
    // getAddress(arr) {
    //   this.addArr = {};
    //   for (var i = 0; i < arr.length; i++) {
    //     if (arr[i].level === "province" || arr[i].level === "city") {
    //       this.addArr[arr[i].name.replace(/市/gi, "")] = [
    //         Number(arr[i].center.split(",")[0]),
    //         Number(arr[i].center.split(",")[1])
    //       ];
    //     }
    //     if (
    //       Array.isArray(arr[i].districts) &&
    //       (arr[i].level === "province" || arr[i].level === "city")
    //     ) {
    //       this.getAddress(arr[i].districts);
    //     }
    //   }
    // },
    // 首页中间tab点击事件
    titleClick(val) {
      if (val.name === "分公司") {
        this.showMap = true;
        this.showPro = false;
        this.showVideo = false;
      } else if (val.name === this.$t("ProjectIntroduction")) {
        this.showMap = false;
        this.showPro = true;
        this.showVideo = false;
      } else if (val.name === this.$t("Video")) {
        this.showMap = false;
        this.showPro = false;
        this.showVideo = true;
      }
    },
    titleClickRight() {
      // this.tabShowRight = val.name;
      // this.keyTimer = +new Date()
    },
    // 中间下面菜单的点击事件
    moduleTypeClick() { },
    // 项目信息展开隐藏
    toggleInfo() {
      this.Isexpand = !this.Isexpand;
    }
  }
};
</script>
<style lang="scss">
.reportBack {
  height: 32px;
  line-height: 32px;
  img {
    height: 49px;
  }
  span {
    top: 0.6rem !important;
  }
  .Report {
    right: 1.85rem !important;
  }
  .Customize {
    right: 1.25rem !important;
  }
  .iconImg {
    position: relative;
    left: 0.9rem;
    img {
      height: 15px;
    }
  }
}
.home-page-wrap {
  width: 100%;
  // min-width: 1820px;
  height: 100%;
  display: flex;
  padding: 10px;
  box-sizing: border-box;
  position: relative;
  // 个性化设置按钮
  .CustomizeShow {
    position: absolute;
    top: 0px;
    right: 20px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 140px;
    height: 47px;
    line-height: 47px;
    background: url(../../assets/customize/bjIcon.png) no-repeat;
    font-size: 16px;
    font-weight: 400;
    color: #01d4f9;
    img {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }
  }
  // 一键填报按钮
  .ReportShow {
    position: absolute;
    top: 0px;
    right: 160px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 140px;
    height: 47px;
    line-height: 47px;
    background: url(../../assets/customize/bjIcon.png) no-repeat;
    font-size: 16px;
    font-weight: 400;
    color: #01d4f9;
    img {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }
  }
  .leftStyle,
  .rightStyle {
    width: 410px;
    height: 100%;
    min-width: 200px;
    margin: 10px 10px 0;
    display: flex;
    flex-direction: column;

    .area {
      width: 100%;
      flex-grow: 1;
      height: 0;
      position: relative;
    }
  }

  .text {
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
    box-sizing: border-box;
    padding-left: 20px;
    padding-top: 14px;
    line-height: 32px;
    width: 100%;
    height: 46px;
    background: url(../../assets/boxTop.png) no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: row;
    align-items: end;
    position: relative;
    .barStyle {
      position: absolute;
      top: 18px;
      right: 20px;
      display: flex;
      flex-direction: row;
      p {
        width: 56px;
        height: 24px;
        line-height: 24px;
        font-size: 10px;
        text-align: center;
        background: url(../../assets/customize/bar_no.png) no-repeat;
        background-size: 100% 100%;
        margin-right: 6px;
      }
      .active {
        background: url(../../assets/customize/bar_s.png) no-repeat !important;
        background-size: 100% 100% !important;
      }
    }
  }

  .areaContent {
    width: 100%;
    height: calc(100% - 46px);
    background: url(../../assets/boxBottom.png) no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding-top: 10px;
    position: relative;
    .countStyle {
      top: 6px;
      right: 30px;
      font-size: 14px;
      position: absolute;
    }
    .countStyleOne {
      top: 10px;
      left: 30px;
      font-size: 14px;
      position: absolute;
      span {
        display: inline-block;
        &:nth-child(1) {
          margin-right: 40px;
        }
      }
    }
    .barCStyle {
      top: 10px;
      left: 30px;
      font-size: 14px;
      line-height: 20px;
      position: absolute;
      color: #fff;
      span {
        display: inline-block;
        &:nth-child(1) {
          margin-right: 20px;
        }
      }
      .active {
        color: #3d7bf7;
      }
    }
  }

  .middle {
    flex: 1;
    height: 100%;
    margin: 10px 10px 0;
    min-width: 600px;

    display: flex;
    flex-direction: column;

    .content {
      width: 100%;
      height: 600px;
      background: url(../../assets/indexconk.png) no-repeat center;
      background-size: 100% 100%;
      position: relative;
      flex-grow: 1;
    }

    .bottom {
      width: 100%;
      height: 201px;
      background: url(../../assets/bomk.png) no-repeat center;
      background-size: 100% 100%;
    }

    .btn-wrap {
      position: absolute;
      z-index: 10;
      left: 50%;
      transform: translate(-50%, 0);
    }

    .pro-info {
      height: 93%;
      width: 96%;
      // background: url(../../assets/pro-info.png);
      position: absolute;
      top: 3%;
      left: 2%;
      overflow: hidden;
      background-size: 100% 100%;
      aspect-ratio: 96/93;

      .right_show_div {
        width: 373px;
        height: 68%;
        position: absolute;
        right: -320px;
        top: 20%;
        font-size: 0;
        z-index: 10;
        transition: 0.5s;

        .left_img {
          width: 53px;
          height: 136px;
          display: inline-block;
          cursor: pointer;
          vertical-align: middle;
          img {
            width: 100%;
            height: 100%;
          }
        }

        .right_list_box {
          width: 320px;
          height: 100%;
          background: rgba(4, 18, 60, 0.8);
          display: inline-block;
          vertical-align: middle;
          position: relative;
          overflow: hidden;

          h5 {
            margin: 0;
            width: 100%;
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: rgba(0, 246, 255, 1);
            margin-top: 25px;
            margin-bottom: 25px;
            overflow: hidden;
            white-space: nowrap;
            text-align: center;
          }

          ul {
            width: 100%;
            margin: 0;
            padding: 0 15px;

            li {
              text-align: left;
              font-size: 16px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: white;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              line-height: 30px;
            }
          }
        }
      }

      .expand {
        right: 0;
      }
    }

    .video {
      height: 93%;
      width: 96%;
      position: absolute;
      top: 3%;
      left: 2%;
    }

    .echarts_map {
      height: 93%;
      width: 96%;
      position: absolute;
      top: 3%;
      left: 2%;
      z-index: 9;
      background: #0b3472;
    }
  }
}
</style>
<style lang="scss">
.multiple-player {
  .prism-big-play-btn {
    top: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    margin: auto;
  }
}

div[class^="video-container"] > div {
  position: absolute !important;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}
</style>
