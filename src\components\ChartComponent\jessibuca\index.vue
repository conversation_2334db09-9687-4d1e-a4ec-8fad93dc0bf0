<template>
  <div class="jessibucaWrap">
    <div
      ref="container"
      class="JvideoWrap"
      @dblclick="fullscreenSwich"
    >
      <div
        class="buttons-box"
        id="buttonsBox"
        v-if="radioValue === realTime"
      >
        <div class="buttons-box-left">
          <el-tooltip
            class="item"
            effect="dark"
            content="播放/暂停"
            placement="bottom"
          >
            <i
              v-if="!playing"
              class="iconfont icon-play jessibuca-btn"
              @click="playBtnClick"
            ></i>
            <i
              v-if="playing"
              class="iconfont icon-pause jessibuca-btn"
              @click="pause"
            ></i>
          </el-tooltip>
          <!-- 此功能暂时没用 -->
          <el-tooltip
            class="item"
            effect="dark"
            content="停止"
            placement="bottom"
            v-if="false"
          >
            <i
              class="iconfont icon-stop jessibuca-btn"
              @click="destroy"
            ></i>
          </el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            content="声音"
            placement="bottom"
          >
            <i
              v-if="isNotMute"
              class="iconfont icon-audio-high jessibuca-btn"
              @click="mute()"
            ></i>
            <i
              v-if="!isNotMute"
              class="iconfont icon-audio-mute jessibuca-btn"
              @click="cancelMute()"
            ></i>
          </el-tooltip>
          <!-- <el-tooltip
            class="item"
            effect="dark"
            content="云台控制"
            placement="bottom"
          >
            <img
              class="controlImg"
              @click="controlClick"
              src="../../../assets/control.png"
            />
          </el-tooltip> -->

          <el-tooltip
            class="item"
            effect="dark"
            content="录屏"
            placement="bottom"
          >
            <i
              class="iconfont el-icon-video-camera jessibuca-btn"
              @click="recordVideo()"
              style="font-size: 20px !important;"
            ></i>
          </el-tooltip>
        </div>
        <div class="buttons-box-right">
          <span class="jessibuca-btn">{{ kBps }} kb/s</span>
          <!--          <i class="iconfont icon-file-record1 jessibuca-btn"></i>-->
          <!--          <i class="iconfont icon-xiangqing2 jessibuca-btn" ></i>-->
          <el-tooltip
            class="item"
            effect="dark"
            content="截图"
            placement="bottom"
          >
            <i
              class="iconfont icon-camera1196054easyiconnet jessibuca-btn"
              style="font-size: 1rem !important"
              @click="screenshot"
            ></i>
          </el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            content="刷新"
            placement="bottom"
          >
            <i
              class="iconfont icon-shuaxin11 jessibuca-btn"
              @click="playBtnClick"
            ></i>
          </el-tooltip>

          <i
            v-if="!fullscreen"
            @click="fullscreenSwich"
          ></i>

          <el-tooltip
            class="item"
            effect="dark"
            content="全局全屏"
            placement="bottom"
          >
            <i
              class="iconfont el-icon-full-screen jessibuca-btn"
              style="font-size: 1rem !important;"
              @click="fullscreenSwich"
            ></i>
          </el-tooltip>
        </div>
      </div>

      <!-- v-if="videoType" -->
      <!-- 视频回放 -->
      <!-- <div
        class="tabSelect"
        v-if="showRecordBtn"
      >
        <el-radio-group
          v-model="radioValue"
          @input="setRadioValue"
        >
          <el-radio-button :label="realTime"></el-radio-button>
          <el-radio-button :label="playBack"></el-radio-button>
        </el-radio-group>
        <div v-if="radioValue === playBack">
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            :range-separator="$t(`To`)"
            :start-placeholder="$t(`StartDate`)"
            :end-placeholder="$t(`EndDate`)"
            @change="handlerChange"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </div>
      </div> -->

      <div
        class="recoderWrap"
        v-show="isRecording"
      >
        <record ref="record" />
      </div>

      <!-- 回放底部滑块 -->
      <div
        class="playbackWrap"
        v-if="radioValue === playBack"
      >
        <el-slider
          class="playtime-slider"
          v-model="playTime"
          id="playtimeSlider"
          :min="sliderMIn"
          :max="sliderMax"
          :range="true"
          :format-tooltip="playTimeFormat"
          @change="playTimeChange"
          :marks="playTimeSliderMarks"
        >
        </el-slider>
      </div>

      <ptzControl
        class="ptzControlWrap"
        v-if="isShowControl"
        :videoCfg="videoCfg"
      />

    </div>

    <!-- 预设点 -->
    <!-- <div
      class="presetWrap"
      v-if="radioValue == realTime && showPreset"
    >
      <Preset
        :videoId="videoId"
        :videoCfg="videoCfg"
        videoType="GB"
      />
    </div> -->
  </div>
</template>

<script>

import dayjs from "dayjs";
import moment from 'moment'
import { getVideoDetail } from "../ezuikit/api.js";

import ptzControl from './ptzControl.vue'
import record from './record.vue'
// import Preset from '../ezuikit/preset.vue'
import { playStart, playbackStart, playbackStop } from './api.js'

let jessibucaPlayer = {};
export default {
  components: {
    ptzControl,
    record,
    // Preset
  },
  data() {
    return {
      playing: false,
      isNotMute: false,
      quieting: false,
      fullscreen: false,
      loaded: false, // mute
      speed: 0,
      performance: "", // 工作情况
      kBps: 0,
      btnDom: null,
      videoInfo: null,
      volume: 1,
      rotate: 0,
      vod: true, // 点播
      forceNoOffscreen: false,
      radioValue: "实时",
      realTime: "实时",
      playBack: "回放",
      playTime: null,
      timeRange: null,
      startTime: null,
      endTime: null,
      streamId: '',
      streamInfo: null,
      app: null,
      mediaServerId: null,
      ssrc: null,
      currentVideoUrl: null,
      currentHasAudio: false,
      videoCfg: {
        channelId: "",
        deviceId: "",
        accessToken: "",
      },
      // 底部滑块
      playTime: null,
      sliderMIn: 0,
      sliderMax: 86400,
      playTimeSliderMarks: {
        0: "00:00",
        3600: "01:00",
        7200: "02:00",
        10800: "03:00",
        14400: "04:00",
        18000: "05:00",
        21600: "06:00",
        25200: "07:00",
        28800: "08:00",
        32400: "09:00",
        36000: "10:00",
        39600: "11:00",
        43200: "12:00",
        46800: "13:00",
        50400: "14:00",
        54000: "15:00",
        57600: "16:00",
        61200: "17:00",
        64800: "18:00",
        68400: "19:00",
        72000: "20:00",
        75600: "21:00",
        79200: "22:00",
        82800: "23:00",
        86400: "24:00",
      },
      isShowControl: false,

      jessibuca: null,

      isRecording: false,
    };
  },
  props: ['videoUrl', 'error', 'hasAudio', 'height', 'videoType', 'videoId', 'showRecordBtn', 'showPreset'],
  created() {
    let paramUrl = decodeURIComponent(this.$route.params.url)
    this.$nextTick(() => {
      console.log(2222)
      this.updatePlayerDomSize()
      window.onresize = this.updatePlayerDomSize
      if (typeof (this.videoUrl) == "undefined") {
        this.videoUrl = paramUrl;
      }
      this.btnDom = document.getElementById("buttonsBox");
    })
  },
  mounted() {
    this.initDatas(this.videoId);
  },
  // mounted() {
  //   const ro = new ResizeObserver(entries => {
  //     entries.forEach(entry => {
  //       this.updatePlayerDomSize()
  //     });
  //   });
  //   ro.observe(this.$refs.container);
  // },
  watch: {
    videoId: {
      handler(newVal) {
        this.isShowControl = false;
        this.isRecording = false
        this.radioValue = this.realTime
        this.playTime = null
        this.startTime = null
        this.endTime = null
        this.timeRange = null
        this.getVideoDetail(newVal);
      },
    },
    videoUrl: {
      handler(val, _) {
        this.$nextTick(() => {
          this.play(val);
        })
      },
      immediate: true
    }
  },
  methods: {
    recordVideo() {
      this.isRecording = !this.isRecording
      if (this.isRecording) {
        this.start()
        this.$refs.record.startTimer()
      } else {
        this.stop()
        this.$refs.record.stopTimer()
      }
    },
    start() {
      const time = new Date().getTime();
      this.jessibuca.startRecord(time, this.recordType)
    },
    stop() {
      this.jessibuca.stopRecordAndSave()
    },
    initDatas(videoId) {
      this.$nextTick(() => {
        this.getVideoDetail(videoId);
      });
    },
    // 初始化视频基本信息
    async getVideoDetail(videoId) {
      if (!videoId) return;

      const res = await getVideoDetail(videoId);
      let data = res.data || {};

      data = data.data || {};

      const { deviceSerial, deviceId, token, hdLiveEzOpenUrl } =
        data;
      this.videoCfg = {
        channelId: deviceSerial,
        deviceId,
        accessToken: token,
      };

      this.$emit('updateVideoUrl', hdLiveEzOpenUrl)
      this.playStart();
    },
    playTimeFormat(val) {
      let h = parseInt(val / 3600);
      let m = parseInt((val - h * 3600) / 60);
      let s = parseInt(val - h * 3600 - m * 60);

      let hStr = h;
      let mStr = m;
      let sStr = s;
      if (h < 10) {
        hStr = "0" + hStr;
      }
      if (m < 10) {
        mStr = "0" + mStr; s
      }
      if (s < 10) {
        sStr = "0" + sStr;
      }
      return hStr + ":" + mStr + ":" + sStr
    },
    playTimeChange(val) {
      const currentDate = this.startTime.split(' ')

      const start = currentDate[0] + " 00:00:00"
      const end = currentDate[0] + " 00:00:00"

      const startTimeStr = moment(new Date(start).getTime() + val[0] * 1000).format("YYYY-MM-DD HH:mm:ss");
      let endTimeStr = moment(new Date(end).getTime() + val[1] * 1000).format("YYYY-MM-DD HH:mm:ss");

      this.setTime(startTimeStr, endTimeStr)
      this.playRecord();
    },

    setTime: function (startTime, endTime) {

      this.startTime = startTime;
      this.endTime = endTime;

      const currentDate = this.startTime.split(' ')
      const current = currentDate[0] + " 00:00:00"

      let start = (new Date(this.startTime).getTime() - new Date(current).getTime()) / 1000;
      let end = (new Date(this.endTime).getTime() - new Date(current).getTime()) / 1000;

      this.playTime = [start, end];
      this.timeRange = [startTime, endTime];
    },
    moment: function (v) {
      return moment(v)
    },
    handlerChange(val) {
      this.startTime = val && val[0]
      this.endTime = val && val[1]
      const startDate = this.startTime.split(' ')
      let start = (new Date(this.startTime).getTime() - new Date(startDate[0] + " 00:00:00").getTime()) / 1000;
      let end = (new Date(this.endTime).getTime() - new Date(startDate[0] + " 00:00:00").getTime()) / 1000;
      this.playTime = [start, end];
      this.playRecord()
    },
    playStart() {
      const params = {
        ...this.videoCfg
      }
      const that = this
      playStart(params).then(function (res) {
        console.log(res)
        if (res.data.code === 0) {
          const videoUrl = res.data.data.ws_flv;
          that.$emit('updateVideoUrl', videoUrl)
        } else {
          this.$message.error(res.data.msg);
        }
      }).catch(function (e) {
        console.error(e)
      });
    },
    playRecord: function (start, end) {
      if (this.streamId !== "") {
        this.stopPlayRecord(() => {
          this.streamId = "";
          this.playRecord();
        })
      } else {
        const params = {
          ...this.videoCfg,
          startTime: this.startTime,
          endTime: this.endTime
        }
        playbackStart(params).then(res => {
          if (res.data.code === 0) {
            this.streamInfo = res.data.data;
            this.app = this.streamInfo.app;
            this.streamId = this.streamInfo.stream;
            this.mediaServerId = this.streamInfo.mediaServerId;
            this.ssrc = this.streamInfo.ssrc;
            this.currentVideoUrl = this.getUrlByStreamInfo();
            this.$emit('updateVideoUrl', this.currentVideoUrl)
            this.currentHasAudio = this.streamInfo.tracks && this.streamInfo.tracks.length > 1
          } else {
            this.$message({
              showClose: true,
              message: '回放记录不存在,请确保设备具有数据存储功能',
              type: "error",
            });
          }
        })
      }
    },

    getUrlByStreamInfo() {
      this.currentVideoUrl = this.streamInfo["ws_flv"]
      return this.currentVideoUrl;

    },
    // 停止录像回放
    stopPlayRecord: function (callback) {
      if (this.streamId !== "") {
        this.pause();
        this.currentVideoUrl = '';
        const params = {
          ...this.videoCfg,
          streamId: this.streamId
        }
        playbackStop(params).then(res => {
          if (callback) callback()
        })
      }
    },
    async setRadioValue(val) {
      this.radioValue = val;
      if (val == this.realTime) {
        // 调用停止，再调用播放
        await this.stopPlayRecord()
        this.playStart()
      } else {
        this.isShowControl = false;
        // 调用回放
        if (!this.startTime) {
          const currentDate = moment();
          this.startTime = currentDate.startOf('day').format('YYYY-MM-DD HH:mm:ss');
          this.endTime = currentDate.endOf('day').format('YYYY-MM-DD HH:mm:ss');
          // this.timeRange = [this.startTime, this.endTime]
        }
        this.playRecord()
      }
    },
    updatePlayerDomSize() {
      let dom = this.$refs.container;
      let width = dom.parentNode.clientWidth
      let height = (9 / 16) * width
      console.log(height)

      console.log(dom.clientHeight)
      if (height > dom.clientHeight) {
        height = dom.clientHeight
        width = (16 / 9) * height
      }
      if (width > 0 && height > 0) {
        dom.style.width = width + 'px';
        dom.style.height = height + "px";
        console.log(width)
        console.log(height)
      }
    },
    create() {
      let options = {
        container: this.$refs.container,
        autoWasm: true,
        background: "",
        controlAutoHide: false,
        debug: false,
        decoder: "static/js/jessibuca/decoder.js",
        forceNoOffscreen: false,
        hasAudio: typeof (this.hasAudio) == "undefined" ? true : this.hasAudio,
        heartTimeout: 5,
        heartTimeoutReplay: true,
        heartTimeoutReplayTimes: 3,
        hiddenAutoPause: false,
        hotKey: true,
        isFlv: false,
        isFullResize: false,
        isNotMute: this.isNotMute,
        isResize: false,
        keepScreenOn: true,
        loadingText: "请稍等, 视频加载中......",
        loadingTimeout: 10,
        loadingTimeoutReplay: true,
        loadingTimeoutReplayTimes: 3,
        openWebglAlignment: false,
        operateBtns: {
          fullscreen: false,
          screenshot: false,
          play: false,
          audio: false,
          record: false
        },
        recordType: "mp4",
        rotate: 0,
        showBandwidth: false,
        supportDblclickFullscreen: false,
        timeout: 10,
        useMSE: true,
        useWCS: location.hostname === "localhost" || location.protocol === "https:",
        useWebFullScreen: true,
        videoBuffer: 0.1,
        wasmDecodeErrorReplay: true,
        wcsUseVideoRender: true
      };
      console.log("Jessibuca -> options: ", options);
      jessibucaPlayer[this._uid] = new window.Jessibuca({ ...options });

      let jessibuca = jessibucaPlayer[this._uid];

      this.jessibuca = jessibuca
      let _this = this;
      jessibuca.on("pause", function () {
        _this.playing = false;
      });
      jessibuca.on("play", function () {
        _this.playing = true;
      });
      jessibuca.on("fullscreen", function (msg) {
        _this.fullscreen = msg
      });
      jessibuca.on("mute", function (msg) {
        _this.isNotMute = !msg;
      });
      jessibuca.on("performance", function (performance) {
        let show = "卡顿";
        if (performance === 2) {
          show = "非常流畅";
        } else if (performance === 1) {
          show = "流畅";
        }
        _this.performance = show;
      });
      jessibuca.on('kBps', function (kBps) {
        _this.kBps = Math.round(kBps);
      });
      jessibuca.on("videoInfo", function (msg) {
        console.log("Jessibuca -> videoInfo: ", msg);
      });
      jessibuca.on("audioInfo", function (msg) {
        console.log("Jessibuca -> audioInfo: ", msg);
      });
      jessibuca.on("error", function (msg) {
        console.log("Jessibuca -> error: ", msg);
      });
      jessibuca.on("timeout", function (msg) {
        console.log("Jessibuca -> timeout: ", msg);
      });
      jessibuca.on("loadingTimeout", function (msg) {
        console.log("Jessibuca -> timeout: ", msg);
      });
      jessibuca.on("delayTimeout", function (msg) {
        console.log("Jessibuca -> timeout: ", msg);
      });
      jessibuca.on("playToRenderTimes", function (msg) {
        console.log("Jessibuca -> playToRenderTimes: ", msg);
      });
    },
    playBtnClick: function (event) {
      this.play(this.videoUrl)
    },
    play: function (url) {
      console.log("Jessibuca -> url: ", url);
      if (jessibucaPlayer[this._uid]) {
        this.destroy();
      }
      this.create();
      jessibucaPlayer[this._uid].on("play", () => {
        this.playing = true;
        this.loaded = true;
        this.quieting = jessibuca.quieting;
      });
      if (jessibucaPlayer[this._uid].hasLoaded()) {
        jessibucaPlayer[this._uid].play(url);
      } else {
        jessibucaPlayer[this._uid].on("load", () => {
          jessibucaPlayer[this._uid].play(url);
        });
      }
    },
    pause: function () {
      // TODO
      console.log('调用pause', this._uid)
      if (jessibucaPlayer[this._uid]) {
        jessibucaPlayer[this._uid].pause();
      }
      this.playing = false;
      this.err = "";
      this.performance = "";
    },
    screenshot: function () {
      if (jessibucaPlayer[this._uid]) {
        jessibucaPlayer[this._uid].screenshot();
      }
    },
    mute: function () {
      if (jessibucaPlayer[this._uid]) {
        jessibucaPlayer[this._uid].mute();
      }
    },
    cancelMute: function () {
      if (jessibucaPlayer[this._uid]) {
        jessibucaPlayer[this._uid].cancelMute();
      }
    },
    destroy: function () {
      if (jessibucaPlayer[this._uid]) {
        jessibucaPlayer[this._uid].destroy();
      }
      if (document.getElementById("buttonsBox") == null) {
        this.$refs.container.appendChild(this.btnDom)
      }
      jessibucaPlayer[this._uid] = null;
      this.playing = false;
      this.err = "";
      this.performance = "";
    },
    fullscreenSwich: function () {
      let isFull = this.isFullscreen()
      jessibucaPlayer[this._uid].setFullscreen(!isFull)
      this.fullscreen = !isFull;
    },
    isFullscreen: function () {
      return document.fullscreenElement ||
        document.msFullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement || false;
    },
    controlClick() {
      this.isShowControl = !this.isShowControl
    },
  },
  destroyed() {
    if (jessibucaPlayer[this._uid]) {
      jessibucaPlayer[this._uid].destroy();
    }
    this.playing = false;
    this.loaded = false;
    this.performance = "";
    this.isShowControl = false;
  },
}
</script>

<style lang="scss" scoped>
.jessibucaWrap {
  width: 90% !important;
  height: 100%;
  // background-color: #000000;
  .JvideoWrap {
    height: 100% !important;
    width: 100% !important;
  }

  .recoderWrap {
    position: absolute;
    z-index: 999;
    left: calc(50% - 34px);
    bottom: 34px;
    color: #ffffff;
    padding: 0 4px;
    height: 24px;
    line-height: 24px;
    background: #00000050;
    border-radius: 12px;
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-around;
  }

  .tabSelect {
    position: absolute;
    top: 15px;
    right: 20px;
    z-index: 999;
  }

  .playbackWrap {
    position: absolute;
    bottom: 0;
    z-index: 999;
    width: 100%;
    background: rgba(0, 0, 0, 0.5);
    padding: 20px;
    box-sizing: border-box;
  }

  .ptzControlWrap {
    height: 170px;
    position: absolute;
    width: 160px;
    z-index: 999;
    // background: #fff;
    top: 50%;
    right: 20px;
    transform: translate(0, -50%);
  }

  .presetWrap {
  }
}

.buttons-box {
  width: 100%;
  height: 28px;
  background-color: rgba(29, 36, 44, 0.9);
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  bottom: 0;
  user-select: none;
  z-index: 10;

  .buttons-box-left {
    display: flex;
    align-items: center;

    .controlImg {
      height: 22px;
      width: 22px;
      margin: 0 10px;
      cursor: pointer;
    }
  }

  .jessibuca-btn {
    width: 20px;
    color: rgb(255, 255, 255);
    line-height: 27px;
    margin: 0px 10px;
    padding: 0px 2px;
    cursor: pointer;
    text-align: center;
    font-size: 0.9rem !important;
  }

  .buttons-box-right {
    position: absolute;
    right: 0;
  }
}
</style>
