<template>
  <div
    class="background-image-container"
    @click="hidePopup"
    ref="container"
  >
    <img
      class="background-image"
      :src="monitorInfo.filePath"
      alt="Project Background"
      ref="backgroundImage"
      @load="handleImageLoad"
    >

    <!-- 标记点循环 -->
    <div
      v-for="(result, index) in monitorResult"
      :key="index"
      class="marker-image-container"
      :class="{ highlighted: highlightedCategory === result.catgory }"
      :style="markerStyle(result)"
      @click.stop="handleMarkerClick(result, $event)"
    >
      <img
        class="marker-image"
        :src="getCategoryIcon(result.catgory)"
        :alt="result.catgory"
      >
    </div>

    <!-- 弹框 -->
    <div
      v-if="popup.visible"
      class="marker-popup"
      :style="popupStyle"
      @click.stop
    >
      <div
        class="mark-text"
        @click="goToModule"
      >
        <span class="mark-title">{{ getCategoryTitle(popup.data.catgory) }}</span>
        <span class="mark-arrow"> > </span>
      </div>
      <div class="mark-underline"></div>
      <component
        class="monitor-chart-wrap"
        :is="getComponentByCategory(popup.data.catgory)"
        :isShowTitle="false"
        :moduleName="getCategoryTitle(popup.data.catgory)"
      />
    </div>
  </div>
</template>

<script>
import { getMonitorImg, getMonitorResult, getAllModule } from '@/api/common'
import { BASE_URL } from '@/util/constant'
import { getStore } from '@/util/store'

// 导入组件
import MaterialManage from '@/components/ChartComponent/MaterialManage.vue'
import TowerCrane from '@/components/ChartComponent/TowerCrane.vue'

// 分类配置映射
const CATEGORY_CONFIG = {
  '塔式起重机': {
    title: '塔吊监测',
    icon: require('@/assets/tab/塔式起重机.png'),
    component: 'TowerCrane',
    directJump: false,
    level: 1,
  },
  '轮胎式起重机': {
    title: '可移动机械设备',
    icon: require('@/assets/tab/轮胎式起重机.png'),
    component: 'TowerCrane',
    directJump: true,
    jumpType: 'mobile',
    level: 1,
  },
  '全地面起重机': {
    title: '可移动机械设备',
    icon: require('@/assets/tab/全地面起重机.png'),
    component: 'TowerCrane',
    directJump: true,
    jumpType: 'mobile',
    level: 1,
  },
  '防护栏杆': {
    title: '工具式防护栏杆',
    icon: require('@/assets/tab/防护栏杆.png'),
    component: 'TowerCrane',
    directJump: true,
    jumpType: 'railings',
    level: 1,
  },
  'H型钢': {
    title: '材料管理',
    icon: require('@/assets/tab/H型钢.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 1,
  },
  '工字钢': {
    title: '材料管理',
    icon: require('@/assets/tab/工字钢.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 1,
  },
  '槽钢': {
    title: '材料管理',
    icon: require('@/assets/tab/槽钢.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 1,
  },
  '脚手架': {
    title: '材料管理',
    icon: require('@/assets/tab/脚手架.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 1,
  },
  '混凝土实心砖': {
    title: '材料管理',
    icon: require('@/assets/tab/混凝土实心砖.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 1,
  },
  '砌体填充墙': {
    title: '材料管理',
    icon: require('@/assets/tab/砌体填充墙.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 1,
  },
  '混凝土罐车': {
    title: '可移动机械设备',
    icon: require('@/assets/tab/混凝土罐车.png'),
    component: 'MaterialManage',
    directJump: true,
    jumpType: 'mobile',
    level: 2,
  },
  '混凝土搅拌机': {
    title: '可移动机械设备',
    icon: require('@/assets/tab/混凝土搅拌机.png'),
    component: 'MaterialManage',
    directJump: true,
    jumpType: 'mobile',
    level: 2,
  },
  '推土机': {
    title: '可移动机械设备',
    icon: require('@/assets/tab/推土机.png'),
    component: 'MaterialManage',
    directJump: true,
    jumpType: 'mobile',
    level: 2,
  },
  '挖掘机': {
    title: '可移动机械设备',
    icon: require('@/assets/tab/挖掘机.png'),
    component: 'MaterialManage',
    directJump: true,
    jumpType: 'mobile',
    level: 2,
  },
  '装载机': {
    title: '可移动机械设备',
    icon: require('@/assets/tab/装载机.png'),
    component: 'MaterialManage',
    directJump: true,
    jumpType: 'mobile',
    level: 2,
  },
  '采光板': {
    title: '材料管理',
    icon: require('@/assets/tab/采光板.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 2,
  },
  '防水卷材': {
    title: '材料管理',
    icon: require('@/assets/tab/防水卷材.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 2,
  },
  '钢筋': {
    title: '材料管理',
    icon: require('@/assets/tab/钢筋.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 2,
  },
  '单元式幕墙': {
    title: '材料管理',
    icon: require('@/assets/tab/单元式幕墙.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 2,
  },
  '金属板幕墙': {
    title: '材料管理',
    icon: require('@/assets/tab/金属板幕墙.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 2,
  },
  '石材幕墙': {
    title: '材料管理',
    icon: require('@/assets/tab/石材幕墙.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 2,
  },
  '铝塑共挤节能门窗': {
    title: '材料管理',
    icon: require('@/assets/tab/铝塑共挤节能门窗.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 2,
  },
  '木门窗': {
    title: '材料管理',
    icon: require('@/assets/tab/木门窗.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 2,
  },
  '塑料门窗': {
    title: '材料管理',
    icon: require('@/assets/tab/塑料门窗.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 2,
  },
  '预应力钢绞线': {
    title: '材料管理',
    icon: require('@/assets/tab/塑料门窗.png'),
    component: 'MaterialManage',
    directJump: false,
    level: 2,
  },
}

export default {
  name: 'ProjectBackground',
  components: {
    MaterialManage,
    TowerCrane
  },
  props: {
    projectId: { type: Number, required: true },
  },
  data() {
    return {
      monitorInfo: {
        projectId: 0,
        filePath: '',
        id: 0,
      },
      monitorResult: [],
      popup: {
        visible: false,
        data: {},
        x: 0,
        y: 0,
      },
      moduleList: [],
      imageScale: {
        widthRatio: 1,
        heightRatio: 1,
        offsetX: 0,
        offsetY: 0
      },
      originalImageSize: {
        width: 0,
        height: 0
      },
      loading: false,
      imageLoaded: false,
      highlightedCategory: null
    }
  },
  computed: {
    popupStyle() {
      return {
        position: 'absolute',
        left: this.popup.x + 'px',
        top: this.popup.y + 'px',
        zIndex: 10,
        transformOrigin: 'left top'
      }
    }
  },
  mounted() {
    this.initData()
    document.addEventListener('click', this.hidePopup)
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.hidePopup)
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initData() {
      this.loading = true
      const promises = [
        this.getMonitorImg(),
        this.getAllModule()
      ]

      Promise.all(promises)
        .catch(error => {
          console.error('初始化数据失败:', error)
        })
        .finally(() => {
          this.loading = false
        })
    },

    handleImageLoad() {
      const img = this.$refs.backgroundImage
      if (img && img.naturalWidth && img.naturalHeight) {
        this.originalImageSize = {
          width: img.naturalWidth,
          height: img.naturalHeight
        }
        this.calculateImageScale()
      }
    },

    handleResize() {
      this.calculateImageScale()
    },

    calculateImageScale() {
      const container = this.$refs.container
      const img = this.$refs.backgroundImage

      if (!container || !img || !this.originalImageSize.width || !this.originalImageSize.height) return

      const containerWidth = container.clientWidth
      const containerHeight = container.clientHeight
      const imgRatio = this.originalImageSize.width / this.originalImageSize.height
      const containerRatio = containerWidth / containerHeight

      let width, height, offsetX = 0, offsetY = 0

      if (imgRatio > containerRatio) {
        width = containerWidth
        height = containerWidth / imgRatio
        offsetY = (containerHeight - height) / 2
      } else {
        height = containerHeight
        width = containerHeight * imgRatio
        offsetX = (containerWidth - width) / 2
      }

      this.imageScale = {
        widthRatio: width / this.originalImageSize.width,
        heightRatio: height / this.originalImageSize.height,
        offsetX: offsetX,
        offsetY: offsetY
      }
    },

    buildJumpUrl(type) {
      const userId = getStore({ name: 'userId' })
      const projectId = getStore({ name: 'projectId' })
      const companyId = getStore({ name: 'companyId' })
      const language = getStore({ name: "language" })
      const firstName = '智慧管理'

      const baseUrl = window.location.protocol + '//' + window.location.hostname + ':20000/'
      const params = '?language=' + language + '&userId=' + userId + '&companyId=' + companyId + '&projectId=' + projectId + '&firstName=' + firstName

      if (type === 'mobile') {
        return baseUrl + 'MobileMachinery/Index' + params + '&secondName=可移动机械设备管理'
      } else if (type === 'railings') {
        return baseUrl + 'ProtectiveRailings/Index' + params + '&secondName=工具式防护栏杆'
      }
      return ''
    },

    goToModule() {
      const categoryTitle = this.getCategoryTitle(this.popup.data.catgory)
      const module = this.moduleList.find(function (m) { return m.moduleName === categoryTitle })

      if (module && module.jumpLinkUrl) {
        const userId = getStore({ name: 'userId' })
        const projectId = getStore({ name: 'projectId' })
        const companyId = getStore({ name: 'companyId' })

        let url = module.jumpLinkUrl
        const separator = url.includes('?') ? '&' : '?'
        url += separator + 'userId=' + userId + '&projectId=' + projectId + '&companyId=' + companyId

        window.open(url)
      }
    },

    async getAllModule() {
      const isAll = true
      try {
        const res = await getAllModule(this.projectId, isAll)
        if (res.data && res.data.statusCode === 200) {
          this.moduleList = res.data.data
        }
      } catch (error) {
        console.error('获取所有模块失败:', error)
      }
    },

    async getMonitorImg() {
      try {
        const res = await getMonitorImg(this.projectId)
        if (res.data && res.data.statusCode === 200) {
          this.monitorInfo = {
            ...res.data.data,
            filePath: BASE_URL + res.data.data.filePath
          }

          await this.preloadImage(this.monitorInfo.filePath)
          this.getMonitorResult(this.monitorInfo.id)
        }
      } catch (err) {
        console.error('getMonitorImg接口异常:', err)
      }
    },

    preloadImage(src) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.src = src
        img.onload = () => {
          this.originalImageSize = {
            width: img.naturalWidth,
            height: img.naturalHeight
          }

          this.$nextTick(() => {
            this.calculateImageScale()
            this.imageLoaded = true
            resolve()
          })
        }
        img.onerror = reject
      })
    },

    async getMonitorResult(imageId) {
      if (!imageId) {
        this.$message && this.$message.error('图片不存在，获取识别结果失败')
        return
      }
      try {
        const res = await getMonitorResult(imageId)
        if (res.data && res.data.statusCode === 200) {

          const processedData = this.processOverlappingMarkers(res.data.data)
          this.$set(this, 'monitorResult', processedData)
        }
      } catch (error) {
        console.error('获取监控结果失败:', error)
      }
    },

    processOverlappingMarkers(data) {
      const positionMap = new Map()

      data.forEach(item => {
        const positionKey = item.boxPosition
        const existingItem = positionMap.get(positionKey)
        const currentConfig = CATEGORY_CONFIG[item.catgory]
        const currentLevel = currentConfig ? currentConfig.level : 2

        if (!existingItem) {
          positionMap.set(positionKey, item)
        } else {
          const existingConfig = CATEGORY_CONFIG[existingItem.catgory]
          const existingLevel = existingConfig ? existingConfig.level : 2

          if (currentLevel < existingLevel) {
            positionMap.set(positionKey, item)
          }
        }
      })

      return Array.from(positionMap.values())
    },

    markerStyle(result) {
      const [x, y] = result.boxPosition.split(',').map(Number)
      const scaledX = x * this.imageScale.widthRatio + this.imageScale.offsetX
      const scaledY = y * this.imageScale.heightRatio + this.imageScale.offsetY

      const isHighlighted = this.highlightedCategory === result.catgory

      return {
        left: scaledX + 'px',
        top: scaledY + 'px',
        position: 'absolute',
        transform: 'translate(-50%, -50%)',
        filter: isHighlighted ? 'drop-shadow(0 0 10px rgba(255, 255, 255, 1))' : 'none',
        zIndex: isHighlighted ? 5 : 3
      }
    },

    handleMarkerClick(result, event) {
      event.stopPropagation()

      this.highlightedCategory = result.catgory

      const categoryConfig = CATEGORY_CONFIG[result.catgory]

      if (!categoryConfig) {
        console.error('未找到分类配置:', result.catgory)
        return
      }

      if (categoryConfig.directJump && categoryConfig.jumpType) {
        const url = this.buildJumpUrl(categoryConfig.jumpType)
        if (url) {
          window.open(url)
        }
        return
      }

      this.popup = {
        visible: true,
        data: {
          ...result,
          id: result.id
        },
        x: 0,
        y: 0
      }

      this.$nextTick(() => {
        const container = this.$el.getBoundingClientRect()
        const [x, y] = result.boxPosition.split(',').map(Number)

        const scaledX = x * this.imageScale.widthRatio + this.imageScale.offsetX
        const scaledY = y * this.imageScale.heightRatio + this.imageScale.offsetY

        const popupWidth = 343
        const popupHeight = 280

        let left = scaledX + 30
        let top = scaledY - popupHeight / 2

        if (left + popupWidth > container.width) {
          left = scaledX - popupWidth - 30
        }

        this.popup.x = Math.max(0, left)
        this.popup.y = Math.max(0, top)
      })
    },

    hidePopup() {
      this.popup.visible = false
      this.highlightedCategory = null
    },

    getCategoryIcon(category) {
      const config = CATEGORY_CONFIG[category]
      return config ? config.icon : ''
    },

    getCategoryTitle(category) {
      const config = CATEGORY_CONFIG[category]
      return config ? config.title : category
    },

    getComponentByCategory(category) {
      const config = CATEGORY_CONFIG[category]
      return config ? config.component : 'div'
    }
  }
}
</script>

<style lang="scss" scoped>
.background-image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;

  .monitor-chart-wrap {
    height: calc(100% - 40px) !important;
    >>> .areaContent {
      width: 100% !important;
      height: 100% !important;
    }
    >>> .text {
      display: none !important;
    }
  }

  .background-image {
    min-width: 100%;
    min-height: 100%;
    object-fit: cover;
    pointer-events: none;
  }

  .marker-image-container {
    position: absolute;
    cursor: pointer;
    z-index: 3;
    pointer-events: auto;
    user-select: none;
    transition: filter 0.3s ease, transform 0.3s ease;

    &:hover {
      transform: translate(-50%, -50%) scale(1.2);
    }

    &.highlighted {
      filter: drop-shadow(0 0 10px rgba(255, 255, 255, 1));
      z-index: 5;
    }

    .marker-image {
      width: 63px;
      height: 63px;
      object-fit: contain;
      transition: transform 0.2s;

      &:hover {
        transform: scale(1.2);
      }
    }
  }

  .marker-popup {
    width: 380px;
    height: 260px;
    border-radius: 8px;
    color: #fff;
    font-size: 14px;
    padding-bottom: 20px;
    animation: fadeIn 0.3s ease-out;

    background: linear-gradient(
      1deg,
      rgba(32, 93, 149, 0.9) 27%,
      rgba(23, 70, 141, 0.9) 100%
    );
    border-radius: 8px 8px 8px 8px;
    border: 1px solid;
    border-image: radial-gradient(
        circle,
        rgba(255, 255, 255, 1),
        rgba(92, 177, 255, 1),
        rgba(74, 174, 255, 1),
        rgba(59, 118, 166, 1),
        rgba(59, 118, 166, 0)
      )
      1 1;

    .mark-text {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 16px;
      font-weight: 500;
      color: #fff;
      position: relative;
      height: 40px;
      padding: 0 15px;
      cursor: pointer;
    }

    .mark-title {
      flex: 1;
      text-align: left;
    }

    .mark-arrow {
      font-size: 18px;
      margin-left: 8px;
      color: #6fd0ff;
    }

    .mark-underline {
      width: 100%;
      height: 1px;
      background: #418ecd;
      margin-bottom: 12px;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
</style>