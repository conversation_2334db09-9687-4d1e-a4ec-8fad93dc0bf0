import Axios from "@/router/axios";

// 国标设备点播-开始点播
export function playStart(data) {
  return Axios({
    url: `/GB/api/play/start/${data.deviceId}/${data.channelId}`,
    method: 'get',
    accessToken: data.accessToken
  });
}

// 国标设备点播-停止点播
export function playStop(data) {
  return Axios({
    url: `/GB/api/play/stop/${data.deviceId}/${data.channelId}`,
    method: 'get',
    accessToken: data.accessToken
  });
}

// 视频回放-开始视频回放
export function playbackStart(data) {
  return Axios({
    url: `/GB/api/playback/start/${data.deviceId}/${data.channelId}`,
    method: 'get',
    params: {
      startTime: data.startTime,
      endTime: data.endTime
    },
    accessToken: data.accessToken
  });
}

// 视频回放-停止视频回放
export function playbackStop(data) {
  return Axios({
    url: `/GB/api/playback/stop/${data.deviceId}/${data.channelId}/${data.streamId}`,
    method: 'get',
    accessToken: data.accessToken
  });
}

// 云台控制
export function ptzControl(data) {
  return Axios({
    url: `/GB/api/ptz/control/${data.deviceId}/${data.channelId}`,
    method: 'post',
    params: {
      command: data.command,
      horizonSpeed: data.horizonSpeed,
      verticalSpeed: data.verticalSpeed,
      zoomSpeed: data.zoomSpeed,
    },
    accessToken: data.accessToken
  });
}

// 云台控制-通用前端控制命令
export function ptzFrondEnd(data) {
  return Axios({
    url: `/GB/api/ptz/front_end_command/${data.deviceId}/${data.channelId}`,
    method: 'post',
    params: {
      // ...data,
      cmdCode: data.cmdCode,
      parameter1: data.parameter1,
      parameter2: data.parameter2,
      combindCode2: data.combindCode2,
    },
    accessToken: data.accessToken
  });
}