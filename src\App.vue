<template>
  <el-row id="app">
    <!-- 首页头部和其他页面头部独立控制，首页头部包含页面功能 -->
    <!-- isShowPageBtn为false代表正常页面不显示首页特有的按钮 -->
    <HeadTop
      ref="header"
      :isShowPageBtn="false"
      v-if="$route.meta.showHeader"
    ></HeadTop>

    <div
      class="btn-box"
      v-if="$route.name == 'todo' || $route.name == 'warning'"
    >
      <div
        class="goback"
        v-if="isShow"
        @click="goBack"
      >
        <img
          class="todo"
          src="./assets/goback.png"
        />
        <span>返回驾驶舱</span>
      </div>
      <div
        class="goback"
        v-if="isCompanyShow"
        @click="goCompany"
      >
        <img
          class="todo"
          src="./assets/goback.png"
        />
        <span>返回企业级</span>
      </div>
    </div>
    <!-- <HeadTop
      ref="header"
      v-if="$isShowHeader"
    ></HeadTop>
    <el-row
      :class="['route-content route-contentOne', {'route-content-iframe': !$isShowHeader}]"
      id="route-content"
    >
      <AsideMenu style="margin-top:20px;"></AsideMenu>
      </el-row>-->
    <router-view />
  </el-row>
</template>
<script>
// import NavButton from '@/pub/components/navButton';
import { COMPANY_URL, PROJECT_URL } from "@/util/constant";
import { getToken } from "@/api/echrtsApi";
import HeadTop from "@/pub/components/header";

export default {
  name: "App",
  components: {
    // NavButton,
    HeadTop
  },
  data() {
    return {
      isShow: true,
      tempShow: false,
      isCompanyShow: false,
      // screenWidth: document.body.clientWidth,
      // screenHeight: document.body.screenHeight,
      userId: ""
    };
  },
  watch: {
    userId: {
      handler(newVval) {
        if (newVval) {
          if (getStore({ name: "tempUserId" }) != newVval) {
            setStore({ name: "currentMenuTab", content: null });
          }
        }
      },
      immediate: true
    },
    $route(to) {
      // this.tempShow = to.meta.isTemp;
    }
    // screenWidth(val) {
    //   // 为了避免频繁触发resize函数导致页面卡顿，使用定时器
    //   if (!this.timer) {
    //     // 一旦监听到的screenWidth值改变，就将其重新赋给data里的screenWidth
    //     this.screenWidth = val;
    //     this.timer = true;
    //     let that = this;
    //     setTimeout(function () {
    //       // 打印screenWidth变化的值
    //       that.timer = false;
    //     }, 400);
    //   }
    // },
    // screenHeight(val) {
    //   // 为了避免频繁触发resize函数导致页面卡顿，使用定时器
    //   if (!this.timer) {
    //     // 一旦监听到的screenWidth值改变，就将其重新赋给data里的screenWidth
    //     this.screenHeight = val;
    //     this.timer = true;
    //     let that = this;
    //     setTimeout(function () {
    //       // 打印screenWidth变化的值
    //       that.timer = false;
    //     }, 400);
    //   }
    // },
  },
  created() {
    this.$bus.$on('getUserInfoSuccess', () => {
      if (getStore({ name: "userType" }) == 7) {
        this.isCompanyShow = true;
      }
    })
  },
  mounted() {
    this.userId = getStore({ name: "userId" });
    this.getTokenData();
    // const that = this;
    // window.onresize = () => {
    //   return (() => {
    //     window.screenWidth = document.body.clientWidth;
    //     window.screenHeight = document.body.clientHeight;
    //     that.screenWidth = window.screenWidth;
    //     that.screenHeight = window.screenHeight;
    //   })();
    // };
    // window.onload = () => {
    //   document.getElementById("app").style.height = window.innerHeight + "px";
    //   document.getElementById("route-content").style.height = (window.innerHeight - 70) + "px";
    //   document.getElementById("app").style.width = window.innerWidth + "px";
    //   setStore({ name: 'routeContent', content: document.getElementById("route-content").style.height })
    // };
    // window.addEventListener("resize", function () {
    //   document.getElementById("app").style.height = window.innerHeight + "px";
    //   document.getElementById("app").style.width = window.innerWidth + "px";
    //   document.getElementById("route-content").style.height = (window.innerHeight - 70) + "px";
    //   setStore({ name: 'routeContent', content: document.getElementById("route-content").style.height })
    // });
  },
  methods: {
    goBack() {
      const userId = getStore({ name: "userId" });
      const projectId = getStore({ name: "projectId" });
      const companyId = getStore({ name: "companyId" });
      const projectName = getStore({ name: "projectName" });
      const userType = getStore({ name: "userType" });
      location.href = `${PROJECT_URL}/?userId=${userId}&companyId=${companyId}&projectId=${projectId}&projectName=&userType=${userType}#/constructionRecord`;
      // if (projectId) {
      //   location.href =
      //     `${COMPANY_URL}/?userId=${userId}&companyId=${companyId}&projectId=${projectId}&projectName=${projectName}&userType${userType}#/constructionRecord`
      // } else {
      //   location.href =
      //     `${PROJECT_URL}/?userId=${userId}&companyId=${companyId}&projectId=${projectId}&projectName=${projectName}&userType${userType}#/constructionRecord`
      // }
    },
    goCompany() {
      const userId = getStore({ name: "userId" });
      const companyId = getStore({ name: "companyId" });
      const projectId = 0
      const language = getStore({ name: "language" });
      location.href = `${projectId ? PROJECT_URL : COMPANY_URL}/?userId=${userId}&companyId=${companyId}&projectId=${projectId}&language=${language}`;
    },
    goBackLast() {
      this.$router.go(-1);
    },
    getTokenData() {
      getToken(this.userId).then(res => {
        setStore({ name: "token", content: res.data.data });
      });
    },
    getList() {
      //获取到 header里 调用 头部的 getlist方法
      this.$refs.header.$children[0].getList(true)
    },
  }
};
</script>

<style lang="scss">
@media screen and (max-width: 1200px) {
  #app {
    // width:1100PX!important;
    overflow: auto !important;
  }
}
#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: url(./assets/BIMBg.png) no-repeat center;
  background-size: 100% 100%;
  // overflow-y: hidden;
  // overflow: auto;
  width: 100%;
  height: 100%;

  .route-content {
    width: 100%;
    height: 100%;
    // overflow-y: auto;
    padding: 70px 50px 20px;
    box-sizing: border-box;
    display: flex;
  }
  .route-contentOne {
    padding: 70px 10px 20px !important;
    overflow: auto;
  }
  .route-content-iframe {
    padding: 0px 10px 20px !important;
  }
  .btn-box {
    position: fixed;
    top: 80px;
    transition: all 0.4s ease 0s;
    right: -110px;
    z-index: 99999;
    .goback {
      position: relative;
      color: #fff !important;
      text-indent: 0.2rem;
      cursor: pointer;
      width: 130px;
      height: 40px;
      line-height: 40px;
      img {
        // width: 100%;
        // height: 100%;
      }
      .todo {
        height: 100%;
      }
      span {
        position: absolute;
        top: 0px;
        right: 4px;
        font-size: 16px;
      }
      .word {
        position: absolute;
        left: 30px;
        color: #fff;
        font-size: 16px;
        text-align: center;
      }
    }
  }
  .btn-box:hover {
    right: 0;
    transition: all 0.4s ease 0s;
  }
  .node_div {
    display: flex;
    span {
      width: 200px;
    }
  }
  .active {
    color: #66a3ff;
  }
}
</style>
