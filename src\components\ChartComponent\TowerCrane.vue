<!--
 * @Description: 塔吊监测
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-08-01 15:55:36
 * @LastEditors: dongqi<PERSON>qian
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="TowerCraneRef"
      >
        <div
          id="TowerCraneChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
      <p class="barCStyle">
        <span
          :class="barActive == items ? 'active' : null"
          @click="setActive(items)"
          v-for="(items, key) in barList"
          :key="key"
        >{{ items }}</span>
      </p>
      <!-- <p class="countStyle"> {{
            $t(`total`)
          }}{{ barActive }} {{
            $t(`Accumulated`)
          }}：{{ totalCount }}</p> -->
      <!-- <p class="countStyle">总{{ barActive }}累计：{{ totalCount }}</p> -->
    </div>
  </div>
</template>
<script>
import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getTowerCrane } from "@/api/echrtsApi";
export default {
  components: {},
  name: "TowerCrane",
  props: {
    moduleName: String
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "TowerCraneChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.towerCraneTotal"),
        // seriesRadius: ["50%", "80%"],
        seriesCenter: ["25%", "58%"],
        richNameWidth: 70,
      },
      early: [], // 预警数据
      warning: [], // 报警数据
      barList: [this.$t("customization.towerCraneEarly"), this.$t("customization.towerCraneWarning")],
      barActive: this.$t("customization.towerCraneEarly"),
      totalCount: 0,
      language: 'zh'
    };
  },
  watch: {
    "$i18n.locale"(val) {
      if (val && this.$IsProjectShow) {
        this.language = val
        this.languageChange();
      }
    },
  },
  created() {
    this.projectId = getStore({
      name: "projectId"
    });
    this.companyId = getStore({
      name: "companyId"
    });
    this.language = getStore({
      name: "language"
    });
    if (this.$IsProjectShow) {
      this.languageChange()
    } else {
      this.getBarData();
    }
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    languageChange() {
      if (this.language == 'en') {
        this.pieParams = {
          dom: "TowerCraneChart",
          data: [],
          nameTitle: null,
          seriesLabel: false,
          // seriesRadius: ["54%", "84%"],
          seriesCenter: ["25%", "50%"],
          richNameWidth: 70,
          itemStyleEmphasis: {
            label: {
              show: true,
              // position: 'center',
              x: "20%",
              y: "10%",
              textStyle: {
                rich: {
                  numText: {
                    color: "#fff",
                    fontSize: 13,
                    width: 30,
                    textAlign: "center"
                  },
                  text: {
                    color: "#fff",
                    fontSize: 13,
                    padding: [0, 0, 10, 0],
                    width: 30,
                    textAlign: "center"
                  }
                }
              },
              formatter: function (params) {
                // console.log(params);
                return `{text| ${params.name} Accumulated：${params.value
                  }}\n{numText|Proportion： ${params.percent || 0}%}`;
              }
            }
          }
        }
      } else {
        this.pieParams = {
          dom: "TowerCraneChart",
          data: [],
          nameTitle: null,
          seriesLabel: false,
          // seriesRadius: ["54%", "84%"],
          seriesCenter: ["25%", "50%"],
          richNameWidth: 70,
          noTooltipShow: true, //不显示
          itemStyleEmphasis: {
            label: {
              show: true,
              // position: 'center',
              x: "20%",
              y: "10%",
              textStyle: {
                rich: {
                  numText: {
                    color: "#fff",
                    fontSize: 13,
                    width: 30,
                    textAlign: "center"
                  },
                  text: {
                    color: "#fff",
                    fontSize: 13,
                    padding: [0, 0, 10, 0],
                    width: 30,
                    textAlign: "center"
                  }
                }
              },
              formatter: function (params) {
                // console.log(params);
                return `{text| ${params.name} 累计：${params.value
                  }}\n{numText|占比： ${params.percent || 0}%}`;
              }
            }
          }
        }
      }
      this.barList[0] = this.$t("customization.towerCraneEarly");
      this.barList[1] = this.$t("customization.towerCraneWarning");
      this.barActive = this.$t("customization.towerCraneEarly");
      this.getBarData();
    },
    setEchartsWidth() {
      this.barWidth = this.$refs.TowerCraneRef.offsetWidth;
      this.barHeight = this.$refs.TowerCraneRef.offsetHeight;
    },
    getBarData() {
      getTowerCrane()
        .then(res => {
          const {
            statusCode,
            data: { early, warning }
          } = res.data;
          if (statusCode == 200) {
            this.early = early;
            this.warning = warning;
            this.setEcharts(early);
          }
        })
        .catch(() => { });
    },
    setActive(val) {
      const { early, warning } = this;
      this.barActive = val;
      if (val == this.$t("customization.towerCraneEarly")) {
        this.setEcharts(early);
      } else {
        this.setEcharts(warning);
      }
    },
    setEcharts(val) {
      let dataList = val;
      let sum = 0;
      dataList.forEach(ele => {
        switch (ele.name) {
          case "载重":
            ele.name = this.$t("customization.towerCraneLoad");
            break;
          case "风速":
            ele.name = this.$t("customization.towerCraneWindSpeed");
            break;
          case "力矩":
            ele.name = this.$t("customization.towerCraneMoment");
            break;
          case "倾角":
            ele.name = this.$t("customization.towerCraneIncline");
            break;
          case "防碰撞":
            ele.name = this.$t("customization.towerCraneAntiCollision");
            break;
        }
        sum = sum + Number(ele.value);
      });

      this.totalCount = sum;

      // this.pieParams.titleInfor.text = totalCount;
      this.pieParams.data = dataList;
      // console.log(dataList,"dataList")
      drawAnnularChart(this.pieParams);
    }
  }
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
