<!--
 * @Description: 车辆冲洗
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2024-02-26 10:55:59
 * @LastEditors: Jky200guo <EMAIL>
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div class="box" ref="CarWashRef">
        <div
          id="CarWashChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawPie } from "@/components/constructionRecord/Echarts/echartsOne.js";
import { getCarWash } from "@/api/echrtsApi";
export default {
  components: {},
  name: "CarWash",
  props: {
    moduleName: String,
  },
  watch: {
    "$i18n.locale"(val) {
      if (val && this.$IsProjectShow) {
        this.languageChange();
      }
    },
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "CarWashChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        // seriesRadius: '70%',
        seriesCenter: ["30%", "45%"],
        // tooltipFormatter: '{b}<br /> 回收质量：{c}kg<br />回收占比：{d}%',
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    if (this.$IsProjectShow) {
      this.languageChange();
    } else {
      this.getBarData();
    }
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    languageChange() {
      this.getBarData();
    },
    setEchartsWidth() {
      this.barWidth = this.$refs.CarWashRef.offsetWidth - 40;
      this.barHeight = this.$refs.CarWashRef.offsetHeight;
    },
    getBarData() {
      getCarWash(this.projectId)
        .then((res) => {
          const { statusCode, data } = res.data;
          if (statusCode == 200) {
            if (this.$IsProjectShow) {
              data.forEach((ele) => {
                if (ele.name == "已冲洗") {
                  ele.name = this.$t(`Rinsed`);
                }
                if (ele.name == "冲洗时间不足") {
                  ele.name = this.$t("Insufficientflushingtime");
                }
                if (ele.name == "直接驶出冲洗区") {
                  ele.name = this.$t("Driveoutarea");
                }
                if (ele.name == "绕道未冲洗") {
                  ele.name = this.$t("Detourwithoutrinsing");
                }
                if (ele.name == "其它") {
                  ele.name = this.$t("other");
                }
              });
            }
            this.pieParams.data = data;
            drawPie(this.pieParams);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
}
</style>
