/**
 * Created by wanglei
 * 公共提示方法
 * showConfirm：确认／取消提示框
 * showMessage：消息提示框
 */
// import Vue from 'vue';
import {Notification,MessageBox} from 'element-ui'

/**
 *
 * 提示框公共方法
 *title:标题
 * message:消息内容
 * confirmFunc：确认回调函数
 * type:消息类型：success, warning, info, error
 * cancelFunc：取消回调函数
 * confirmButtonText：	确定按钮的文本内容
 * cancelButtonText：取消按钮的文本内容
 */
export function showConfirm(message,title,confirmFunc,cancelFunc,confirmButtonText,cancelButtonText,type,showCancelButton,closeOnClickModal){
    MessageBox.confirm(message || '确定要执行此操作吗？', title || '提示', {
        showCancelButton: showCancelButton || true,
        confirmButtonText: confirmButtonText || '确定',
        cancelButtonText: cancelButtonText || '取消',
        closeOnClickModal:closeOnClickModal || false,
        type: type || 'warning'
    }).then(function(){
        confirmFunc();
    }).catch(function(){
        if (typeof(eval(cancelFunc)) == "function") {
            cancelFunc();
        }
    });
}
/**
 *
 * 提示框公共方法
 *title:标题
 * message:消息内容
 * type:消息类型：success, warning, info, error
 * duration：显示时间, 毫秒。设为 0 则不会自动关闭
 * offset：偏移的距离
 */
export function showMessage(title,message,type,duration,offset){
    Notification(
        {
            title: title || '失败',
            message: message || '网络异常,请稍后重试~',
            type:type || 'error',
            duration:duration || 2000,
            offset:offset || 30
        }
    );
}