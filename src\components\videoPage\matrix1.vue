<!-- 视屏监控 -->
<template>
  <!-- 左侧列表和1*1展示 -->
  <div class="root">
    <div class="left">
      <p class="top_P">视频列表</p>
      <div class="list_Tab">
        <ul>
          <li
            class="list"
            v-for="(item) in dataList"
            :key="item.id"
            @click.stop="getAddress(item)"
          >
            <label :for="item.id"> {{ item.deviceName }} ({{item.onlineStatusTxt}})</label>
            <input
              type="radio"
              :id="item.id"
              :checked="curPlayVideo.id === item.id"
              style="cursor: pointer; margin-left:8px;"
            />
          </li>
        </ul>
      </div>
    </div>
    <div class="right">
      <Player
        v-if="curPlayVideo.deviceSerial"
        :key="curPlayVideo.id"
        :showPreset="true"
        :videoId="curPlayVideo.id"
      />
    </div>
  </div>

</template>

<script>

import Player from './ezuikit'

export default {
  props: {
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      curPlayVideo: {}
    };
  },
  components: {
    Player
  },
  created() {
  },
  mounted() {
    this.curPlayVideo = this.dataList[0]
  },
  methods: {
    getAddress(val) {
      this.curPlayVideo = val
    },
  },
};

</script>


<style lang="scss" scoped>
.root {
  display: flex;
}

.left,
.right {
  height: 100%;
}

.left {
  width: 30%;
  margin-right: 20px;
  display: flex;
  flex-direction: column;
  flex-grow: 0;
  flex-shrink: 0;

  .top_P {
    width: 100%;
    height: 42px;
    line-height: 42px;
    padding: 0 16px;
    color: #fff;
    font-size: 16px;
    flex-grow: 0;
    flex-shrink: 0;
    background: -webkit-gradient(
      linear,
      right top,
      left top,
      from(#103594),
      to(#1e57b2)
    );
    background: linear-gradient(270deg, #103594, #1e57b2);
  }

  .list_Tab {
    flex-grow: 1;
    /* height: 75vh; */
    font-size: 15px;
    color: #fff;
    text-align: center;
    overflow: auto;
    background: #0d2c7a;
  }
}

.right {
  flex-grow: 1;
  flex-shrink: 1;
  width: 70%;
}

.list {
  height: 36px;
  line-height: 36px;
  width: 25vw;
  padding-left: 20px;
  border-bottom: 1px solid #365f7d;
  text-align: left;
  font-size: 15px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.list label {
  cursor: pointer;
}
</style>
<style type="text/css">
video {
  object-fit: fill;
}
</style>
