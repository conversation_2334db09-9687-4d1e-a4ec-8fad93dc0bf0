<!--
 * @Description: 配电箱监测
  * @Author:
 * @Date：2025-07-16 
 * @LastEditors: dong<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-07-16 18:08:05
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">

      <IconLayout
        :showTitle="false"
        :titleObj="titleObj"
        :titleIcon="titleIcon"
        :iconList="iconList3x3"
        :cols="3"
      />
    </div>
  </div>
</template>
<script>
import { drawBarLineTotal } from "@/components/constructionRecord/Echarts/echartsOne.js";
import IconLayout from './iconLayout.vue';
import { getPowerBox } from "@/api/echrtsApi";
export default {
  components: { IconLayout },
  name: "SchemeManage",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,

      titleIcon: "",
      titleObj: {
        text: '', value: 0
      },
      iconList3x3: [

      ],
      iconList2x2: [

      ]
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
  },
  methods: {
    getBarData() {
      getPowerBox(this.projectId)
        .then((res) => {

          const { data, statusCode } = res.data;

          if (statusCode == 200) {
            if (Array.isArray(data) && data.length > 0) {
              data.forEach((item, index) => {
                var iconIndex = index % data.length;
                this.iconList3x3.push({
                  icon: require(`../../assets/customize/DistributionBox/box${iconIndex}.png`),
                  text: item.name,
                  value: item.value
                })
              });
            }
          }
        })
        .catch(() => { });
    },
    setEcharts(val) {

    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
}
.area {
  overflow: hidden;
}
</style>
