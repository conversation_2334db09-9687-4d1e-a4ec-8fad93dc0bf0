<!--
 * @Description: 一键填报授权协议
 * @Author:
 * @Date: 2022-12-22 10:25:17
 * @LastEditTime: 2023-12-25 10:10:59
 * @LastEditors: Jky200guo <EMAIL>
 * @Usage:
-->
<template>
    <div class="device-management-wrap">
      <el-dialog
        title="授权协议"
        :visible.sync="visibleFlag"
        width="30%"
        class="reportClass"
      >
      <el-row>
        <div  v-html="agreementInfo"></div>
        <!-- <div class="agreement">
          <p>《用户授权协议》 (以下简称“本协议”)是北京建科研软件有限公司 (以下简称“建科研”)与用户 (以下简称“您”) 所订立的有效合约。您通过网络页面点击同意或以其他方式选择接受本协议，即表示您与已达成协议并同意接受本协议的全部约定内容，同时默认授权您所在项目的所有项目级账户同时接受本协议。</p>
          <p>在接受本协议之前，请您仔细阅读本协议的全部内容。如您不同意本协议的内容，或无法准确理解本协议任何条款的含义，请不要进行确认及后续操作。如果您对本协议有疑问的，请通过客服渠道进行询问，其将向您解释。</p>
          <div>1、合作背景</div>
          <p>根据北京市住房和城乡建设委员会的要求，项目需每月在“风险分级管控平台”上传智慧工地应用项目佐证资料，进行智慧工地建设自评。佐证资料包括智慧工地云平台相应模块使用截图和现场设备照片以及手机端使用截图等，为方便您完成每月的智慧工地自评工作，建科研特研发此“一键填报”功能。</p>
          <div >2、功能介绍</div>
          <p>本功能根据2023年2月17日京建发【2023】34号文规定的智慧工地做法及认定关键点，72个子项分别匹配了默认抓拍模块，默认抓拍日期及时间，系统会自动调度，如您认为不合理，可在设置中心模块自行修改。同时对平台中无法捕获的资料例如现场设备照片以及手机端使用截图等，进行建议补充提醒，最后将您补充的资料与建科研捕获的资料合并形成PDF文件，您可一键导出佐证资料，去“风险分级管控平台”按条文依次上传。</p>
          <div>3、授权范围</div>
          <p>您同意将本项目智慧工地云台内所有模块及相关信息等，授权给建科研进行捕获数据截图，以达到合作双方约定的目的。</p>
          <div >4、授权期限</div>
          <p> 本授权书的授权自授权书签署之日起生效，自该项目工程完工之日至失效，双方可根据实际情况协商延长或终止授权协议。</p>
          <div >5、 保密条款</div>
          <p> 建科研会对您授权抓拍的模块数据及相关信息进行保密，不得将其泄露给此项目关联账号以外的任何第三方。 </p>
          <div >6、违约责任</div>
          <p>您必须保证智慧工地云台内上传的数据真实、合法，否则由此产生的侵权，由您负责；若建科研违反本授权书的保密条款，您有权立即终止对此协议的授权行为，并保留追究建科研违约责任的权利;</p>
          <div >7、 补充说明</div>
          <p> 关于本功能自动捕获的佐证资料仅作为辅助，需用户您根据京建发【2023】34号文规定的智慧工地做法及认定关键点的要求，核实佐证资料的合理性，由此佐证资料产生的一起后果，由您负责。</p>
          <p> 如您认为本功能设计有待优化，欢迎您通过客服渠道进行投诉建议。</p>
          <div>公司地址：北京市海淀区三里河路39号院13号楼三层</div>
          <div>客服电话：010-64405889</div>
        </div> -->
        <div class="accept">
          <el-checkbox v-model="checked">我同意</el-checkbox>
        </div>
        <div class="accept">
        <el-button :disabled="!checked" type="primary" @click="handleAdd">确 定</el-button>
      </div>
    </el-row>
      </el-dialog>
    </div>
</template>

<script>
import { saveAgreement,getAgreement } from "@/api/report";

export default {
  name: "customizePage",
  components: {
  },
  props: {
    customizeVisible: Boolean,
    CustomizeList: Array,
    personCustomize: Array
  },
  data() {
    return {
      visibleFlag: false,
      agreementInfo: "",
      id: 0,
      checked: false
    };
  },
  created() {
  },
  computed: {},
  methods: {
    open(){
      this.checked=false
      getAgreement().then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          if(result.data.isAgree){
            this.$router.push('/Authorization')
          }
          this.visibleFlag=true
          this.agreementInfo = result.data.content;
          this.id=result.data.id
        }
      });
    },
    // 关闭
    handleClose() {
      this.visibleFlag=false
    },
    // 保存
    handleAdd() {
        let parmas = {
          id: this.id,
          isAgree: this.checked
        };
        saveAgreement(parmas)
          .then(res => {
            let {
              data: { statusCode, errors }
            } = res;
            if (statusCode == 200) {
              this.visibleFlag=false
              // 跳转佐证资料
              this.$router.push('/Authorization')
            } else {
              this.$message.error(errors);
            }
            this.visibleFlag=false
          })
          .catch(() => {
            this.visibleFlag=false
          });
    },

  }
};
</script>

<style lang="scss">
  .device-management-wrap{
    .accept{
  margin: 20px 0;
  text-align: center;
  }
  .agreement{
    height: 450px;
    overflow: auto;
    font-size: 16px;
    line-height: 35px;
    padding: 5px 20px;
    p{
      text-indent:2em;
      font-size: 16px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 30px;
      -webkit-background-clip: text;
    }
  }
  }
</style>
