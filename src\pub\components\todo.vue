<template>
  <div style="width:100%">
    <div class="nav">
      <NavButton></NavButton>
    </div>
    <div class="message_wrap">
      <el-tabs
        v-model="activeName"
        type="card"
        @tab-click="handleClick"
      >
        <el-tab-pane
          label="全部"
          name="first"
        ></el-tab-pane>
        <el-tab-pane
          label="我的待办"
          name="second"
        ></el-tab-pane>
        <el-tab-pane
          label="我的已办"
          name="third"
        ></el-tab-pane>

      </el-tabs>
      <el-row>
        <el-col>
          <el-form
            ref="form"
            :model="searchForm"
            label-width="90px"
          >
            <el-col :span="7">
              <el-form-item label="接收时间:">
                <el-date-picker
                  v-model="searchForm.time"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>

            <!-- <el-col :span="6">
              <el-form-item label="消息类型:">
                <el-select
                  placeholder="请选择"
                  v-model="searchForm.type"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="状态:">
                <el-select
                  placeholder="请选择"
                  v-model="searchForm.status"
                >
                  <el-option
                    v-for="item in statusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <el-col
              :span="4"
              :offset="0"
            >
              <el-button
                type="primary"
                class="buttonType"
                @click="getList()"
              >查询</el-button>

            </el-col>
          </el-form>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <TableList
            :border="false"
            :containerHeight="440"
            :tHeader="columns"
            :tableData="listData"
            :mulSelect="true"
            :mulSelectFixed="false"
            @selectChange="selectionChange"
          >
            <template #status="scope">
              <span :class="{ 'ing': scope.row.status == 3, 'ed' : scope.row.status == 2 }">{{scope.row.status == 2 ?  '进行中' : scope.row.status == 1 ?  '待处理' : scope.row.status == 3 ?  '已完成' : '已完成'}}</span>
            </template>
            <template #options="scope">
              <el-button
                type="text"
                @click="detailClick(scope.row)"
              >查看</el-button>
            </template>
          </TableList>
        </el-col>
      </el-row>
      <div class="pagination">
        <bottom-page
          :pagination="pagination"
          v-on:currentChange="handleCurrentChange"
          v-on:sizeChange="handleSizeChange"
        ></bottom-page>
      </div>
    </div>
  </div>
</template>
<script>
import NavButton from '@/pub/components/navButton';
import BottomPage from "@/pub/components/BottomPage";
import { getStore, setStore } from "@/util/store";
import { warningList, todoList } from '@/api/todo'
export default {
  name: "warning",
  components: {
    NavButton,
    BottomPage
  },
  data() {
    return {
      activeName: 'first',
      listData: [

      ],
      options: [{ label: '全部', value: '' }, { label: '已读', value: '' }, { label: '未读', value: '' }],
      statusOptions: [{ label: '全部', value: 0 }, { label: '已完成', value: 3 }, { label: '进行中', value: 2 }],
      moduleOptions: [{ label: '全部', value: '' }],
      searchForm: {
        time: "",
        beginTime: "",
        endTime: "",
        moduleType: 0,
        status: 0,
      },
      pagination: {
        //分页默认值
        totalSize: 0,
        pageIndex: 1,
        pageSize: 10,
        position: "center",
        layout: "total,pager,sizes,prev,next,jumper",
      },
      columns: [
        {
          prop: "index",
          label: "序号",
          type: "codeNum",
          width: 80
        },
        {
          prop: "moduleTypeName",
          label: "模块名称",
          width: 140

        },
        {
          prop: "title",
          label: "消息名称",
        },
        {
          prop: "name",
          label: "发起人",
          width: 140
        },
        {
          prop: "status",
          type: "slot",
          label: "状态",
          width: 140
        },
        {
          prop: "time",
          label: "接收时间",
          width: 160
        },

        {
          prop: 'options',
          type: "slot",
          label: '操作',
          width: 150
        },
      ],
      userId: 0,
      companyId: 0,
      projectId: 0,
      projectName: '',
    };
  },
  created() {
    this.userId = getStore({ name: 'userId' }) || getStore({ name: 'UserId' });
    this.companyId =
      getStore({ name: 'companyId' }) || getStore({ name: 'CompanyId' });
    this.projectId =
      getStore({ name: 'projectId' }) || getStore({ name: 'ProjectId' });
    this.projectName = getStore({ name: 'projectName' });
    this.getList();
  },
  methods: {
    handleClick(val) {
      this.pagination.pageIndex = 1;

      this.searchForm.status = val.name == 'second' ? 0 : val.name == 'third' ? 3 : 0;
      this.getList()
    },
    async getList() {
      this.searchForm.beginTime = this.searchForm.time[0]
      this.searchForm.endTime = this.searchForm.time[1]
      let params = {
        ...this.pagination,
        ...this.searchForm,
        status: this.activeName == 'second' ? 1 : this.searchForm.status
      }
      let { data } = await todoList(params);
      console.log(data, 'warigng')
      let res = data.data;
      this.listData = res.items;
      this.pagination.totalSize = res.totalCount;
      this.listData.map(item => {
        let arr = JSON.parse(item.metadata);
        switch (item.moduleType) {
          case 1:
            item.moduleTypeName = "质量检查"
            break;
          case 2:
            item.moduleTypeName = "物质申请"
            break;
          case 3:
            item.moduleTypeName = "安全检查"
            break;
        }
        if (item.moduleType == 1 || item.moduleType == 3) {//质量检查
          let name = item.moduleType == 1 ? '质量' : item.moduleType == 3 ? '安全' : '';
          item.title = ' 收到一条【' + (arr[0] ? arr[0].value : '') + '】' + name + '检查问题需整改';
          item.name = arr[1].value;
          //item.status = arr[2].value;
          item.time = arr[4].value;
        } else if (item.moduleType == 2) {//物资申请
          item.name = arr[0].value;
          item.time = arr[1].value;
          item.title = arr[0].value + ' 发起：【物资进场申请-' + (arr[3] ? arr[3].value : '') + '】 待审批消息';
        }

      })
    },
    selectionChange() { },
    detailClick(val) {
      //moduleType 1质量检查 2物资申请
      if (val.moduleType == 1) {
        window.open(`${window.location.protocol}//${window.location.hostname}:8201/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/dailyCheck/viewPage?id=${val.moduleRelatedId}`);
      } else if (val.moduleType == 2) {
        window.open(`${window.location.protocol}//${window.location.hostname}:8856/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/detail/index?id=${val.moduleRelatedId}`);
      } else if (val.moduleType == 3) {
        window.open(`${window.location.protocol}//${window.location.hostname}:8096/?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}#/dailyCheck/viewPage?id=${val.moduleRelatedId}`);
      }
    },
    handleCurrentChange(val) {
      this.pagination.pageIndex = val;
      this.getList();
    },
    handleSizeChange(val) {
      this.pagination.pageIndex = 1;
      this.pagination.pageSize = val;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
.nav {
  margin-top: 90px;
}
.message_wrap {
  background: #0b3472;
  padding: 20px 50px 0;
  width: 90%;
  margin: 0 auto;
}
.pagination {
  text-align: right;
  padding: 10px 0 0 0;
}
/deep/ .el-tabs__item {
  color: #fff !important;
}
/deep/.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  color: #409eff !important;
}
/deep/ .message_wrap {
  .el-input__inner {
    border: 1px solid #fff !important;
    background: transparent !important;
  }
  .el-select {
    .el-input__inner {
      border: 0 !important;
    }
  }
}
.ing {
  background: #70b603;
  color: #fff;
  padding: 0.5rem 1.25rem;
  margin-top: 0.0625rem;
  display: inline-block;
  border-radius: 20px;
  box-sizing: content-box;
  line-height: 10px;
}
.ed {
  background: #02a7f0;
  color: #fff;
  padding: 0.5rem 1.25rem;
  margin-top: 0.0625rem;
  display: inline-block;
  border-radius: 20px;
  box-sizing: content-box;
  line-height: 10px;
}

>>> .el-pagination__editor.el-input {
  border: none !important;
}
/deep/ .el-table td,
/deep/ .el-table th {
  border-right: 1px solid #ebeef5;
}
/deep/ .el-table td:nth-child(1),
/deep/ .el-table th:nth-child(1) {
  border-left: 1px solid #ebeef5;
}

/deep/ .el-table th {
  border-top: 1px solid #ebeef5;
}
</style>
