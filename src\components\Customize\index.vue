<!--
 * @Description: 定制化弹框
 * @Author:
 * @Date: 2022-12-22 10:25:17
 * @LastEditTime: 2025-07-21 16:40:07
 * @LastEditors: dongqi<PERSON>qian
 * @Usage:
-->
<template>
  <el-dialog
    title="提示"
    :visible.sync="visibleFlag"
    width="100%"
    :show-close="false"
    :fullscreen="true"
    custom-class="customizeStyle"
  >
    <div class="content customize-content">
      <div class="sides">
        <div
          class="box"
          v-for="(items, key) in silderLeft"
          :key="key"
        >
          <draggable
            v-model="allList[items - 1].arrList"
            :group="allList[items - 1].group"
            animation="600"
            @start="onStart"
            @end="onEnd"
          >
            <transition-group>
              <div
                class="itemImg"
                v-for="(item, itKey) in allList[items - 1].arrList"
                :key='item.id'
              >
                <img
                  v-if="$IsProjectShow"
                  :src="item.moduleImg"
                  alt="图片"
                />
                <img
                  v-else
                  :src="BASEURL + item.moduleImg"
                  alt="图片"
                />
                <div
                  v-if="$IsProjectShow"
                  style="position: relative;
                  bottom: 60%;
                  font-size: 23px;
                  font-weight: 600;text-align: center;"
                >
                  <div>
                    {{item.name_zh}}
                  </div>
                  <div>
                    {{item.name_en}}
                  </div>
                </div>
                <i
                  @click="delectMethods(items)"
                  class="el-icon-delete"
                ></i>
              </div>
            </transition-group>
          </draggable>
        </div>
      </div>
      <div class="middleStyle">
        <div class="TopStyle">
          <i
            :style="{ color: currentPage == 1 ? '#86898d' : '#fff' }"
            @click="goNext('pre')"
            class="el-icon-arrow-left"
          ></i>
          <draggable
            v-model="contentList"
            group="site"
            animation="600"
            ghostClass="ghostClass"
            chosenClass="chosenClass"
            dragClass="dragClass"
            :forceFallback="true"
            @start="onStart"
            @end="onEnd"
          >
            <transition-group class="middleList">
              <div
                class="itemImgM"
                v-for="(item, itKey) in contentList"
                :key='item.id'
                v-cloak
              >
                <img
                  v-if="$IsProjectShow"
                  :src="item.moduleImg"
                  alt="图片"
                />
                <img
                  v-else
                  :src="BASEURL + item.moduleImg"
                  alt="图片"
                />
                <div
                  v-if="$IsProjectShow"
                  style="position: relative;
                  bottom: 60%;
                  font-size: 23px;
                  font-weight: 600;text-align: center;"
                >
                  <div>
                    {{item.name_zh}}
                  </div>
                  <div>
                    {{item.name_en}}
                  </div>
                </div>
              </div>
            </transition-group>
          </draggable>
          <i
            :style="{
              color: currentPage == allPage ? '#86898d' : '#fff'
            }"
            @click="goNext('next')"
            class="el-icon-arrow-right"
          ></i>
        </div>
        <div class="bottomStyle">
          <el-button @click="handleClose">{{ $t(`Cancellation`) }}</el-button>
          <el-button
            @click="handleAdd"
            :loading="addLoading"
          >{{ $t(`Determine`) }}</el-button>
          <!-- <p @click="handleClose">取消</p>
          <p @click="handleAdd">确定</p> -->
        </div>
      </div>
      <div class="sides">
        <div
          class="box"
          v-for="(items, key) in silderRight"
          :key="key"
        >
          <draggable
            v-model="allList[items - 1].arrList"
            :group="allList[items - 1].group"
            animation="600"
            @start="onStart"
            @end="onEnd"
          >
            <transition-group>
              <div
                class="itemImg"
                v-for="(item, itKey) in allList[items - 1].arrList"
                :key='item.id'
              >
                <img
                  v-if="$IsProjectShow"
                  :src="item.moduleImg"
                  alt="图片"
                />
                <img
                  v-else
                  :src="BASEURL + item.moduleImg"
                  alt="图片"
                />
                <div
                  v-if="$IsProjectShow"
                  style="position: relative;
                  bottom: 60%;
                  font-size: 23px;
                  font-weight: 600;text-align: center;"
                >
                  <div>
                    {{item.name_zh}}
                  </div>
                  <div>
                    {{item.name_en}}
                  </div>
                </div>
                <i
                  @click="delectMethods(items)"
                  class="el-icon-delete"
                ></i>
              </div>
            </transition-group>
          </draggable>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { saveUserConfigure } from "@/api/customizeApi";
import draggable from "vuedraggable";
export default {
  name: "customizePage",
  components: {
    draggable
  },
  props: {
    customizeVisible: Boolean,
    CustomizeList: Array,
    personCustomize: Array
  },
  data() {
    return {
      // dialogVisible: true,
      //定义要被拖拽对象的数组
      allList: [
        {
          index: 1,
          arrList: [],
          group: {
            name: "site",
            put: () => {
              return this.allList[0].arrList.length < 1;
            }, //可以拖入
            pull: false
          }
        },
        {
          index: 2,
          arrList: [],
          group: {
            name: "site",
            put: () => {
              return this.allList[1].arrList.length < 1;
            }, //可以拖入
            pull: false
          }
        },
        {
          index: 3,
          arrList: [],
          group: {
            name: "site",
            put: () => {
              return this.allList[2].arrList.length < 1;
            }, //可以拖入
            pull: false
          }
        },
        {
          index: 4,
          arrList: [],
          group: {
            name: "site",
            put: () => {
              return this.allList[3].arrList.length < 1;
            }, //可以拖入
            pull: false
          }
        },
        {
          index: 5,
          arrList: [],
          group: {
            name: "site",
            put: () => {
              return this.allList[4].arrList.length < 1;
            }, //可以拖入
            pull: false
          }
        },
        {
          index: 6,
          arrList: [],
          group: {
            name: "site",
            put: () => {
              return this.allList[5].arrList.length < 1;
            }, //可以拖入
            pull: false
          }
        }
      ],
      silderLeft: [1, 3, 5],
      silderRight: [2, 4, 6],
      currentPage: 1,
      minCurrentPage: 0,
      BASEURL: this.$BASEURL,
      contentList: [],
      addLoading: false,
      handleFlag: false,
      projectId: 0,

    };
  },
  created() {
    this.contentList = this.CustomizeList;
    this.projectId = getStore({
      name: "projectId"
    });
    const { personCustomize } = this;
    if (personCustomize.length) {
      personCustomize.forEach(items => {
        this.allList[items.moduleSort - 1].arrList.push(items);
      });
    }
  },
  computed: {
    allPage() {
      if (this.contentList.length > 0) {
        return Math.ceil(this.contentList.length / 4);
      } else {
        return 0;
      }
    },
    visibleFlag() {
      return this.customizeVisible;
    }
    // contentList() {
    //   return this.CustomizeList;
    // },
  },
  methods: {
    // 关闭
    handleClose() {
      this.$emit("closeCustomize", false);
    },
    // 保存
    handleAdd() {
      const { allList, handleFlag } = this;
      let isFlag = allList.every(items => items.arrList.length > 0);
      if (!handleFlag) {
        this.handleClose();
        return;
      }
      if (isFlag) {
        this.addLoading = true;
        let parmas = this.setData(allList);

        saveUserConfigure(parmas)
          .then(res => {
            let {
              data: { statusCode, errors }
            } = res;
            if (statusCode == 200) {
              this.$message.success("首页配置成功");
              this.$emit("closeCustomize", true);
            } else {
              this.$message.error(errors);
            }
            this.addLoading = false;
          })
          .catch(() => {
            this.addLoading = false;
          });
      } else {
        this.$message.error("配置没有配置完整!");
      }
    },
    // 设置数据
    setData(val) {
      let data = [];
      val.forEach(ele => {
        data.push({
          ...ele.arrList[0],
          moduleSort: ele.index,
          id: 0,
          projectId: Number(this.projectId)
        });
      });
      return data;
    },
    showData(val) {
      return val < this.currentPage * 4 && val >= this.minCurrentPage * 4;
    },
    //开始拖拽事件
    onStart() { },
    //拖拽结束事件
    onEnd() {
      this.handleFlag = true;
      const { contentList } = this;

      if (Math.ceil(contentList.length / 4) < this.currentPage) {
        this.goNext("pre");
      }
    },
    delectMethods(val) {
      this.handleFlag = true;
      this.contentList.push(this.allList[val - 1].arrList[0]);
      this.allList[val - 1].arrList = [];
    },
    goNext(type) {
      const { currentPage, contentList } = this;
      let heightVal = document.getElementsByClassName("TopStyle")[0]
        .clientHeight;
      let scrollDiv = document.getElementsByClassName("middleList")[0];
      if (type == "next") {
        if (currentPage < Math.ceil(contentList.length / 4)) {
          this.currentPage = currentPage + 1;
          this.minCurrentPage = currentPage;
          scrollDiv.style.top = -heightVal * currentPage + "px";
          scrollDiv.style.transition = "0.8s";
        }
      } else if (type == "pre") {
        if (currentPage > 1) {
          this.currentPage = currentPage - 1;
          this.minCurrentPage = currentPage - 2;
          scrollDiv.style.top = -heightVal * this.minCurrentPage + "px";
          scrollDiv.style.transition = "0.8s";
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.customizeStyle {
  .el-dialog__header {
    display: none !important;
  }
  .content {
    width: 100%;
    height: 100%;
    padding: 90px 30px 20px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    box-sizing: border-box;
    background: url("~@/assets/customize/cus_bg.jpg") no-repeat;
    background-size: 100% 100%;
    .sides {
      width: 410px;
      display: flex;
      flex-direction: column;
      .box {
        width: 100%;
        height: 33.3%;
        flex-shrink: 0;
        > div {
          width: 100%;
          height: 100%;
          background: url("../../assets/customize/bg.png") no-repeat;
          background-size: 100% 100%;
        }
        span {
          width: 100%;
          height: 100%;
          display: inline-block;
        }
      }
    }
    .middleStyle {
      width: calc(100% - 800px);
      .TopStyle {
        height: 650px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        > div {
          width: 880px;
          height: 650px;
          position: relative;
        }
        .middleList {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          top: 0px;
          transition: 0.8s;
          position: absolute;
        }
        .itemImgM {
          width: 359px;
          height: 266px;
          margin: 30px 38px 30px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .bottomStyle {
        display: flex;
        flex-direction: row;
        justify-content: center;
        margin-top: 60px;
        button {
          width: 140px;
          height: 40px;
          // line-height: 40px;
          font-size: 18px;
          font-weight: bold;
          text-align: center;
          color: #fefefe;
          border-radius: 2px;
          padding: 0;
          &:nth-child(1) {
            background: #3c67d3;
            margin-right: 50px;
          }
          &:nth-child(2) {
            background: #2ccae6;
          }
        }
      }
    }
    .itemImg {
      width: 100%;
      height: 100%;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 86%;
        height: 86%;
        margin-top: -1px;
      }
      .el-icon-delete {
        position: absolute;
        right: 16px;
        top: 22px;
        font-size: 20px;
      }
      // border: 1px solid red;
    }
  }
  .el-icon-arrow-left {
    font-size: 30px;
  }
  .el-icon-arrow-right {
    font-size: 30px;
  }
}
</style>
<style>
.customizeStyle {
  background: rgba(0, 0, 0, 0.4) !important;
}
.customizeStyle .el-dialog__header {
  display: none !important;
}
.customizeStyle .el-dialog__body {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  width: 100%;
  height: 100%;
}
[v-cloak] {
  display: none;
}

/* chosenClass 选中元素的样式问题 */
.chosenClass {
  width: 359px !important;
  height: auto !important;
}

/* dragClass拖动元素的样式问题 */
/* .dragClass{
  width: 359px!important;
  height: auto!important;
} */
</style>
