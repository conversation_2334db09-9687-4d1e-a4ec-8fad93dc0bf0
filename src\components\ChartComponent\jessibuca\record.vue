<!--
 * @Description: 
 * @Author: 
 * @Date: 2024-05-27 17:01:07
 * @LastEditTime: 2024-05-31 14:45:25
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Usage: 
-->
<template>
  <div>
    <div class="jess-time-area">
      <span class="dot"></span>
      <span class="value">{{ formatTime(timer) }}</span>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      running: false,
      startTime: 0,
      timer: 0,
    }
  },
  methods: {
    startTimer() {
      this.running = true;
      this.startTime = Date.now();
      this.updateTimer();
    },
    stopTimer() {
      this.running = false;
      this.timer = 0;
      clearInterval(this.timerInterval);
    },
    updateTimer() {
      this.timerInterval = setInterval(() => {
        this.timer = Math.floor((Date.now() - this.startTime) / 1000);
      }, 1000);
    },
    formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const remainingSeconds = seconds % 3600;
      const minutes = Math.floor(remainingSeconds / 60);
      const secondsDisplay = remainingSeconds % 60;
      if (hours > 0) {
        return `${this.padTime(hours)}:${this.padTime(minutes)}:${this.padTime(secondsDisplay)}`;
      } else {
        return `${this.padTime(minutes)}:${this.padTime(secondsDisplay)}`;
      }
    },
    padTime(time) {
      return (time < 10 ? '0' : '') + time;
    },
  }
}
</script>

<style lang="scss" scoped>
.jess-time-area {
  .dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    background: red;
    border-radius: 100%;
    margin: 0 4px 1px 4px;
  }
  .value {
  }
}
</style>