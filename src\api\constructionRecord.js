import Axios from '@/router/axios'
export function getCityData(query) {
  let url = query ? `/city/api/area/${query}` : '/city/api/area'
  return Axios({
    url: url,
    method: 'get',
    isHeaders: true,
    isServer: true
  })
}


//左上角施工总进度
export function getAllProgress(query) {
  return Axios({
    url: `/api/construction-module/task-all-out/${query}`,
    method: 'get',
    data: query
  })
}
//左上角各个进度
export function getProgress(query) {
  return Axios({
    url: `/api/construction-module/task-end-out/${query}`,
    method: 'get',
    data: query
  })
}

// 质量验收 柱状图
export function qualityBarData(query) {
  return Axios({
    url: `/api/quality-accept-module/company-quality-count`,
    method: 'post',
    data: query
  })
}
// 质量验收 弹框图
export function qualityDialogData(query) {
  return Axios({
    url: `/api/quality-accept-module/project-quality/${query}`,
    method: 'get',
    data: query
  })
}
// 现场劳务
export function getData(query) {
	return Axios({
		url: `/api/labor-m-module/piechattana-lysis-from-attendance-record-today`,
		method: 'post',
		data: query
	})
}
// 劳务弹框
export function getDataDailog(query) {
	return Axios({
    url: `/api/labor-m-ver2-module/web-piechartanalysis-from-attendance-record-chart/${query.projectid}/${query.type}`,
		method: 'get',
		data: query
	})
}

// 进度管理下拉框
export function selectData(query) {
	return Axios({
		url: `/company/project-list`,
		method: 'post',
		data: query
	})
}
//右下角劳务点击框第一排第一个
export function getTodayNum(query) {
  return Axios({
    url: `/api/labor-m-module/realtime-attendance`,
    method: 'post',
    data: query
  })
}
//删除
export function delData(query) {
  return Axios({
    url: `/t-special-check/${query}`,
    method: 'delete',
    data: query
  })
}
//新增
export function addData(query) {
  return Axios({
    url: '/t-special-check',
    method: 'post',
    data: query
  })
}
//模板库接口
//获取模板库的列表数据
export function temLibData(query) {
  return Axios({
    url: '/t-template/data',
    method: 'post',
    data: query
  })
}
//创建模板库的列表数据
export function addTemLibData(query) {
  return Axios({
    url: '/t-template',
    method: 'post',
    data: query
  })
}
//查看模板库的详情数据
export function detailTemLibData(query) {
  return Axios({
    url: `/t-template/${query}`,
    method: 'get',
    data: query
  })
}

// 风险变化趋势
export function changeData(query) {
	return Axios({
		url: `/api/risk-control-module/default-risk-change-data`,
		method: 'post',
		data: query
	})
}

//  "隐患变化趋势"
export function riskChangeData(query) {
	return Axios({
		url: `/api/security-check-module/default-hazard-change-data`,
		method: 'post',
		data: query
	})
}

//项目信息
export function getIntroduction(query) {
	return Axios({
		url: `/project-manage/project-extend/${query}`,
		method: 'get',
		data: query
	})
}

