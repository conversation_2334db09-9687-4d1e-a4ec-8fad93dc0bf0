<!--
 * @Description:
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2022-09-13 17:30:45
 * @LastEditors: lihanbing
 * @Usage:
-->
<!-- 质量验收 -->
<template>
  <div class="box" ref="qualityBoxRef">
    <div
      id="barChart"
      :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
    ></div>
  </div>
</template>
<script>
import { drawBar4 } from './Echarts/echarts';
import { qualityDialogData } from '@/api/constructionRecord';
export default {
  components: {},
  name: 'qualityAcceptance',
  data() {
    return {
      projectId: '',
      companyId: '',
      barWidth: null,
      barHeight: null,
      barParams: {
        dom: 'barChart',
        xAxisData: [],
        seriesData: [],
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: 'projectId',
    });
    this.companyId = getStore({
      name: 'companyId',
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener('resize', function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.qualityBoxRef.offsetWidth - 40;
      this.barHeight = this.$refs.qualityBoxRef.offsetHeight;
    },
    getBarData() {
      qualityDialogData(this.projectId)
        .then((res) => {
          let result = res.data;
          if (result.statusCode == 200) {
            let arr1 = [],
              arr3 = [];
            if (result.data && result.data.length > 0) {
              result.data.forEach((ele) => {
                arr3.push(ele.monthPercent);
                arr1.push(ele.month);
              });
            }
            this.barParams.xAxisData = arr1.reverse();
            this.barParams.seriesData = arr3.reverse();
            drawBar4(this.barParams);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
}

</style>
