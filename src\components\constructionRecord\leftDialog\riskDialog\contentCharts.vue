<!-- 数据分析 “体温”图表内容 -->
<template>
  <el-row class="content-echarts">
    <el-col class="content-box">
      <div class="list">
        <ul class="list_top">
          <li
            v-for="item in list_left"
            :key="item.value"
            :class="item.isChecked ? 'actived' : ''"
            @click="itemClick(item)"
          >
            <div class="name">{{ item.name }}</div>
          </li>
        </ul>
      </div>
      <div class="content" :key="keyTimer">
        <div>
          <el-date-picker
            v-model="trainForm.checkPersonId"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
          <el-button style="margin-left: 20px"> 统计</el-button>
        </div>
        <!-- 隐患级别对比 / 隐患类型对比 -->
        <div v-show="this.tabShow == '隐患类型对比' || this.tabShow == '隐患级别对比'">
          {{this.tabShow}}
          <div
            id="barBar"
            :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
          ></div>
        </div>
        <!-- 隐患整改情况 纵轴折叠-->
        <div v-show="this.tabShow == '隐患整改情况'">
          {{this.tabShow}}
          <div
            id="zheBar"
            :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
          ></div>
        </div>
        <!-- 隐患变化趋势 折线图折叠-->
        <div v-show="this.tabShow == '隐患变化趋势'">
          <div
            id="zheLine"
            :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
          ></div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
// import { drawPie, drawLine,drawLine2 } from "@/util/echarts";
// import {
//   getPieList,
//   getTemperature,
//   getAttendance,
// } from "@/api/statisticalAnalysis";
// import {deepClone} from '@/util/util'
export default {
  components: {},
  name: "contentCharts",
  props: {
    tabShow: {
      type: String,
      default: "隐患级别对比",
    },
  },
  data() {
    return {
      barWidth: 1000,
      barHeight: 550,
      keyTimer: "",
      actived: false,
      isRectify: false,
      isChange: false,
      list_left: [
        {
          name: "全部",
          id: 0,
          value: "temperature",
          isChecked: true,
          isShow: false,
        },
        {
          name: "北京市政2",
          id: 1,
          value: "jobsType",
          isChecked: false,
          isShow: false,
        },
        {
          name: "房地产项目",
          id: 2,
          value: "originPlace",
          isChecked: false,
          isShow: false,
        },
        {
          name: "建科研智慧建造云平台",
          id: 3,
          value: "age",
          isChecked: false,
          isShow: false,
        },
      ],
      scheduleDialog: false,
      trainForm: {
        checkPersonId: 1,
      },
      peopleOptions: [
        { label: "请选择", value: 0 },
        { label: "建科研智慧建造云平台", value: 1 },
        { label: "北京市政", value: 2 },
        { label: "房地产2", value: 3 },
      ],
      barParams: {
        dom: "barBar",
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
      barParams2: {
        dom: "barBar2",
        data1: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        data2: [320, 332, 301, 334, 390, 330, 320],
        data3: [220, 182, 191, 234, 290, 330, 310],
      },
      lineParams: {
        dom: "lineBar3",
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
      pickerOptions: {
        shortcuts: [
          {
            // text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime());
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  created() {
    // this.projieceid = getStore({ name: "ProjectID" });
    // let params = { ProjectID: this.projieceid };
    // this.getPieData(params);
    // this.lineParams.nameTitle = this.tabShowRight;
    // if (this.tabShowRight == "体温情况") {
    //   this.getTemperatureData(params);
    //   this.lineParams.isMoreLine = false;
    //   this.lineParams.seriesData = {
    //     type: "line",
    //     data: [],
    //   };
    // } else {
    //   this.lineParams.isMoreLine = true;
    //   this.lineParams.seriesData = [];
    //   this.getAttendanceData(params);
    // }
  },
  mounted() {
    // console.log("隐患：" + this.tabShow);
  },
  methods: {
    checkPersonSelect(val) {
      this.peopleOptions.forEach((ele) => {
        if (ele.value == val) {
          this.trainForm.checkPersonId = ele.value;
          // this.searchForm.checkPersonName = ele.label;

          //传递参数值，图表数据发生变化
        }
      });
    },
    itemClick(val) {
      if (val.disabled) return false;
      this.list_left.forEach((ele) => {
        if (ele.id == val.id) {
          ele.actived = true;
          ele.isChecked = true;
        } else {
          // ele.actived = 0;
          ele.actived = false;
          ele.isChecked = false;
        }
      });
      this.tabShow = val.name;
      this.keyTimer = +new Date();
      // this.$emit("titleClick", val);
      this.$nextTick(() => {
        this.isBtnClick = false;
      });
    },
    // //饼图
    // getPieData(query) {
    //   getPieList(query)
    //     .then((res) => {
    //       let result = res.data;
    //       if (result.statusCode == 200) {
    //         if (result.data && result.data.length > 0) {
    //           result.data.forEach((ele) => {
    //             if (ele.condition == this.tabShow) {
    //               if (ele.piecharValue.length > 0) {
    //                 ele.piecharValue.forEach((v) => {
    //                   v.name = v.field;
    //                   v.value = v.fieldvalues;
    //                 });
    //               }
    //               this.pipParams.data = ele.piecharValue;
    //             }
    //           });
    //         }
    //         drawPie(this.pipParams);
    //       }
    //     })
    //     .catch(() => {});
    // },
    // //体温折线图
    // getTemperatureData(query) {
    //   getTemperature(query).then((res) => {
    //     let result = res.data;
    //     if (result.statusCode == 200) {
    //       if (result.data && result.data.length > 0) {
    //         result.data.forEach((ele) => {
    //           this.lineParams.xAxisData.push(ele.temperDate);
    //           this.lineParams.seriesData.data.push(ele.temnum);
    //         });
    //       }
    //     }
    //     drawLine(this.lineParams);
    //   });
    // },
    // //出勤折线图
    // getAttendanceData(query) {
    //   this.lineShow = false;
    //   getAttendance(query).then((res) => {
    //     let result = res.data;
    //     if (result.statusCode == 200) {
    //       let xData = [];
    //       if (result.data && result.data.length > 0) {
    //         result.data.forEach((ele) => {
    //           this.lineParams.legendData.push(ele.unitName);
    //           let obj = { name: ele.unitName, data: [] };
    //           if (
    //             ele.attendanceMessageList &&
    //             ele.attendanceMessageList.length > 0
    //           ) {
    //             ele.attendanceMessageList.forEach((v) => {
    //               let str = JSON.stringify(xData);
    //               str.indexOf(v.dateName) == -1 && xData.push(v.dateName);
    //               obj.data.push(v.attendanceNum);
    //             });
    //           }
    //           this.lineParams.seriesData.push(obj);
    //         });
    //       }
    //       this.lineParams.xAxisData = xData;
    //     }
    //     // drawLine(this.lineParams);
    //   });
    // },
  },
};
</script>

<style lang="scss" scoped>
.el-dialog__body {
  height: 700px !important;
}
.list {
  .list_top {
    display: flex;
    justify-content: flex-start;
    // margin-left: 5%;
  }
  ul {
    display: inline-block;
    li {
      height: 36px;
      width: 15%;
      min-width: 100px;
      font-size: 18px;
      padding-top: 10px;
      margin-right: 20px;
      margin-bottom: 20px;
      text-align: center;
      color: #fff;
      background: url(../../../../assets/tab.png) no-repeat center;
      background-size: 100% 100%;
      cursor: pointer;
      .name {
        font-size: 16px;
      }
    }
    .actived {
      background: #2f65ec;
    }
  }
}
.content-box {
  // display: flex;
  // justify-content: center;
  // margin-top: 20px;
  width: 100%;
  height: 800px;
  .box {
    width: 70%;
    min-height: 500px;
    background: #0b3472;
    margin-right: 70px;
  }
  // .el-button {
  //   background: blue;
  //   color: #fff;
  // }
}
</style>
