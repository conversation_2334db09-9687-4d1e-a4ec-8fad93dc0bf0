<!-- 进度管理弹框 -->
<template>
  <el-dialog
    :visible.sync="scheduleDialog"
    :append-to-body="true"
    width="52%"
    @close="closeCli"
  >
    <div slot="title" class="header-title">
      <span class="title-name">进度管理</span>
    </div>
    <el-row>
      <el-col>
        <el-form ref="form" :model="trainForm">
          <div class="select">
            <el-select
              v-model="trainForm.checkPersonId"
              @change="checkPersonSelect"
              placeholder="请选择"
            >
              <el-option
                v-for="item in peopleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="list">
            <span
              v-for="item in list_left"
              :key="item.value"
              :style="{ display: item.isShow ? 'none' : 'block' }"
              :class="item.isChecked ? 'actived' : ''"
              @click="itemClick(item)"
            >
              {{ item.name }}
            </span>
          </div>
          <div>
            <div style="margin-bottom: 8px">
              <span style="margin-left: 9%">2017-01-01</span>
              <span style="margin-left: 72%">2023-08-28</span>
            </div>
            <span style="font-size: 18px"> 计划工期</span>
            <el-progress
              :text-inside="true"
              :stroke-width="20"
              :percentage="percentage"
              style="margin-left: 10px; width: 90%; display: inline-block"
            ></el-progress>
          </div>
          <div class="content" :key="keyTimer">
            <div
              id="lineBar"
              :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
              style="display: inline-block; margin-top: 20px"
            ></div>
            <div class="task">
              <span class="top">0</span>
              <div>
                <img src="@/assets/jdgl_modal_rwzs.png" />
              </div>
              <span class="bottom">任务总数</span>
            </div>
          </div>
        </el-form>
      </el-col>
    </el-row>
  </el-dialog>
</template>
<script>
import { drawLineBar } from "@/util/echarts";
export default {
  name: "scheduleDialog",
  props: {},
  data() {
    return {
      keyTimer: "",
      percentage: 100,
      tabShow: "总控计划",
      isTempture: true,
      isBtnClick: false,
      actived: false,
      list_left: [
        {
          name: "总控计划",
          id: 0,
          value: "temperature",
          isChecked: true,
          isShow: false,
        },
        {
          name: "前期计划",
          id: 1,
          value: "jobsType",
          isChecked: false,
          isShow: false,
        },
        {
          name: "招采计划",
          id: 2,
          value: "originPlace",
          isChecked: false,
          isShow: false,
        },
        {
          name: "设计计划",
          id: 3,
          value: "age",
          isChecked: false,
          isShow: false,
        },
        {
          name: "施工计划",
          id: 4,
          value: "teamGroup",
          isChecked: false,
          isShow: false,
        },
      ],
      isBtnClick: false,
      scheduleDialog: false,
      trainForm: {
        checkPersonId: 1,
      },
      peopleOptions: [
        { label: "请选择", value: 0 },
        { label: "建科研智慧建造云平台", value: 1 },
        { label: "北京市政", value: 2 },
        { label: "房地产2", value: 3 },
      ],
      barWidth: 750,
      barHeight: 400,
      lineBarParams: {
        dom: "lineBar",
        // nameTitle: "统计分析",
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
    };
  },
  created() {
    // this.list_left.forEach((ele) => {
    //   ele.isChecked = ele.value === this.defaultChecked;
    //   if (this.disabled && this.disabled.length > 0) {
    //     this.disabled.forEach((v) => {
    //       v == ele.value && ((ele.isChecked = false), (ele.disabled = true));
    //     });
    //   }
    // });
  },
  // mounted() {
  //   var elem = document.getElementById("temptureBtn");
  //   elem.addEventListener("click", () => {
  //     this.isTempture = getStore({ name: "isTempture" });
  //     this.list_left.forEach((ele, i) => {
  //       if (i == 0) {
  //         ele.isShow = !this.isTempture;
  //       }
  //       if (this.isTempture) {
  //         if (i == 1) {
  //           this.itemClick(ele);
  //         }
  //       }
  //     });
  //   });
  // },
  methods: {
    closeCli() {
      this.scheduleDialog = false;
    },
    checkPersonSelect(val) {
      this.peopleOptions.forEach((ele) => {
        if (ele.value == val) {
          this.trainForm.checkPersonId = ele.value;
          // this.searchForm.checkPersonName = ele.label;

          //传递参数值，图表数据发生变化
        }
      });
    },
    titleClick(val) {
      this.tabShow = val.name;
      this.keyTimer = +new Date();
    },
    itemClick(val) {
      if (val.disabled) return false;
      this.list_left.forEach((ele) => {
        if (ele.id == val.id) {
          // ele.actived = 1;
          ele.isChecked = true;
        } else {
          // ele.actived = 0;
          ele.isChecked = false;
        }
      });
      this.tabShow = val.name;
      this.keyTimer = +new Date();
      // this.$emit("titleClick", val);
      this.$nextTick(() => {
        this.isBtnClick = false;
      });
    },
    mouseOver(val) {
      val.actived = 1;
    },
    mouseLeave(val) {
      !val.isClick && (val.actived = 0);
    },
  },
  // destroyed() {
  //   var elem = document.getElementById("temptureBtn");
  //   elem.removeEventListener("click", () => {
  //     this.isTempture = getStore({ name: "isTempture" });
  //   });
  // },
};
</script>
<style lang="scss" scoped>
.list {
  padding: 20px 0;
  padding-left: 10px;
  box-sizing: border-box;
  overflow-x: auto;
  display: flex;
  span {
    width: 120px;
    height: 20px;
    padding: 10px 10px;
    line-height: 20px;
    text-align: center;
    margin-right: 20px;
    background: rgba(42, 192, 220, 0.4);
    // background: url(../../assets/outside.png) no-repeat;
    // background: #4fa8d0;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
  }
  .active {
    background: #29bed8 !important;
  }
}
.header-title {
  border-bottom: 1px solid #4fa8d0;
}
.title-name {
  width: 100%;
  height: 0.5rem;
  line-height: 0.5rem;
  text-align: center;
  color: #00deff;
  font-size: 20px;
  font-weight: bold;
  margin-left: 45%;
  background: rgba(13, 51, 134, 0.8);
  // border-bottom: 1px solid #4fa8d0;
}
.content {
  width: 100%;
  height: 375px;
  float: left;
  .task {
    width: 80px;
    height: 100%;
    display: inline-block;
    float: right;
    margin-right: 8%;
    margin-top: 5%;
    cursor: pointer;
    text-align: center;
    .top {
      font-size: 16px;
      text-align: center;
    }
    .bottom {
      font-size: 16px;
      text-align: center;
    }
  }
}
</style>

