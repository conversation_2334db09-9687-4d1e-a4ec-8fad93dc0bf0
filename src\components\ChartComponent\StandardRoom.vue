<!--
 * @Description: 标养室
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-24 16:25:15
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box boxDouble"
        ref="StandardRoomRef"
      >
        <div
          id="temperatureChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
        <div
          id="humidityChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawDashboardTotal } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getStandardRoom } from "@/api/echrtsApi";

import pointerIcon1 from '../../assets/pointer1.png';
import pointerIcon2 from '../../assets/pointer2.png';

export default {
  components: {},
  name: "StandardRoom",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "temperatureChart",
        detailFormatter: "",
        pointerIcon: pointerIcon1,
        gradientColors: [
          { offset: 0, color: '#9EDCFF' },   // 50%
          { offset: 1, color: '#5AC3FF' }    // 100%
        ]
      },
      pieParams1: {
        dom: "humidityChart",
        detailFormatter: "",
        pointerIcon: pointerIcon2,
        gradientColors: [
          { offset: 0, color: '#A5FCFF' },   // 50%
          { offset: 1, color: '#63E6EA' }    // 100%
        ]
      },
    };
  },
  created() {
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = (this.$refs.StandardRoomRef.offsetWidth - 40) * 0.5;
      this.barHeight = this.$refs.StandardRoomRef.offsetHeight;
    },
    getBarData() {
      getStandardRoom(this.projectId)
        .then((res) => {
          let { statusCode, data } = res.data;
          if (statusCode == 200) {
            this.pieParams.detailFormatter =
              "{bold|" +
              data.temperature +
              "℃" +
              "\n}{gray|" +
              this.$t("customization.realTem") +
              "}";
            this.pieParams1.detailFormatter =
              "{bold|" +
              data.humidity +
              "%RH" +
              "\n}{gray|" +
              this.$t("customization.realHumidity") +
              "}";
            this.pieParams.dataValue = data.temperature;
            this.pieParams1.dataValue = data.humidity;
            // console.log(this.pieParams1);
            // drawDashboardTotal(this.pieParams);
            // console.log('222');
            // drawDashboardTotal(this.pieParams1);
            // setInterval(() => {
            this.setEcharts(this.pieParams);
            // }, 150);

            this.setEcharts(this.pieParams1);
          }
        })
        .catch(() => { });
    },
    setEcharts(val) {
      drawDashboardTotal(val);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.boxDouble {
  display: flex;
  flex-direction: row;
}
// .home-page-wrap .areaContent {
//   padding-top: 0 !important;
// }
</style>
