/**
 * @name: 
 * @Author: shuSheng
 * @msg: 自定义合计
 * param: 需计算的数据
 * typeList：需计算合计的prop,需定义好, Array
 * totalObj：后端返回的合计数据， Object
 * totalIndex:合计显示的列的下标,默认为0, Number
 * unit：单位,String
 * @param {type} 
 * @return: 函数注释
 */
export const handleSummaryMethod = (param, typeList, totalObj = null, totalIndex = 0, unit = '') => {
  const {
    columns,
    data
  } = param;
  const sums = [];
  if (totalObj) {
    columns.forEach((column, index) => {
      if (index === totalIndex) {
        sums[index] = "合计";
        return;
      }
      typeList.forEach(item => {
        if (item === column.property) {
          sums[index] = totalObj[column.property];
          sums[index] += unit;
        }
      });
    });
  }
  return sums;
}