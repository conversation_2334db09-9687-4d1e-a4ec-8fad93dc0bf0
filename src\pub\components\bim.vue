<!-- BIM关联进度 -->
<template>
  <div class="bim">
    <el-button
      class="finshBtn"
      @click="sureClick"
      v-if="saveBtn && isFinsh"
      type="primary"
    >选择完成</el-button>
    <div
      :id="idName || 'bim-viewer'"
      class="my-obv-viewer"
    ></div>
  </div>
</template>

<script>
import { getModleToken, getAccount, getDefaultModel } from '@/api/home'
// import { getStore } from '@/util/store';

const obvApiGlobal = null;
export default {
  components: {},
  name: 'bim',
  props: {
    idName: String,
    bimId: String,
    // token: String,
    urn: String,
    defaultHeight: Boolean,
    saveBtn: Boolean,
    hideAll: Boolean,
    checkedNode: Array,
    callback: Function,
    sectionBimsId: Array,
  },
  data() {
    return {
      type: '',
      ids: [],
      firstIn: false,
      modelId: 1,
      isFinsh: false,
      OBV: null,
      companyid: 0,
      projectId: 0,
      modelUrl: '',
      bimBucket: '',
      token: '',
      // domShow: false
    };
  },
  watch: {
    sectionBimsId: {
      handler(nVal, oVal) {
        this.sectionBimsId = nVal;
        if (this.sectionBimsId && this.sectionBimsId.length) {
          this.showSelections();
        }
      },
      immediate: true,
    },
  },
  created() {
    // this.domShow = true
    this.companyid = getStore({ name: "companyId" });
    this.projectId = getStore({
      name: "projectId"
    });
  },
  async mounted() {
    if (!this.defaultHeight) {
      this.$nextTick(() => {
        // let el = document.getElementsByClassName("my-obv-viewer");
        // el.forEach((ele) => {
        //   ele.style.height = getStore({ name: "calHeight" })
        //     ? getStore({ name: "calHeight" }) - 50 + "px"
        //     : "500px";
        // });
      });
    }
    try {
      await this.getDefaultModel();
      await this.getAccount();
      await this.getToken();
      // 检查依赖变量
      if (!this.modelUrl || !this.bimBucket || !this.token) {
        throw new Error('模型初始化依赖数据缺失');
      }
      this.init();
    } catch (e) {
      this.$message.error('BIM模型初始化失败');
    }
  },
  methods: {
    async getToken() {
      const { data: { data, statusCode, errors } } = await getModleToken(this.companyid);
      if (statusCode === 200) {
        console.log('token==', data)
        this.token = data;
      } else {
        this.$message.error(errors || '获取模型token失败');
      }
    },
    async getAccount() {
      const { data: { data, statusCode, errors } } = await getAccount(this.companyid)
      if (statusCode === 200) {
        this.bimBucket = data.bimBucket
      } else {
        this.$message.error(errors);
      }
    },
    async getDefaultModel() {
      const { data: { data, statusCode, errors } } = await getDefaultModel({
        projectId: this.projectId,
        type: 2,
      });

      if (statusCode === 200) {
        this.modelUrl = data.modelUrl
      } else {
        this.$message.error(errors);
      }
    },
    showSelections() {
      obvApiGlobal.clearSelection();
      // let ids = [1431, 1516, 1594, 1612, 1687];
      let arrids = this.sectionBimsId;
      if (arrids.length > 0) {
        let nodes = new Array();
        arrids.map((item) => {
          nodes.push({ dbId: Number(item), modelId: 1 });
        });
        obvApiGlobal.select(nodes);
      }
    },
    init() {
      let that = this;
      async function main() {
        let selection;
        // 创建实例需要传入的参数，部署环境serviceConfig 和 用户有效期getAccessToken
        let applicationOptions = {
          // 配置 OBV 服务端（BIMServer）API 服务的 origin，这个适合于私有部署的用户使用
          getAccessToken: getAccessToken,
          refreshAccessToken: getAccessToken,
          serviceConfig: {
            origin: 'https://api.cloud.pkpm.cn',
            apiContextPath: '/bimserver/viewing/v3',
          },
        };
        // 定义urn，模型的唯一标识
        let urn = `urn:bimbox.object:${that.bimBucket}/${that.modelUrl}`;
        //let urn = 'urn:bimbox.object:6ZnGNSvp5sn/杨闸（二期）1#-AR(1)_rvt';
        // 实例化 Builder，用于模型加载
        const builder = new OBV.Api.ObvBuilder();
        // 创建 Application 对象
        const application = await builder.buildApplication(applicationOptions);

        // 创建 document 管理视图，加载完成后可以调用接口
        const obvDocument = await builder.loadDocument(application, urn);
        // 创建 viewer 容器, 创建API
        const obvApi = await builder.buildViewer3d(
          application,
          document.getElementById(that.idName || 'bim-viewer')
        );

        window.obvApiGlobal = obvApi;
        // 获取三维视图
        const viewer3dItems = obvDocument.get3dGeometryItems();
        builder.load3dModels(obvApi, {
          obvDocument: obvDocument,
          viewer3dItem: viewer3dItems[0],
        });
        // 通过监听鼠标点击，选择构件
        // document.getElementById('btnSelect').onclick = () => {
        // obvApi.clearSelection()
        // let ids = [1431, 1516, 1594, 1612, 1687];
        // if (ids != null && ids != '') {
        // let arrids = this.sectionBimsId;
        // if (arrids.length > 0) {
        //   let nodes = new Array();
        //   arrids.map(item => {
        //     nodes.push({ dbId: Number(item), modelId: 1 });
        //   })
        //   obvApi.select(nodes);
        // }
        // }
        // };

        //模型id;
        const midelid = obvApi.getModelIds();
        that.modelId = midelid[0];
        OBV.SettingsController.instance.enableCache = true;
        // 监听模型加载完成（模型还没有展示出来，不可以对构件进行操作）
        obvApi.addEventListener(
          OBV.ViewerEventTypes.V3dModelLoadedEvent,
          async (event) => {
            that.isFinsh = true;
          }
        );
        // 监听模型树加载完成，可以查询模型树（getObjectTree）
        obvApi.addEventListener(
          OBV.ViewerEventTypes.V3dModelTreeLoadedEvent,
          (event) => {
            that.hideAll && obvApi.hideAll();
            setTimeout(() => {
              that.firstIn = true;
            }, 1000);
          }
        );
        // 设置监听事件
        obvApi.addEventListener(
          OBV.ViewerEventTypes.V3dSelectionChangedEvent,
          (event) => {
            selection = event.nodeIdArray;
          }
        );

        // 监听构件被显示
        obvApi.addEventListener(OBV.ViewerEventTypes.V3dShowEvent, (event) => {
          obvApi.getObjectTree(1).then((modelTreeData) => {
            let node = event.nodeIdArray;
            obvApi.select(node);
            let nodeChilds = modelTreeData._dbIdToNode;
            getIDS(node[0].dbId, nodeChilds, 1);
            that.type = 1;
          });
        });
        // 监听构件被隐藏
        obvApi.addEventListener(OBV.ViewerEventTypes.V3dHideEvent, (event) => {
          if (that.firstIn) {
            obvApi.getObjectTree(that.modelId).then((modelTreeData) => {
              let node = event.nodeIdArray;
              obvApi.deselect(node);
              let nodeChilds = modelTreeData._dbIdToNode;
              getIDS(node[0].dbId, nodeChilds, 2);
              that.type = 2;
            });
          }
        });
        // 监听几何数据加载完成，可以对构件进行操作（显隐/选中/颜色/…）
        obvApi.addEventListener(
          OBV.ViewerEventTypes.V3dGeometryLoadedEvent,
          async (event) => {

            //设置被选中构件
            that.checkedNode &&
              that.checkedNode.length > 0 &&
              (await that.checkedNodeFun(that.checkedNode, obvApi));
          }
        );
        // that.bimObj = obvApi;

        if (obvApi) {
          // 如果事先已存在cubeBackground,先将其清除
          OBV.SettingsController.instance.envmap = 0;
          obvApi.setBackground(0, 0, 0, 0);

          const toolbar = obvApi.getToolbar();
          if (toolbar) {
            // 隐藏工具栏
            toolbar.hideToolbar();
          } else {
            console.log('OBV 工具栏未创建成功！');
          }
        } else {
          console.log('OBV 未创建成功！');
        }

        if (that.callback) await that.callback();
      }

      // 调用main函数进行代码的实现
      this.$nextTick(() => {
        main();
      });
      //获取所有子节点
      function getIDS(dbid, data, type) {
        if (type == 1) {
          arrayPush(dbid);
        } else if (type == 2) {
          arrayRemove(dbid);
        }

        var childArry = getChild(dbid, data);
        if (childArry.length > 0) {
          for (var i in childArry) {
            getIDS(childArry[i].dbId, data, type);
          }
        }
      }

      //获取该节点下所有子节点
      function getChild(dbid, data) {
        var newArry = new Array();
        for (var i in data) {
          if (data[i].parentId == dbid) newArry.push(data[i]);
        }
        return newArry;
      }

      //添加元素
      function arrayPush(dbid) {
        if (that.ids.length == 0) {
          that.ids.push(dbid);
        } else {
          let res = false;
          that.ids.find((item) => {
            if (item == dbid) {
              res = true;
            }
          });
          if (!res) {
            that.ids.push(dbid);
          }
        }
        setStore({ name: 'bimData', content: that.ids, type: 'session' });
      }
      //移除元素
      function arrayRemove(dbid) {
        if (that.ids.length > 0) {
          var pos = search(that.ids, dbid);
          that.ids.splice(pos, 1);
        }
      }

      // 访问的令牌 getAccessToken 和 令牌有效期 expiresIn
      function getAccessToken(callBack) {
        let token = that.token;
        callBack(token, 36000);
      }
    },
    checkedNodeFun(val, obvApi) {
      let arr = [];
      val.forEach((ele) => {
        arr.push({
          dbId: this.bimId ? ele[this.bimId] * 1 : ele * 1,
          modelId: this.modelId,
        });
      });
      if (obvApi) {
        obvApi.select(arr);
      } else {
        window.obvApiGlobal && window.obvApiGlobal.select(arr);
      }
    },
    sureClick() {
      this.$emit('sureClick', this.type, this.ids);
    },
    uninitializeViewer() {
      if (window.obvApiGlobal) window.obvApiGlobal.uninitializeViewer();
    },
  },
  destroyed() {
    window.obvApiGlobal.uninitializeViewer();
    this.$destroy();
  },
};
</script>
<style lang="scss">
.obv-viewer .view3d-viewer .obv-viewer3d-main-canvas {
  height: 100%;
  width: 100%;
}
.my-obv-viewer {
  height: 100%;
}
.pkpm-obv-viewer .view3d-viewer {
  height: 100%;
  background: red;
}
.finshBtn {
  position: relative;
  top: 40px;
  left: 48%;
  z-index: 999;
}
</style>
