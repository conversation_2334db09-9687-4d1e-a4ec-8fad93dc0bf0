<template>
  <div
    class="container"
    :class="{ 'full-screen': isFullScreen }"
  >

    <MonitorMark
      :markers="markers"
      :projectId="projectId"
      :componentData="componentData"
      @marker-click="handleMarkerClick"
      v-if="headerTab === 'monitor'"
      :style="{ zIndex: isFullScreen ? 3 : 1 }"
    />
    <Project
      v-show="headerTab === 'project'"
      :imageUrl="currentProjectImage"
      :info="projectInfoObj"
    />

    <Bim
      v-if="headerTab === 'BIM'"
      class="bim-viewer"
      ref="bimRef"
    ></Bim>

    <div
      class="header"
      @click="handleMainClick('header')"
    >
      <!-- isShowPageBtn为true代表显示首页特有的按钮 -->
      <HeadTop
        ref="header"
        :isShowPageBtn="true"
        @btn-click="handleBtnClick"
        @tab-change="handleTabChange"
      ></HeadTop>
    </div>

    <div
      class="main"
      :style="{ zIndex: isFullScreen ? 0 : 1 }"
      @click="handleMainClick('main')"
    >
      <div
        class="side-panel left"
        v-if="showPanels"
      >
        <template v-for="(items, key) in componentData">
          <component
            :key="key"
            v-if="leftNumber.includes(items.moduleSort)"
            :is="items.component"
            :moduleName="items.moduleName"
            :allInfor="items"
          />
        </template>
      </div>
      <div
        class="center-bg"
        @click="handleCenterClick('main')"
      >
        <!-- <Bim
          v-if="headerTab === 'BIM'"
          class="bim-viewer"
          ref="bimRef"
        ></Bim> -->
      </div>
      <div
        class="side-panel right"
        v-if="showPanels"
      >
        <template v-for="(items, key) in componentData">
          <component
            :key="key"
            v-if="rightNumber.includes(items.moduleSort)"
            :is="items.component"
            :moduleName="items.moduleName"
            :allInfor="items"
          />
        </template>
      </div>
    </div>

    <div
      class="footer"
      v-if="showPanels"
    >
      <AsideMenu
        :hideSubMenu="hideSubMenu"
        @isSubmenuOpen="onHideSubMenu"
      ></AsideMenu>
    </div>

    <Report ref="Report" />

    <Customize
      @closeCustomize="closeCustomize"
      :customizeVisible="customizeVisible"
      v-if="customizeVisible"
      :CustomizeList="CustomizeList"
      :personCustomize="personCustomize"
    />

    <transition name="fade">
      <div
        v-if="showEscHint"
        class="esc-hint"
      >
        <img
          src="@/assets/esc.png"
          alt="ESC退出全屏"
        />
      </div>
    </transition>

  </div>
</template>

<script>
import VueAliplayerV2 from "vue-aliplayer-v2";
import { getIntroduction } from "@/api/constructionRecord.js";
import { gitVideoList } from "@/api/video.js";
import { getAllModule, userConfigure } from "@/api/customizeApi";
import { buryPoint } from '@/api/home';
import { getUrlParams } from "@/util/util.js";

import HeadTop from "@/pub/components/header";
import Bim from '@/pub/components/bim';
import Customize from "@/components/Customize/index.vue";
import Report from "@/components/Report/index.vue";
import Project from "./project";
import MonitorMark from "./monitorMark";
import ModuleType from "./moduleType";

import iconImg from "@/assets/customize/icon.png";
import iconReport from "@/assets/customize/report.png";

export default {
  components: {
    HeadTop,
    VueAliplayerV2,
    ModuleType,
    Customize,
    Report,
    Project,
    MonitorMark,
    Bim
  },

  data() {
    return {
      projectInfoObj: {},
      iconImg,
      iconReport,
      showPanels: true,
      // Configuration related
      componentList: [],
      leftNumber: [1, 3, 5],
      rightNumber: [2, 4, 6],
      customizeVisible: false,
      CustomizeList: [],
      personCustomize: [],
      // Background image and markers (now simplified)
      currentProjectImage: require("@/assets/pro-info.png"),
      markers: [
        { x: 700, y: 100, label: "A" },
        { x: 600, y: 200, label: "B" }
      ],
      // Other
      language: 'zh',
      projectId: 0,
      userId: null,
      headerBtn: '',
      headerTab: 'project',
      isFullScreen: false,
      showEscHint: false,
      hideSubMenu: 101, // 点击主图随机数控制子菜单隐藏
      isHideSubMenu: true, // 监听是否隐藏子菜单
    };
  },
  computed: {
    componentData() {
      let data = [];
      const { componentList } = this;
      componentList.forEach(element => {
        element.component = this.loadView(element.component);
        data.push(element);
      });
      return data;
    }
  },
  watch: {
    "$i18n.locale"(val) {
      if (val) {
        this.language = val;
      }
    },
    // 监听路由查询参数变化
    '$route.query': {
      handler(newQuery, oldQuery) {
        console.log("路由参数变化", newQuery, oldQuery);
        this.$nextTick(() => {
          const newProjectId = getUrlParams().projectId;
          console.log("新projectId==", newProjectId);
          console.log("旧projectId==", this.projectId);
          if (newProjectId && newProjectId !== this.projectId) {
            this.projectId = newProjectId;
            this.getCustomizeList();
            this.getCutomizeInfor();
            this.getProjectInfo();
          }
        });
      },
      deep: true, // 深度监听
      immediate: true
    }
  },
  created() {
    this.projectId = getStore({
      name: "projectId"
    }) || getUrlParams().projectId;
  },
  mounted() {
    this.language = getStore({ name: "language" });
    this.getCustomizeList();
    this.userId = getStore({
      name: "userId"
    }) || getUrlParams().userId;
    this.getCutomizeInfor();
    //获取项目信息
    this.getProjectInfo();
  },
  methods: {
    onHideSubMenu(val) {
      console.log('隐藏子菜单', val)
      this.isHideSubMenu = val
    },
    async getProjectInfo() {
      const { data } = await getIntroduction(this.projectId)
      this.projectInfoObj = data.data
    },
    handleBtnClick(val) {
      console.log('按钮点击', val)
      this.headerBtn = val
      if (val === 'fullScreen') {
        this.fullScreen()
      } else if (val === 'fillSubmit') { // 一件填报
        this.reportClick()
      } else if (val === 'setChart') {
        this.setCustomize()
      }
    },
    fullScreen() {
      this.isFullScreen = true;
      this.showEscHint = true;

      // 2秒后隐藏ESC提示
      setTimeout(() => {
        this.showEscHint = false;
      }, 2000);

      // 监听ESC键退出全屏
      const handleKeyDown = (e) => {
        if (e.key === 'Escape') {
          this.exitFullScreen();
          document.removeEventListener('keydown', handleKeyDown);
        }
      };
      document.addEventListener('keydown', handleKeyDown);
    },
    exitFullScreen() {
      this.isFullScreen = false;
    },
    handleTabChange(val) {
      this.headerTab = val
      this.isFullScreen = false
    },
    handleCenterClick(val) {
      console.log('点击主图', this.isHideSubMenu)
      if (val === 'main') {
        this.fullScreen()
      }
      this.hideSubMenu = Math.floor(Math.random() * 100) + 1;
    },
    handleMainClick(val) {
      console.log('点击主图', this.isHideSubMenu)
      this.hideSubMenu = Math.floor(Math.random() * 100) + 1;
    },
    reportClick() {
      // 埋点注入
      let params = {
        moduleId: 0,
        moduleName: '一键填报',
        logType: '0003-0000-0001',
        extendJson: '',
        origin: 1,
      };
      buryPoint(params).then((res) => {
        console.log(res, '一键填报埋点');
      });
      this.$refs.Report.open()
    },

    handleMarkerClick(marker) {
      // 编辑点逻辑
      console.log('Marker clicked in parent:', marker);
    },

    loadView(view) {
      return resolve =>
        require([`@/components/ChartComponent/${view}.vue`], resolve);
    },
    getCustomizeList() {
      getAllModule(this.projectId).then(res => {
        let {
          data: { statusCode, data }
        } = res;
        if (statusCode == 200) {
          if (this.$IsProjectShow) {
            data.forEach((ele) => {
              if (ele.moduleName.includes('智能测量')) {
                ele.moduleImg = '实测实量.png'
                ele.name_zh = '实测实量'
                ele.name_en = 'NowMeasure'
                ele.moduleImg = require('../../assets/customize/智能测量.png')
              }
              else if (ele.moduleName.includes('工程资料')) {
                ele.moduleImg = '工程资料.png'
                ele.name_zh = '工程资料'
                ele.name_en = 'EngineeringData'
                ele.moduleImg = require('../../assets/customize/' + ele.moduleImg + '')
              }
              else if (ele.moduleName.includes('隐患')) {
                ele.moduleImg = '隐患风险.png'
                ele.name_zh = '隐患风险'
                ele.name_en = 'DangerRisk'
                ele.moduleImg = require('../../assets/customize/' + ele.moduleImg + '')
              }
              else {
                const spaceIndex = ele.moduleName.indexOf(" ");
                ele.moduleImg = ele.moduleName.slice(0, spaceIndex) + '.png'
                ele.moduleImg = require('../../assets/customize/' + ele.moduleImg + '')
                ele.name_zh = ele.moduleName.slice(0, spaceIndex)
                ele.name_en = ele.component
              }
            })
          }
          this.CustomizeList = data;
        }
      });
    },

    getCutomizeInfor() {
      userConfigure(this.userId, this.projectId).then(res => {
        let {
          data: { statusCode, data }
        } = res;
        if (statusCode == 200) {
          this.personCustomize = JSON.parse(JSON.stringify(data));
          this.componentList = JSON.parse(JSON.stringify(data));
          if (this.$IsProjectShow) {
            this.personCustomize.forEach((ele) => {
              if (ele.moduleName.includes('智能测量')) {
                ele.moduleImg = '实测实量.png'
                ele.name_zh = '实测实量'
                ele.name_en = 'NowMeasure'
                ele.moduleImg = require('../../assets/customize/智能测量.png')
              }
              else if (ele.moduleName.includes('工程资料')) {
                ele.moduleImg = '工程资料.png'
                ele.name_zh = '工程资料'
                ele.name_en = 'EngineeringData'
                ele.moduleImg = require('../../assets/customize/' + ele.moduleImg + '')
              }
              else if (ele.moduleName.includes('隐患')) {
                ele.moduleImg = '隐患风险.png'
                ele.name_zh = '隐患风险'
                ele.name_en = 'DangerRisk'
                ele.moduleImg = require('../../assets/customize/' + ele.moduleImg + '')
              }
              else {
                const spaceIndex = ele.moduleName.indexOf(" ");
                ele.moduleImg = ele.moduleName.slice(0, spaceIndex) + '.png'
                ele.moduleImg = require('../../assets/customize/' + ele.moduleImg + '')
                ele.name_zh = ele.moduleName.slice(0, spaceIndex)
                ele.name_en = ele.component
              }
            })
          }
        }
      });
    },

    closeCustomize(val) {
      if (val) {
        this.getCustomizeList();
        this.getCutomizeInfor();
      }
      this.customizeVisible = false;
    },

    setCustomize() {
      this.customizeVisible = !this.customizeVisible;
    },
  },
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  transition: all 0.5s ease;

  &.full-screen {
    .side-panel.left {
      transform: translateX(-100%);
    }
    .side-panel.right {
      transform: translateX(100%);
    }
    .footer {
      transform: translateY(100%);
    }
  }

  .side-panel,
  .footer {
    transition: transform 0.5s ease;
  }

  /* ESC提示样式 */
  .esc-hint {
    position: fixed;
    bottom: 88px;
    left: 50%;
    transform: translate(-50%, 0);
    z-index: 999;
    padding: 20px;

    img {
      height: 62px;
    }
  }

  /* 淡入淡出动画 */
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.5s;
  }
  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }

  /* Make sure content is above background */
  .header,
  .main,
  .footer,
  .btn-box {
    position: relative;
    z-index: 0;
  }

  .header {
    height: 68px;
    z-index: 4; /* 增加这个 */
    position: relative; /* 确保定位有效 */
  }

  .footer {
    height: 75px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 20px;
    z-index: 4;
    position: relative;
  }

  .main {
    flex: 1 1 auto;
    display: flex;
    min-height: 0;
    min-width: 0;
    position: relative;
    top: 20px;

    .side-panel {
      width: 530px;
      height: 100%;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      overflow: hidden;
      padding-top: 10px;

      .area {
        width: 360px;
        flex-grow: 1;
        height: 0;
        position: relative;
      }
    }

    .left {
      background: url(../../assets/leftbg.png) no-repeat center;
      background-size: cover;
    }
    .right {
      background: url(../../assets/rightbg.png) no-repeat center;
      background-size: cover;
      display: flex;
      align-items: flex-end;
      flex-direction: column;
    }

    .center-bg {
      flex: 1 1 auto;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      min-width: 0;
      min-height: 0;
      background: transparent;

      .bim-viewer {
        height: 98%;
        width: calc(100% - 1060px);
        position: absolute;
      }
    }
  }
}

.areaContent {
  width: 100%;
  height: calc(100% - 46px);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
  .countStyle {
    top: 6px;
    right: 30px;
    font-size: 14px;
    position: absolute;
  }
  .countStyleOne {
    top: 10px;
    left: 30px;
    font-size: 14px;
    position: absolute;
    span {
      display: inline-block;
      &:nth-child(1) {
        margin-right: 40px;
      }
    }
  }
  .barCStyle {
    top: 20px;
    left: 20px;
    font-size: 14px;
    line-height: 20px;
    position: absolute;
    color: #fff;
    span {
      display: inline-block;
      &:nth-child(1) {
        margin-right: 20px;
      }
    }
    .active {
      color: #3d7bf7;
    }
  }

  .dateStyle {
    top: 20px;
    right: 26px;
    line-height: 20px;
    position: absolute;
    display: flex;
    font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
    font-size: 12px;
    color: #ffffff;
    text-align: left;
    font-style: normal;
    text-transform: none;
    padding: 0;
    margin: 0;

    span {
      display: inline-block;
      padding: 1px 12px;
      height: 20px;
      line-height: 20px;
      position: relative;
      cursor: pointer;
      color: #fff;
      background-image: url("~@/assets/rectangle.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: -8px;

      // 确保第一个元素不被前面的元素影响
      &:first-child {
        margin-left: 0;
      }

      &.active {
        background-image: url("~@/assets/rectangle_active.png");
        color: #fff !important;
        z-index: 1;
      }

      &:hover:not(.active) {
        background-image: url("~@/assets/rectangle_active.png");
        z-index: 1;
      }
    }
  }
}

.text {
  box-sizing: border-box;
  padding-left: 50px;
  // padding-top: 14px;
  line-height: 44px;
  width: 100%;
  height: 44px;
  background: url(../../assets/boxTop.png) no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: row;
  position: relative;
  font-family: "YouSheBiaoTiHei", sans-serif;
  font-weight: 400;
  font-size: 22px;
  color: #f4f8ff;
  line-height: 24px;
  text-align: left;
  font-style: normal;
  text-transform: none;

  .barStyle {
    position: absolute;
    top: 18px;
    right: 20px;
    display: flex;
    flex-direction: row;
    p {
      width: 56px;
      height: 24px;
      line-height: 24px;
      font-size: 10px;
      text-align: center;
      background: url(../../assets/customize/bar_no.png) no-repeat;
      background-size: 100% 100%;
      margin-right: 6px;
    }
    .active {
      background: url(../../assets/customize/bar_s.png) no-repeat !important;
      background-size: 100% 100% !important;
    }
  }
}
</style>