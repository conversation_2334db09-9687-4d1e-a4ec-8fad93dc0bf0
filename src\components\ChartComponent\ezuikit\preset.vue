<template>
  <div class="presetRoot">
    <div class="presetList">
      {{$t(`ManagementOfPresetPoints`)}}
      <el-tag
        v-for="(tag, idx) in presetList"
        :key="tag.id"
        closable
        @close="onDelPreset(idx)"
        @click="onClickPreset(idx)"
        size="small"
      >
        {{ tag.pointName }}
      </el-tag>

      <el-tag
        v-if="presetList.length < 3"
        size="small"
        class="addBtn"
        @click="onClickAddPresetBtn"
      >+ {{$t(`Increase`)}}</el-tag>
    </div>

    <el-dialog
      class="presetDialog"
      :title="$t(`Setpresetpoints`)"
      :visible.sync="dialogVisible"
      width="30%"
      center
    >
      <div
        class="inptW"
        style="width: 92%;"
      >
        <label style="width: 180px;text-align: right;padding: 0 10px;">{{$t(`Presetpointname`)}}</label>
        <el-input
          :placeholder="$t(`enterpreset`)"
          v-model="presetName"
        >
        </el-input>
      </div>
      <div class="footer">
        <el-button
          type="primary"
          @click="onAddPreset"
        >{{ $t(`Determine`) }}</el-button>
        <el-button @click="onClosePresetDialog">{{ $t(`Cancellation`) }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addVideoPreset,
  getVideoPresetList,
  delVideoPreset,
  delMileStoneCapture,
} from './api';

import { ptzFrondEnd } from '../jessibuca/api'

import JkyUtils from 'jky-utils';

const { videoControlMove } = JkyUtils.videoPTZ;

export default {
  name: 'Player',
  data() {
    return {
      dialogVisible: false,
      presetName: '',
      presetList: [],
      selectPresetIdx: null,
    };
  },
  props: {
    videoId: {
      type: [String, Number],
      retuired: true,
    },
    videoCfg: {
      type: Object,
      default: () => ({}),
    },
    videoType: {// 视频类型: 萤石云:'', 代建办: GB
      type: String,
      default: '',
    }
  },
  components: {},
  watch: {
    videoId(val, oldVal) {
      if (oldVal && val !== oldVal) {
        // videoId发生变化，重新获取video详情，刷新assetsToken
        this.initDatas(val);
      }
    },
  },
  created() { },
  mounted() {
    this.initDatas(this.videoId);
  },
  methods: {
    // 初始化页面数据
    initDatas(videoId) {
      this.dialogVisible = false;
      this.presetName = '';
      this.selectPresetIdx = null;
      this.mouseDownEle = null;

      this.getPresetList(videoId);
    },

    // 点击新增预设点按钮
    onClickAddPresetBtn() {
      if (this.dialogVisible) {
        this.dialogVisible = false;
        return;
      }

      if (this.presetList.length && this.presetList.length >= 3) {
        return this.$message.error('最多可设置三个预设点');
      }
      this.presetName = '';
      this.dialogVisible = true;
    },

    // 获取当前视频预设点列表
    async getPresetList(videoId) {
      if (!videoId) return;

      let res = await getVideoPresetList(videoId);
      res = res.data || {};
      if (res.statusCode !== 200) return;

      const data = res.data || [];
      const curInfo = data[0] || {};
      this.presetList = curInfo.points || [];
    },

    // 新增预设点
    async onAddPreset() {
      if (!this.presetName) {
        return this.$message('请输入预设点名称');
      }

      const userInfo = getStore({ name: 'userInfo' });

      let res = await addVideoPreset({
        infoId: this.videoId,
        userName: userInfo.account,
        pointName: this.presetName,
      });

      res = res.data;
      if (res.statusCode !== 200) {
        return this.$message.error(res.errors || '操作失败');
      }

      // 刷新预设点列表
      this.getPresetList(this.videoId);

      this.onClosePresetDialog();
    },

    onClosePresetDialog() {
      this.dialogVisible = false;
      this.presetName = '';
    },

    // 删除预设点确认框
    onDelPreset(idx) {
      const that = this;
      this.$confirm('是否确认删除预设点，删除后不可恢复', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
        .then(async () => {
          const pointInfo = that.presetList[idx];
          const { id } = pointInfo;

          let res = await delVideoPreset(id);
          res = res.data;
          if (res.statusCode !== 200) {
            return that.$message.error('操作失败，请稍后再试');
          }

          that.presetList.splice(idx, 1);
          if (that.selectPresetIdx === idx) {
            that.selectPresetIdx = null;
          }

          const _info = {
            videoInfoId: that.videoId,
            pointId: id,
          };
          // 删除视频预设点后，需要删除施工进度中相关预设点
          delMileStoneCapture(_info);

          that.$message({
            type: 'success',
            message: '删除成功!',
          });
        })
        .catch((e) => {
          console.log(e);
        });
    },

    // 点击预设点
    onClickPreset(idx) {
      if (this.videoType == 'GB') {
        this.GBPreset(idx)
      } else {
        this.ysyPreset(idx)
      }
    },

    async ysyPreset(idx) {
      const info = this.presetList[idx];
      this.selectPresetIdx = idx;
      const res = await videoControlMove({
        ...this.videoCfg,
        index: info.pointKey,
      });

      this.$message.success('预置点切换中，请稍后');

      this.$emit('fetchYSAPIAfter', res.data);
    },
    async GBPreset(idx) {
      const info = this.presetList[idx];
      this.selectPresetIdx = idx;
      const params = {
        ...this.videoCfg,
        cmdCode: 130,
        parameter1: 0,
        parameter2: info.pointKey,
        combindCode2: 0,
      }
      const res = await ptzFrondEnd(params)
      this.$message.success('预置点切换中，请稍后');

    },
  },
};
</script>

<style lang="scss" scoped>
.presetList {
  display: flex;
  margin-top: 10px;
}

>>> .el-tag {
  background: transparent;
  color: #fff;
  margin-left: 10px;
  cursor: pointer;

  &.active {
    background: rgba(47, 101, 237, 1);
    border-color: rgba(47, 101, 237, 1);
    color: #fff;
  }
}

.presetDialog {
  .inptW {
    display: flex;

    label {
      width: 100px;
      font-size: 15px;
      line-height: 42px;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
  }
}

.addBtn {
  border-color: rgba(255, 255, 255, 0.2);
}
</style>
