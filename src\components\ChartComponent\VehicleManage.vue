<!--
 * @Description: 车辆管理
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-25 10:47:28
 * @LastEditors: dong<PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div class="box" ref="VehicleManageRef">
        <div
          id="VehicleManageChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawBarLineTotal } from "@/components/constructionRecord/Echarts/echartsOne.js";
import { drawCustomBar } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getVehicleManage } from "@/api/echrtsApi";
export default {
  components: {},
  name: "VehicleManage",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      userId: "",
      barWidth: null,
      barHeight: null,
      barParams: {
        dom: "VehicleManageChart",
        xAxisData: [],
        seriesData: [],
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        yminInterval: 1,
        boundaryGap: true,
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.userId = getStore({
      name: "userId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.VehicleManageRef.offsetWidth;
      this.barHeight = this.$refs.VehicleManageRef.offsetHeight;
    },
    getBarData() {
      getVehicleManage(this.projectId)
        .then((res) => {
          const {
            data: { data: nameV, dataList },
            statusCode,
          } = res.data;
          if (statusCode == 200) {
            dataList.forEach((ele) => {
              ele.type = "bar";
              ele.itemStyle = {
                normal: {
                  color: ele.name == "进场" ? "#4ebf9d" : "#d2a823",
                },
              };
            });
            this.barParams.xAxisData = nameV;
            this.barParams.seriesData = dataList.map((item) => {
              switch (item.name) {
                case "进场":
                  item.name = this.$t("Mobilization");
                  break;
                case "出场":
                  item.name = this.$t("Exit");
                  break;
              }
              return item;
            });
            drawCustomBar(this.barParams);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}
</style>
