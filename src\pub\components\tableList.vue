<template>
  <div
    class="table-main"
    ref="tableContainer"
  >
    <el-table
      :data="tableData"
      :border="border"
      :height="containerHeight === 'auto' ? null : containerHeight"
      :max-height="maxHeight ? maxHeight : null"
      @selection-change="selectChange"
      @select="onSelect"
      @select-all="selectAll"
      ref="elTable"
    >
      <!--支持多选 col-->
      <el-table-column
        v-if="mulSelect"
        align="left"
        type="selection"
        :fixed="mulSelectFixed"
        width="60"
      >
      </el-table-column>

      <el-table-column
        v-for="(column, columnIndex) in tHeader"
        :key="columnIndex"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        align="center"
        :show-overflow-tooltip="showTooltip"
      >
        <template slot-scope="scope">
          <!-- 序号 -->
          <div v-if="column.type === 'codeNum'">
            {{ getCodeNum(scope) }}
          </div>
          <!-- 操作 -->
          <div v-if="column.type === 'slot'">
            <slot
              :name="column.prop"
              :row="scope.row"
            ></slot>
          </div>

          <div v-else>{{ scope.row[column.prop] }}</div>
        </template>
      </el-table-column>
    </el-table>
    <div
      class="pagination-container"
      v-if="pagination.totalSize"
      ref="paginationC"
    >
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.pageIndex"
        :page-size="pagination.pageSize"
        :page-sizes="pagination.pageSizes"
        :layout="pagination.layout"
        :total="pagination.totalSize"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tableData: {
      type: Array,
      default: () => [],
    }, // 表格数据
    tHeader: {
      type: Array,
      default: () => [],
    }, // 表头
    mulSelect: {
      type: Boolean,
      default: false,
    }, // 是否可多选
    mulSelectFixed: {
      type: Boolean,
      default: false,
    }, // 多选列是否固定
    showTooltip: {
      type: Boolean,
      default: true,
    }, // 内容过长是否显示tooltip
    border: {
      type: Boolean,
      default: true,
    }, // 是否带有纵向边框
    pagination: {
      type: Object,
      default: () => ({ layout: 'total,pager,sizes,prev,next,jumper' }),
    }, // 分页参数
    containerHeight: {
      type: [Number, String],
      default: 'auto',
    }, // tableHeight
    maxHeight: {
      type: [String, Number],
      default: '',
    },
  },
  methods: {
    // 序号
    getCodeNum(scope) {
      if (this.pagination.totalSize) {
        return (this.pagination.pageIndex - 1) * 10 + scope.$index + 1;
      }
      return scope.$index + 1;
    },
    // 复选框选项发生变化
    selectChange(selection) {
      this.$emit('selectChange', selection);
    },
    onSelect(selection, row) {
      this.$emit('select', selection, row);
    },
    selectAll(selection) {
      this.$emit('selectAll', selection);
    },
    toggleRowSelection(row) {
      this.$refs.elTable.toggleRowSelection(row, true);
    },
    // currentChange
    handleCurrentChange: function (val) {
      this.$emit('currentChange', val);
    },
    // size change
    handleSizeChange: function (val) {
      this.$emit('sizeChange', val);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-table {
  .el-table__header-wrapper thead tr .el-table__cell {
    .cell {
      color: #fff !important;
    }
  }
}
.pagination-container {
  padding-top: 20px;
  ::v-deep .el-input input {
    height: 26px !important;
  }
}
</style>
