<!-- 数据分析头部标签tab -->
<template>
  <el-row class="title-type">
    <el-col :span="12" align="center">
      <ul class="list_left">
        <li
          v-for="item in list_left"
          :key="item.value"
          :style="{ display: item.isShow ? 'none' : 'block' }"
          :class="item.isChecked ? 'actived' : ''"
          @click="itemClick(item)"
        >
          <div class="name">{{ item.name }}</div>
        </li>
      </ul>
    </el-col>
  </el-row>
</template>

<script>
import { setStore } from "@/util/store";
export default {
  components: {},
  name: "titleType",
  props: {
    defaultChecked: {
      type: String,
      default: "temperature",
    },
  },
  data() {
    return {
      // isTempture: true,
      list_left: [
        {
          name: "隐患级别对比",
          id: 0,
          value: "temperature",
          isChecked: true,
          isShow: false,
        },
        {
          name: "隐患类型对比",
          id: 1,
          value: "jobsType",
          isChecked: false,
          isShow: false,
        },
        {
          name: "隐患整改情况",
          id: 2,
          value: "originPlace",
          isChecked: false,
          isShow: false,
        },
        {
          name: "隐患变化趋势",
          id: 3,
          value: "changePlace",
          isChecked: false,
          isShow: false,
        },
      ],
      // isBtnClick: false,
    };
  },
  created() {
    this.list_left.forEach((ele) => {
      ele.isChecked = ele.value === this.defaultChecked;
      if (this.disabled && this.disabled.length > 0) {
        this.disabled.forEach((v) => {
          v == ele.value && ((ele.isChecked = false), (ele.disabled = true));
        });
      }
    });
    // this.list_right.forEach((ele) => {
    //   ele.isChecked = ele.value === this.defaultRight;
    //   if (this.disabled && this.disabled.length > 0) {
    //     this.disabled.forEach((v) => {
    //       v == ele.value && ((ele.isChecked = false), (ele.disabled = true));
    //     });
    //   }
    // });
  },
  // mounted() {
  //   var elem = document.getElementById("temptureBtn");
  //   elem.addEventListener("click", () => {
  //     this.isTempture = getStore({ name: "isTempture" });
  //     this.list_left.forEach((ele, i) => {
  //       if (i == 0) {
  //         ele.isShow = !this.isTempture;
  //       }
  //       if (this.isTempture) {
  //         if (i == 1) {
  //           this.itemClick(ele);
  //         }
  //       }
  //     });
  //   });
  // },
  methods: {
    itemClick(val) {
      if (val.disabled) return false;
      this.list_left.forEach((ele) => {
        if (ele.id == val.id) {
          ele.isChecked = true;
        } else {
          ele.isChecked = false;
        }
      });
      this.$emit("titleClick", val);
      this.$nextTick(() => {
        this.isBtnClick = false;
      });
    },
  },
  // destroyed() {
  //   var elem = document.getElementById("temptureBtn");
  //   elem.removeEventListener("click", () => {
  //     this.isTempture = getStore({ name: "isTempture" });
  //   });
  // },
};
</script>

<style lang="scss" scoped>
.title-type {
  .list_left {
    display: flex;
    justify-content: center;
    margin-left: 10%;
  }
  ul {
    display: inline-block;
    li {
      height: 40px;
      width: 40%;
      font-size: 18px;
      padding-top: 10px;
      margin-right: 10px;
      margin-bottom: 40px;
      text-align: center;
      color: #fff;
      // background: url(./../../assets/tab.png) no-repeat center;
      background: rgba(42, 192, 220, 0.4);
      background-size: 100% 100%;
      cursor: pointer;
      .name {
        font-size: 16px;
      }
    }
    .actived {
      background: #2f65ec;
    }
  }
}
</style>
