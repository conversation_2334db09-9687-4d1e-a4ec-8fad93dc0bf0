/*
 * @Description:
 * @Author:
 * @Date: 2023-07-29 14:59:56
 * @LastEditTime: 2025-08-01 16:47:53
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Usage:
 */
export default {
  SmartHealthCreation: "Smart health creation",
  Handyman: "Handyman",
  electric<PERSON>elder: "electric welder",
  Smartconstruction: "Smart construction",
  administrativestaff: "Administrative staff",
  Piledriveroperator: "Pile driver operator",
  Generalworkers: "General workers",
  IntelligentManageability: "Intelligent Manageability",
  SmartChuang: "Smart Chuang'an",
  Intelligentimprovement: "Intelligent improvement",
  Smartgreening: "Smart greening",
  Watersaving: "Water saving management",
  Energysaving: "Energy saving management",
  Labormanagement: "Labor management",
  Videomanagement: "Video management",
  Elevatormonitoring: "Elevator monitoring",
  Towercrane: "Tower crane monitoring",
  Distributionbox: "Distribution box monitoring",
  Intelligentmeasurement: "Intelligent measurement",
  averageRate: "Average pass rate",
  Vehiclewashing: "Vehicle washing",
  ProjectCarbon: "Project carbon management",
  foreman: "Foreman",
  qualifiedlevel: "Average qualified rate of level",
  guidingruler: "Average pass rate of guiding ruler",
  rangefinder: "Average qualification rate of rangefinder",
  anglerule: "Average pass rate of angle rule",
  grounding: "Grounding",
  break: "Break",
  SmokeDetector: "Smoke Detector",
  hightemperature: "High temperature",
  shortcircuit: "Short circuit",
  leakageofelectricity: "Leakage of electricity",
  overload: "Overload",
  outage: "Outage",
  Mobilization: "Mobilization",
  Exit: "Exit",
  Rinsed: "Rinsed",
  Insufficientflushingtime: "Insufficient flushing time",
  Driveoutarea: "Drive directly out of the flushing area",
  Detourwithoutrinsing: "Detour without rinsing",
  other: "Other",
  qualifiedtrainees: "Number of qualified trainees",
  trainingpeople: "Number of people in training",
  Untrainedpersonnel: "Untrained personnel",
  unqualifiedpersonnel: "Number of unqualified personnel",
  Artificialcarbonemissions: "Artificial carbon emissions",
  Materialcarbonemissions: "Material carbon emissions",
  Mechanicalcarbonemissions: "Mechanical carbon emissions",
  Constructionwastedischarge: "Construction waste discharge",
  Recyclingcarbonemissions: "Recycling and emission reduction",
  Anticollisions: "Anti-collision",
  moment: "Moment",
  Bricklayer: "Bricklayer",
  number: "Number",
  Proportion: "Proportion",
  Completed: "Completed",
  inprogress: "In progress",
  Notstarted: "Not started",
  Totalnumbertasks: "Total number of tasks",
  people: "people",
  Totalattendancetoday: "Total attendance today",
  Administrativearea: "Administrative area",
  Constructionarea: "Construction area",
  livingquarters: "Living quarters",
  total: "Total ",
  Accumulated: "Accumulated",
  Averagepassrate: "Average pass rate",
  largeScreen: "Large screen display",
  equipmentManagement: "Equipment management",
  Operation: "Operation",
  alarmRecord: "Alarm record",
  antiCollision: "Anti collision management",
  hookVisualization: "Hook visualization",
  todayAlarm: "Today's alarm sorting",
  earlywarning: "Earlywarn",
  giveAlarm: "alarm",
  load: "Load",
  WindSpeed: "Wind speed",
  Incline: "Incline",
  PleaseSelect: "Please select",
  NoData: "No data",
  WarningAndAlarmStatistics: "Warning and alarm statistics",
  Day: "Day",
  Month: "Month",
  History: "History",
  AlarmTypeStatistics: "Alarm type statistics",
  Whole: "Whole",
  ListOfTowerCranes: "List of tower cranes",
  Increase: "Increase",
  edit: "Edit",
  delete: "Delete",
  BasicQquipment: "Basic equipment information and settings",
  EquipmentModel: "Equipment model",
  EquipmentManager: "Equipment manager",
  InstallationPosition: "Installation position",
  ConstructionCommissionNumber: "Construction commission Number",
  Telephone: "Telephone",
  DriverInformation: "Driver information",
  Age: "Age",
  ID: "ID",
  WarningThreshold: "Warning threshold",
  LoadWarning: "Load warning",
  LoadAlarm: "Load alarm",
  WindSpeedWarning: "Wind speed warning",
  WindSpeedAlarm: "Wind speed alarm",
  MomentPercentageWarning: "Moment percentage warning",
  MomentPercentageAlarm: "Moment percentage alarm",
  TiltAngleWarning: "Tilt angle warning",
  TiltAngleAlarm: "Tilt angle alarm",
  AntiCollisionWarning: "Anti collision warning",
  AntiCollisionAlarm: "Anti collision alarm",
  LimitValue: "Limit value",
  RotationLimitL: "Rotation limit (left)",
  RotaryLimitR: "Rotary limit (right)",
  AmplitudeLimitN: "Amplitude limit (near)",
  AmplitudeLimitF: "Amplitude limit (far)",
  HeightLimitU: "Height limit (upper)",
  HeightLimitL: "Height limit (lower)",
  RealTimeTower: "Real time monitoring data for tower cranes",
  TowerCraneParameters: "Tower crane parameters",
  BalanceArmLength: "Balance arm length",
  BoomLength: "Boom length",
  BoomHeight: "Boom height",
  TowerHeight: "Tower height",
  TowerCapHeight: "Tower cap height",
  RealTime: "Real time",
  MomentPercentage: "Moment percentage",
  DipAngle: "Dip angle",
  MinimumCollision: "Minimum collision prevention",
  Rotation: "Rotation",
  Amplitude: "Amplitude",
  Height: "Height",
  Date: "Date",
  StartDate: "Start date",
  To: "to",
  EndDate: "End date",
  Query: "Query",
  SerialNumber: "Serial number",
  LiftingTime: "Lifting time",
  UnloadingTime: "Unloading time",
  MaximumMoment: "Maximum moment percentage",
  LiftingPointHeight: "Lifting point height",
  UnloadingPointHeight: "Unloading point height",
  MaximumHeight: "Maximum height",
  LiftingPointAmplitude: "Lifting point amplitude",
  UnloadingPointAmplitude: "Unloading point amplitude",
  RotationAngleLift: "Rotation angle of lifting point",
  RotationAngleUnloading: "Rotation angle of unloading point",
  AlarmLevel: "Alarm level",
  EarlyWarning: "Early warning",
  RecordingTime: "Recording time",
  AlarmReason: "Reason for alarm",
  TowerList: "List of tower cranes",
  ElevatorMonitoring: "Elevator monitoring",
  LoadInformationStatistics: "Load information statistics",
  StatisticsOfViolation: "Statistics of violation types",
  StatisticsOfElevator: "Statistics of elevator violation records",
  Week: "Week",
  RedLight: "Red light",
  Overload: "Overload",
  HistoricalOfElevator: "Historical data of elevator violation records",
  ListOfElevators: "List of elevators",
  Increases: "Increase",
  DeviceName: " Device name",
  EquipmentNumber: "Equipment number",
  CCFN: "Construction Commission Filing Number",
  Manufacturer: "Manufacturer",
  Photo: "Photo",
  SelectFile: "Select file",
  QualificationCertificate: "Qualification certificate",
  Cancellation: "Cancellation",
  Determine: "Determine",
  PleaseEnter: " Please enter",
  Edits: "Edit",
  SuccessfullySaved: "Successfully saved",
  SuccessfullyDel: "Successfully deleted",
  Fail: "Fail",
  FailSaved: "Save failed",
  AcquisitionFailed: "Acquisition failed",
  Prompt: "Prompt",
  deleteInfo:
    "Are you sure you want to delete this message?Please note that data cannot be restored after deletion.",
  Success: "Success",
  UploadSuccessful: "Upload successful",
  ImportSuccessful: "Import was successful",
  ImportFail: "Import failed",
  MaximumSpeedWarning: "Maximum speed warning",
  MaximumSpeedAlarm: "Maximum speed alarm",
  MaximumTiltWarning: "Maximum tilt warning",
  MaximumTiltAlarm: "Maximum tilt alarm",
  MaximumLoadWarning: "Maximum load warning",
  MaximumLoadAlarm: "Maximum load alarm",
  MaximumAltitudeWarning: "Maximum altitude warning",
  MaximumHeightAlarm: "Maximum height alarm",
  RealTimeScreen: "Real time screen",
  RealTimeElevator: "Real time data for elevator monitoring",
  Direction: "Direction",
  Speed: "Speed",
  XInclination: "X inclination",
  YInclination: "Y inclination",
  RunningState: "Running state",
  QueryDate: "Query date",
  WarningLevel: " Warning level",
  StartingTime: "Starting time",
  EndTime: "End time",
  StartingHeight: "Starting height",
  EndHeight: "End height",
  MovingDistance: "Moving distance",
  AverageSpeed: "Average speed",
  MaximumX: " Maximum inclination in the X-direction",
  MaximumY: "Maximum inclination in Y direction",
  ReasonViolation: "Reason for violation",
  // 项目首页
  ProjectInformation: "Introduction",
  Personalization: "Customized",
  Homepage: "Homepage",
  OneClickReporting: "Assistance",
  ProjectIntroduction: "Introduction",
  Video: "Video",
  recordVideo:
    "If you record a video and download it locally, please use the Hikvision player to play it! Click to download",
  VideoList: "Video list",
  OnLine: "On line",
  ManagementOfPresetPoints: "Management of preset points",
  Realtime: "Real time",
  Playback: "Playback",
  Devicename: "Device name",
  Cameraname: "Camera name",
  Placementlocation: "Placement location",
  State: "State",
  Isitenabled: "Is it enabled",
  Operate: "Operate",
  Hiddendanger: "Hidden danger",
  Time: "Time",
  Hiddendangername: "Hidden danger name",
  Hiddendangerlocation: "Hidden danger location",
  StartTime: "Start time",
  EndTimes: "End time",
  Hiddendangerimages: "Hidden danger images",
  state: "state",
  View: "View",
  Notmanuallyconfirmed: "Not manually confirmed",
  Projecttitle: "Project title",
  PROJECTNO: "Project NO",
  Projectlocation: "Project location",
  Detailedaddress: "Detailed address",
  Projectstatus: "Project status",
  Projectscale: "Project scale",
  Engineeringtype: "Engineering type",
  Setpresetpoints: "Set preset points",
  Presetpointname: "Preset point name",
  enterpreset: "Please enter the preset point name",
  deviceOffline:
    "The device is not online. Please check the device network or restart the device to connect to the fluorite cloud.",
  Adddeviceinformation: "Add device information",
  YingshiAPPKey: "Yingshi APPKey",
  YingshiSecret: "Yingshi Secret",
  Equipmentserialnumber: "Equipment serial number",
  Deviceverificationcode: "Device verification code",
  Offline: "Offline",
  Enable: "Enable",
  Disable: "Disable",
  deleteFile:
    "This operation will permanently delete the file. Do you want to continue?	",
  Cancelleddeletion: "Cancelled deletion",
  Modifyingdeviceinformation: "Modifying device information",
  CameraName: "Camera Name",
  customization: {
    officeArea: "office area",
    constructionArea: "construction area",
    livingArea: "living area",
    alarmsNumber: "Number of alarms",
    alarmWeight: "Weight alarm",
    alarmElectricity: "Current alarm",
    alarmX: "Tilt X alarm",
    alarmY: "Tilt Y alarm",
    others: "others",
    electrical: "electrical",
    HVAC: "HVAC",
    outWater: "Water s and d",
    structure: "structure",
    building: "building",
    drawing: "drawing",
    changeTalk: "Change negotiation",
    '钢筋/吨': "rebar/ton",
    '钢管/吨': "steelPipe/ton",
    '聚合物防水砂浆/立方米': 'Polymer waterproof mortar/m³',
    '混凝土/立方米': 'Concrete/m³',
    '加气块/立方米': 'Aerated block/m³',
    '模板/平方米': 'Template/m²',
    masonry: "Masonry and masonry mortar",
    concrete: "concrete",
    WaterproofMaterials: "Waterproof materials",
    ThermalInsulationMaterials: "Thermal insulation materials",
    ReinforcementAndJoints: "Reinforcement and joints",
    materialNumer: "Number of material inspections",
    witnessNumber: "Number of Witnesses Required",
    qualified: "qualified",
    unQuaDone: "Non conformity rectified",
    unQuaUn: "Unqualified will rectified",
    safetyTotal: "Total number of security checks",
    qualityTotal: "Total number of quality checks",
    week: "week",
    month: "month",
    year: "year",
    testTotal: "Total number of experiments",
    buildAndWater: "Building plumbing and heating engineering",
    buildElect: "Building electrical engineering",
    savePower: "Energy saving engineering",
    windAndAir: "Ventilation and Air Conditioning Engineering",
    commonTable: "Universal Tables",
    smartSmoke: "Total number of intelligent smoke detectors",
    normal: "normal",
    alarm: "alarm",
    fault: "fault",
    smartHatNum: "Number of smart safety helmets",
    onlineNum: "online num",
    unHatAlarm: "Hat removal alarm",
    fenceAlarm: "Fence alarm",
    temperatureAlarm: "temperature alarm",
    fallAlarm: "Fall alarm",
    rules: "Rules and regulations",
    constructCase: "Construction organization plan",
    techGive: "Technical briefing",
    constructSpecCase: "Special construction plan",
    anchor: "Anchor cable axial force",
    horazitalMove: "Horizontal displacement monitoring",
    arroundBuildTile: "Risk of inclination of surrounding buildings",
    totalPoint: "Total number of points",
    totalAlarm: "Total number of pre alarms/alarms",
    reSaveWeight: "Recycling quality",
    reSavePercent: "Recycling proportion",
    abandonedWood: "Abandoned wood",
    abandonedSavePlate: "Abandoned insulation board",
    abandonedBrick: "Abandoned bricks",
    abandonedConcrete: "Abandoned concrete blocks",
    total: "total",
    cumulative: "cumulative",
    xAlarm: "X tilt alarm",
    yAlarm: "Y tilt alarm",
    overWeightAlarm: "Overweight alarm",
    xeAlarm: "X tilt warning",
    yeAlarm: "Y tilt warning",
    overWeighteAlarm: "Overweight waring",
    liveAndWork: "Management of living and office areas",
    cli: "scaffold",
    templateSys: "Template support system",
    safeDefend: "safety protection ",
    tempElec: "Temporary electricity usage",
    towerAndweight: "Tower crane, lifting and hoisting",
    machineSafe: "Mechanical safety",
    cleanAct: "Clean action",
    envirProAct: "Environmental Improvement Action",
    propagateAcT: "Promotional actions",
    clearFour: 'Action to eliminate the "four pests"',
    constSafe: "Construction safety operation disclosure",
    safeTra: "Safety measures and job briefing",
    workTypeb: "Job safety briefing",
    conEleb: "Construction electricity safety briefing",
    conMac: "Construction machinery operation briefing",
    poleInclination: "Pole inclination",
    poleForce: "Vertical pole axial force",
    templateDown: "Template settlement",
    horizontalMove: "Horizontal displacement",
    sideSite: "Number of side stations",
    problemNum: "Number of issues",
    totalNum: "Total number",
    electNet: "Electric green net dustproof canopy",
    soildPen: "Dust isolation shed",
    hazardIdentify: "Hazard identification",
    faceIdentify: "Facial recognition",
    remoteTeach: "Remote technical guidance",
    hiddRec: "Hidden engineering records",
    keyRec: "Key process records",
    realTem: "temperature",
    realHumidity: "humidity",
    onecPass: "One-time pass rate",
    importDanger: "Major hidden dangers",
    smallDanger: "General hazards",
    // Safety acceptance data items
    livingAreaManagement: "Living area management",
    safetyAcceptanceTotal: "Total",
    // Safety disclosure data items
    constructionWorkDisclosure: "Construction work disclosure",
    safetyMeasuresDisclosure: "Safety measures disclosure",
    workTypeDisclosure: "Work type disclosure",
    constructionElectricityDisclosure: "Construction electricity disclosure",
    mechanicalOperationDisclosure: "Mechanical operation disclosure",
    safetyDisclosureTotal: "Total",
    // Elevator monitoring data items
    elevatorLoad: "Load",
    elevatorIncline: "Incline",
    elevatorSpeed: "Speed",
    elevatorHeight: "Height",
    elevatorMonitoringTotal: "Total",
    // High formwork monitoring total
    highFormworkMonitoringTotal: "Total",
    // Patriotic health movement total
    patrioticHealthTotal: "Total number",
    // Tower crane monitoring
    towerCraneTotal: "Total",
    towerCraneWarning: "Alarm",
    towerCraneEarly: "Warning",
    towerCraneLoad: "Load",
    towerCraneWindSpeed: "Wind speed",
    towerCraneMoment: "Moment",
    towerCraneIncline: "Incline",
    towerCraneAntiCollision: "Anti-collision",

    // Dust isolation
    dustIsolationTotal: "Total",
    dustIsolationShed: "Dust isolation shed",
    dustIsolationCanopy: "Electric dustproof canopy",

    // Project carbon management
    projectCarbonTotal: "Total",
    projectCarbonArtificial: "Artificial carbon emission",
    projectCarbonMaterial: "Material carbon emission",
    projectCarbonMechanical: "Mechanical carbon emission",
    projectCarbonWaste: "Construction waste emission",
    projectCarbonRecycle: "Recycling emission reduction",
    // Smart glasses
    smartGlassesTotal: "Total",
    // Gantry crane monitoring
    gantryCraneTotal: "Total",
    // Unload platform monitoring
    unloadPlatformTotal: "Total warnings",
    // Witness sampling
    witnessSampTotal: "Total warnings",
    // Progress management
    progressManageTotal: "Total tasks",
    // Security check
    securityCheckTotal: "Total security checks",
    // Quality test
    qualityTestTotal: "Total quality checks",
    // Smart smoke
    smartSmokeTotal: "Total smoke detectors",
    // Field test
    fieldTestTotal: "Total tests",
    // Construction equipment monitoring
    constructionEquipmentTotal: "Total equipment",
  },
};
