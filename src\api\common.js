/*
 * @Description:
 * @Author:
 * @Date: 2022-06-30 17:21:48
 * @LastEditTime: 2025-07-17 16:09:33
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Usage:
 */
import Axios from "@/router/axios";
/**
 * @description: 根据公司id获取公司信息
 * @param {*} query
 * @return {*}
 */
export function getCompanyInfo(query) {
  return Axios({
    url: `/company/company-info/${query.companyId}`,
    method: "get"
  });
}

/**
 * @description: 根据用户ID获取登录用户信息
 * @param {*} userId
 * @return {*}
 */
export function getUserInfo(query) {
  return Axios({
    url: `/login/user-info/${query.userId}`,
    method: "get"
  });
}
// 公司级人员登录，按项目筛选项目列表
export function getProList(query) {
  return Axios({
    url: `/company/tree-list-company-and-project`,
    method: "post",
    data: query
  });
}
// 公司级人员登录，按地区筛选项目列表
export function getAreaList(query) {
  return Axios({
    url: `/company/tree-list-company-of-area`,
    method: "post",
    data: query
  });
}
//公司级别用户登录,切换公司、子公司、项目重写cookies(公司级人员登录首页，下拉框切换公司和项目)
export function getUserCookie(data) {
  return Axios({
    url: `/login/user-re-login-for-company-switch/${data.companyid}/${data.projectid}/${data.pk}`,
    method: "post",
    data: data
  });
}


// 获取监控图片详情接口
export function getMonitorImg(projectid) {
  return Axios({
    url: `/project-manage/project-image-by-project-id/${projectid}`,
    method: "get",
  });
}

// 根据图片id获取所有识别结果接口
export function getMonitorResult(imageId) {
  return Axios({
    url: `/project-manage/project-image-identify-by-image-id/${imageId}`,
    method: "get",
  });
}

// 获取首页所有模块信息
export function getAllModule(projectId, isAll) {
  return Axios({
    url: `/modul/all-module/${projectId}`,
    method: "get",
    params: {
      isAll
    },
  });
}
