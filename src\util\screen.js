/*
 * @Description:
 * @Author:
 * @Date: 2022-09-08 09:19:28
 * @LastEditTime: 2022-09-08 14:20:12
 * @LastEditors: lihanbing
 * @Usage:
 */
export function getScreen() {
  var width = document.body.clientWidth;
  if (width >= 1200) {
    return 3; // 大屏幕
  } else if (width >= 992) {
    return 2; // 中屏幕
  } else if (width >= 768) {
    return 1; // 小屏幕
  } else {
    return 0; // 超小屏幕
  }
}
// 得到不同屏幕下定义的宽高
export function getScreenWidth(val) {
  let width = document.documentElement.clientWidth;
  let rate = (val / 1920).toFixed(2);
  return (rate * width).toFixed(0);
}
