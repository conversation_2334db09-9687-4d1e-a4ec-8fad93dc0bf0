<!--
 * @Description: 环境监测
 * @Author:
 *  
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="EnviromentRef"
      >

        <div class="circle">
          <p>风速</p>
          <p class="num"><span>16</span> m/s</p>
        </div>
        <div class="circle">
          <p>PM2.5</p>
          <p class="num"><span>16</span> μg/m³</p>
        </div>
        <div class="circle">
          <p>噪声</p>
          <p class="num"><span>16</span> db</p>
        </div>
        <div class="circle">
          <p>温度</p>
          <p class="num"><span>25</span> ℃</p>
        </div>
        <div class="circle">
          <p>湿度</p>
          <p class="num"><span>16</span> %RH</p>
        </div>
        <div class="circle">
          <p>PM10</p>
          <p class="num"><span>54</span> μg/m³</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawPie } from "@/components/constructionRecord/Echarts/echartsOne.js";
// import { getEnviroment } from "@/api/echrtsApi";
export default {
  components: {},
  name: "Enviroment",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,

      totalCount: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
  },
  methods: {
    getBarData() {
      // getEnviroment()
      //   .then((res) => {
      //     const {
      //       statusCode,
      //       data: { dataList, totalCount },
      //     } = res.data;
      //     if (statusCode == 200) {
      //       this.setEcharts(dataList);
      //       this.totalCount = totalCount;
      //     }
      //   })
      //   .catch(() => { });
    },
    setEcharts(val) {

    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
  .circle {
    border-radius: 50%;
    width: 70px;
    height: 70px;
    position: absolute;
    opacity: 0.8;
    padding-top: 12px;
    box-sizing: border-box;
    p {
      text-align: center;
      color: #fff;
      font-size: 12px;
      position: relative;
      z-index: 9;
      line-height: 20px;
      white-space: nowrap;
      span {
        font-size: 18px;
      }
    }
    &:nth-child(1) {
      left: 70px;
      top: 50px;
      width: 70px;
      height: 70px;
      background: radial-gradient(
        circle at 50% 5%,
        #6ecbf5 0%,
        #189ad3 40%,
        #107dac 70%,
        #0a5a7a 100%
      );
      box-shadow: 0 0 1px 7px #234987, 0 0 0 8px #fff, 0 0 0 14px #234987;
      position: absolute;
      &::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        left: 0;
        top: 0;
        background: radial-gradient(
          circle at 50% 95%,
          #b98eff 0%,
          #8c52ff 40%,
          #6a30d9 70%,
          #4a1d9c 100%
        );
        opacity: 0.5;
      }
    }
    &:nth-child(2) {
      top: 20px;
      left: 170px;
      width: 55px;
      height: 55px;

      background: radial-gradient(
        circle at 50% -3%,
        #8d9290 0%,
        #345d51 40%,
        #44524e 70%,
        #1b4b3d 100%
      );
      box-shadow: 0 0 1px 7px #6e4343, 0 0 0 8px #fff, 0 0 0 14px #6e4343;
      position: absolute;
      &::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        left: 0;
        top: 0;
        background: radial-gradient(
          circle at 50% 114%,
          #dc5560 0%,
          #c45757 40%,
          #c63030 70%,
          #ca2a2a 100%
        );
        opacity: 0.5;
      }
    }
    &:nth-child(3) {
      top: 40px;
      left: 260px;
      width: 65px;
      height: 65px;

      background: radial-gradient(
        circle at 50% -3%,
        #8d9290 0%,
        #383309 40%,
        #6e6939 70%,
        #958329 100%
      );
      box-shadow: 0 0 1px 7px #433e2f, 0 0 0 8px #fff, 0 0 0 14px #433e2f;
      position: absolute;
      &::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        left: 0;
        top: 0;
        background: radial-gradient(
          circle at 50% 114%,
          #b7ba88 0%,
          #a6a91d 40%,
          #f2dc56 70%,
          #c8c7a4 100%
        );
        opacity: 0.5;
      }
    }
    // 绿色4
    &:nth-child(4) {
      top: 140px;
      left: 100px;
      width: 55px;
      height: 55px;

      background: radial-gradient(
        circle at 50% -3%,
        #87f9cc 0%,
        #2b7427 40%,
        #0b4c0e 70%,
        #206933 100%
      );
      box-shadow: 0 0 1px 7px #205752, 0 0 0 8px #fff, 0 0 0 12px #205752;
      position: absolute;
      &::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        left: 0;
        top: 0;
        background: radial-gradient(
          circle at 50% 114%,
          #7fd37d 0%,
          #287f2a 40%,
          #285728 70%,
          #41863c 100%
        );
        opacity: 0.5;
      }
    }
    &:nth-child(5) {
      top: 110px;
      left: 180px;
      width: 63px;
      height: 63px;

      background: radial-gradient(
        circle at 50% -3%,
        #35b1b4 0%,
        #2f4548 40%,
        #20535a 70%,
        #1c5e5a 100%
      );
      box-shadow: 0 0 1px 7px #216363, 0 0 0 8px #fff, 0 0 0 12px #216363;
      position: absolute;
      &::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        left: 0;
        top: 0;
        background: radial-gradient(
          circle at 50% 114%,
          #5df9e9 0%,
          #309499 40%,
          #00bcd4 70%,
          #bbbbbb 100%
        );
        opacity: 0.5;
      }
    }
    &:nth-child(6) {
      top: 140px;
      left: 270px;
      width: 63px;
      height: 63px;

      background: radial-gradient(
        circle at 50% -3%,
        #651191 0%,
        #3f004a 40%,
        #391e3d 70%,
        #461e46 100%
      );
      box-shadow: 0 0 1px 7px #472763, 0 0 0 8px #fff, 0 0 0 12px #472763;
      position: absolute;
      &::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        left: 0;
        top: 0;
        background: radial-gradient(
          circle at 50% 114%,
          #bf43e2 0%,
          #65189b 40%,
          #802088 70%,
          #9b23d9 100%
        );
        opacity: 0.5;
      }
    }
  }
}
.ircle {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  box-shadow: 0 0 1px 7px #28a745, 0 0 0 9px #fff, 0 0 0 14px #28a712;
}
</style>
