<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div
      class="text"
      v-if="isShowTitle"
    >{{ moduleName }}</div>
    <div class="areaContent minitorContent">
      <div
        class="box"
        ref="MaterialManageRef"
      >
        <div
          id="MaterialManageChart"
          :style="{ height: barHeight + 'px', width: '100%' }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import { drawHorizontalBarChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getMaterial } from "@/api/echrtsApi";

export default {
  name: "MaterialManage",
  props: {
    moduleName: String,
    isShowTitle: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      lineParams: {
        dom: "MaterialManageChart",
        xAxisData: [],
        seriesData: [],
        boundaryGap: true,
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        xAxisType: "value",
        yAxisType: "category",
        unit: "",
        fontSize: this.getDynamicFontSize(),
        labelFontSize: this.getDynamicFontSize(),
        barWidth: this.getDynamicBarWidth(),
        grid: {
          borderWidth: 0,
          top: "10%",
          left: "2%",
          right: "24%",
          bottom: "3%",
        },
        axisLabelFormatter: (value) => {
          const isSmallScreen = window.innerWidth < 768;
          let res = value;
          if (isSmallScreen && res.length > 3) {
            res = res.substring(0, 3) + "..";
          } else if (res.length > 6) {
            res = res.substring(0, 6) + "..";
          }
          return res;
        },
        tooltipFormatter: (val) => {
          return `${val[0].name}<br/>${val[0].marker}${this.$t("quantity")}: 
            <b>${val[0].data}</b>`;
        },
      },
      resizeTimer: null,
    };
  },
  created() {
    this.projectId = getStore({ name: "projectId" });
    this.companyId = getStore({ name: "companyId" });
    this.getData();
  },
  mounted() {
    this.setEchartsSize();
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    if (this.resizeTimer) clearTimeout(this.resizeTimer);
  },
  methods: {
    handleResize() {
      if (this.resizeTimer) clearTimeout(this.resizeTimer);
      this.resizeTimer = setTimeout(() => {
        this.setEchartsSize();
        // 更新动态参数
        this.lineParams.fontSize = this.getDynamicFontSize();
        this.lineParams.labelFontSize = this.getDynamicFontSize();
        this.lineParams.barWidth = this.getDynamicBarWidth();
        // 重新渲染图表
        if (this.lineParams.xAxisData.length > 0) {
          drawHorizontalBarChart(this.lineParams);
        }
      }, 300);
    },
    setEchartsSize() {
      const container = this.$refs.MaterialManageRef;
      if (!container) return;

      // 根据屏幕宽度动态调整高度
      const isSmallScreen = window.innerWidth < 768;
      this.barHeight = isSmallScreen
        ? Math.max(container.offsetHeight * 1.5, 250)
        : container.offsetHeight;
    },
    getDynamicFontSize() {
      const width = window.innerWidth;
      if (width < 480) return 10;
      if (width < 768) return 12;
      return 14;
    },
    getDynamicBarWidth() {
      const width = window.innerWidth;
      if (width < 480) return "8px";
      if (width < 768) return "10px";
      return "12px";
    },
    getData() {
      getMaterial()
        .then((res) => {
          const {
            data: { counts, names },
            statusCode,
          } = res.data;
          if (statusCode === 200) {
            let lengthNumber = Math.ceil(names.length / 5);
            let index = 1;

            const updateChart = () => {
              if (index > lengthNumber) index = 1;
              this.activeData(
                names.slice((index - 1) * 5, index * 5),
                counts.slice((index - 1) * 5, index * 5)
              );
              index++;
            };

            updateChart();
            this.intervalId = setInterval(updateChart, 5000);
          }
        })
        .catch(() => { });
    },
    activeData(names, counts) {
      const seriesData = [
        {
          type: "bar",
          data: counts,
          itemStyle: {
            normal: {
              color: "#4e81e4", // 保持与智能测量一致的颜色
            },
          },
          label: {
            normal: {
              show: true,
              position: "right",
              textStyle: {
                fontSize: this.getDynamicFontSize(),
              },
            },
          },
        },
      ];

      this.lineParams.xAxisData = names;
      this.lineParams.seriesData = seriesData;
      drawHorizontalBarChart(this.lineParams);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  box-sizing: border-box;
}

@media screen and (max-width: 768px) {
  .box {
    height: 300px;
  }

  #MaterialManageChart {
    margin-top: 15px;
  }
}

@media screen and (max-width: 480px) {
  .box {
    height: 280px;
  }
}
</style>