<!-- 环境监测 -->
<template>
  <div class="box">
    <div class="leftDraw" ref="drawRef">
      <div class="leftImg">
        <img src="@/assets/xcimg.png" />
        现场
      </div>
      <div
        id="gauge"
        :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
      ></div>
      <div class="CompanyStyle">PM2.5 (ug/m<sup>3</sup>)</div>
    </div>
    <div class="rightDraw">
      <div class="rightImg">
        <img src="@/assets/gkdimg.png" />
        国控点
      </div>
      <div
        id="gauge2"
        :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
      ></div>
      <div class="CompanyStyle">PM10 (ug/m<sup>3</sup>)</div>
    </div>
  </div>
</template>

<script>
import { drawRing, drawRing2, drawGauge } from '@/util/echarts';
import { getPieList, getBarList, getCityData } from '@/api/constructionRecord';
import { getPieHollow } from '@/api/chart';
import { getScreenWidth } from '@/util/screen';
export default {
  components: {},
  name: 'environmentalMonitoring',
  data() {
    return {
      barWidth: null,
      barHeight: null,
      ringParams: {
        data: [
          { value: 1048, name: '1号' },
          { value: 735, name: '2号' },
          { value: 580, name: '3号' },
        ],
        dom: 'gauge',
      },
      ringParams2: {
        data: [
          { value: 1048, name: '1号' },
          { value: 735, name: '2号' },
          { value: 580, name: '3号' },
        ],
        dom: 'gauge2',
      },
    };
  },
  created() {
    this.projectId = getStore({ name: 'projectId' });
    this.getPieHollow();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener('resize', function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.drawRef.offsetWidth;
      // console.log(this.$refs.drawRef.offsetWidth);
      this.barHeight = this.$refs.drawRef.offsetHeight * 0.7;
      // this.barWidth = getScreenWidth(150);
      // this.barHeight = getScreenWidth(140);
    },
    getPieHollow() {
      getPieHollow(this.projectId).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          this.ringParams.data[0].value = result.data.pM25
            ? result.data.pM25
            : 0;
          this.ringParams2.data[0].value = result.data.pM10
            ? result.data.pM10
            : 0;
          drawGauge(this.ringParams);
          drawGauge(this.ringParams2);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  height: 90%;
  box-sizing: border-box;
  padding: 0 20px;
  // height: 246px;
  .leftDraw {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 50%!important;
    margin: 0!important;

    .leftImg {
      // margin-top: 10px;
      img {
        height: 14px;
      }
      line-height: 24px;
      font-size: 15px;
    }
  }
  .rightDraw {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 50%!important;
    margin: 0!important;
    .rightImg {
      // margin-top: 10px;
      img {
        height: 14px;
      }
      line-height: 24px;
      font-size: 15px;
    }
  }
  .CompanyStyle {
    font-size: 16px;
  }
}
</style>
