<!--
 * @Description: 方案管理
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2023-02-02 09:11:05
 * @LastEditors: lihanbing
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="SchemeManageRef"
      >
        <div
          id="SchemeManageChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawBarLineTotal } from "@/components/constructionRecord/Echarts/echartsOne.js";
import { getConstructionSch } from "@/api/echrtsApi";
export default {
  components: {},
  name: "SchemeManage",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      barParams: {
        dom: "SchemeManageChart",
        xAxisData: [],
        seriesData: [],
        boundaryGap: true,
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        yminInterval: 1,
        unit: "",
        axisLabelFormatter: function (value) {
          var res = value;
          if (res.length > 3) {
            res = res.substring(0, 3) + "..";
          }
          return res;
        },
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.SchemeManageRef.offsetWidth - 40;
      this.barHeight = this.$refs.SchemeManageRef.offsetHeight;
    },
    getBarData() {
      getConstructionSch()
        .then((res) => {
          const {
            data: { constructSchNames, counts },
            statusCode,
          } = res.data;
          if (statusCode == 200) {
            let seriesData = [
              {
                // name: '报警次数',
                data: counts,
                type: "bar",
                itemStyle: {
                  normal: {
                    color: "#4e81e4",
                  },
                },
                label: {
                  normal: {
                    show: true,
                    position: "top",
                    textStyle: {
                      fontSize: 10,
                    },
                  },
                },
              },
            ];
            this.barParams.xAxisData = constructSchNames.map((item) => {
              switch (item) {
                case "规章制度":
                  item = this.$t("customization.rules");
                  break;
                case "施工组织方案":
                  item = this.$t("customization.constructCase");
                  break;
                case "技术交底":
                  item = this.$t("customization.techGive");
                  break;
                case "施工专项方案":
                  item = this.$t("customization.constructSpecCase");
                  break;
              }
              return item;
            });
            this.barParams.seriesData = seriesData;
            drawBarLineTotal(this.barParams);
          }
        })
        .catch(() => { });
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
}
</style>
