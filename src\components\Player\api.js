import axios from "axios";

/**
 * 视频操作，上下左右远近
 * @param {*} type
 * @param {*} data
 * @returns Promise
 * @see {@link https://open.ys7.com/help/59#device_ptz-api1}
 */
export async function videoControlPTZ(type, data) {
  data = { ...data };
  if (type === "start") data.speed = 1;

  // return axios({
  //   url: `https://open.ys7.com/api/lapp/device/ptz/${type}`,
  //   method: "post",
  //   data: {
  //     ...data,
  //   },
  //   withCredentials: false,
  //   headers: {
  //     "Content-Type": "multipart/form-data",
  //   },
  // });

  data = {
    accessToken:
      "at.8i59l2jqaiitp3mudwnlbbqlbyd1ofw5-8acfj4y0uv-1je9kyv-btek7jg5v",
    deviceSerial: "J81220456",
    speed: 1,
    direction: 3,
    channelNo: 1,
  };

  var formData = new FormData();

  for (var k in data) {
    formData.append(k, data[k]);
  }

  const response = await fetch(
    `https://open.ys7.com/api/lapp/device/ptz/${type}`,
    {
      method: "POST", // *GET, POST, PUT, DELETE, etc.
      headers: {
        // 'Content-Type': 'application/json'
        "Content-Type": "multipart/form-data",
      },
      // redirect: 'follow', // manual, *follow, error
      // referrerPolicy: 'no-referrer', // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
      body: formData, // body data type must match "Content-Type" header
    }
  );

  return response.json();
}

/**
 * 视频操作，跳转到预设点
 * @param {*} type
 * @param {*} data
 * @returns Promise
 * @see {@link https://open.ys7.com/help/59#device_ptz-api5}
 */
export function videoControlMove(data) {
  return axios({
    url: `https://open.ys7.com/api/lapp/device/preset/move`,
    method: "post",
    data: {
      ...data,
    },
    withCredentials: false,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

/**
 * 获取萤石播放地址
 * @param {*} type
 * @param {*} data
 * @returns Promise
 * @see {@link https://open.ys7.com/help/82}
 */
export function getYSPlayUri(data) {
  return axios({
    url: `https://open.ys7.com/api/lapp/v2/live/address/get`,
    method: "post",
    data: {
      ...data,
    },
    withCredentials: false,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
