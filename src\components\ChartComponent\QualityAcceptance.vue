<!--
 * @Description: 质量验收
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-25 17:48:23
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="QualityAcceptanceRef"
      >
        <div
          id="QualityAcceptanceChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>

import { drawCustomBarSingle } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getProjectQuality } from "@/api/echrtsApi";
export default {
  components: {},
  name: "QualityAcceptance",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      lineParams: {
        dom: "QualityAcceptanceChart",
        xAxisData: [],
        seriesData: [],
        isMoreLine: true,
        boundaryGap: true,
        unit: "%",
        tooltipFormatter: (val) => {
          let msg = `${val[0].axisValue}`;
          if (val.length) {
            val.forEach((ele) => {
              msg += `<br/>${ele.marker} ${this.$t("customization.onecPass")}
              <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
              <b>${ele.data}</b>%`;
            });
          }
          return msg;
        },
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.QualityAcceptanceRef.offsetWidth;
      this.barHeight = this.$refs.QualityAcceptanceRef.offsetHeight;
    },
    getData() {
      getProjectQuality(this.projectId)
        .then((res) => {
          let result = res.data;
          const { data, statusCode } = result;
          if (statusCode == 200) {
            let xInfor = [];
            let yInfor = [];
            if (data.length > 0) {
              data.forEach((items) => {
                xInfor.push(items.month);
                yInfor.push(items.monthPercent);
              });
            }
            let seriesData = [
              {
                type: "bar",
                data: yInfor,
                lineStyle: {
                  normal: {
                    color: "#4e81e4",
                  },
                },
                itemStyle: {
                  color: "#4e81e4",
                  borderColor: "#4e81e4",
                },
              },
            ];
            this.lineParams.xAxisData = xInfor;
            this.lineParams.seriesData = seriesData;
            drawCustomBarSingle(this.lineParams);
          }
        })
        .catch(() => { });
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
