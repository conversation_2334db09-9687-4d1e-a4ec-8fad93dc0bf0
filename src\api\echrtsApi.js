/*
 * @Description: 图表的所有接口
 * @Author:
 * @Date: 2022-12-27 10:42:11
 * @LastEditTime: 2025-08-01 14:18:50
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Usage:
 */

import Axios from "@/router/axios";
// 获取节电管理的接口
export function getEnergyData(query) {
  return Axios({
    url: `/api/energy-equipment-module/area-statistics-for-day/${query}`,
    method: "get"
  });
}

// 获取节水管理的接口
export function getPowerData(query) {
  return Axios({
    url: `/api/hydro-power-equipment-module/area-statistics-for-day/${query}`,
    method: "get"
  });
}

// 获取吊篮监测的接口
export function getBaskData() {
  return Axios({
    url: `/api/basket-monitoring-module/bask-bar-chart-list`,
    method: "post"
  });
}

// 获取项目碳管理的接口（北京专版）
export function getCarbonmanagement() {
  return Axios({
    url: `/bjzb/Carbonmanagement/StatisticsCarbon`,
    method: "get"
  });
}

// 获取智能巡检的接口
export function getSafetyInspection() {
  return Axios({
    url: `/api/safety-inspection-module/bar-chart-by-project-id`,
    method: "get"
  });
}

// 获取智能巡检的接口
export function getDrawing() {
  return Axios({
    url: `/api/jdmt-module/drawing-statics-infos`,
    method: "post"
  });
}

//获取图纸管理接口
export function getDrawingInfo() {
  return Axios({
    url: `/api/drawing-management/drawing-statics-infos`,
    method: "get"
  });
}

// 获取安全教育的接口
export function getSafetyEducation() {
  return Axios({
    url: `/api/safety-education-module/safety-education-statistic-info`,
    method: "post"
  });
}

// 获取龙门吊监测的接口
export function getGantryCrane() {
  return Axios({
    url: `/api/gantry-crane-module/gantry-crane-run-status`,
    method: "get"
  });
}

// 获取劳务管理的接口
export function getLabourServices(data) {
  return Axios({
    url: `/api/labor-m-module/piechattana-lysis-from-attendance-record-today`,
    method: "post",
    data
  });
}

// 获取见证取样的接口
export function getWitnessSamp() {
  return Axios({
    url: `/api/witness-sampling-module/statistic-info`,
    method: "get"
  });
}

// 获取材料管理的接口
export function getMaterial() {
  return Axios({
    url: `/api/material-module/material-bar-chart-by-project-id`,
    method: "get"
  });
}

// 获取材料管理的接口
export function getMaterialCheck() {
  return Axios({
    url: `/api/material-check-module/material-check-bar-chart-by-project-id`,
    method: "get"
  });
}

// 获取智能测量的接口
export function getMeasure() {
  return Axios({
    url: `/api/intelligent-measure-module/equip-avg-pass-rate-statistics`,
    method: "get"
  });
}

// 获取智能测量的接口
export function getPowerBox(val) {
  return Axios({
    url: `/api/power-box-module/alert-statistics-info/${val}`,
    method: "get"
  });
}

// 获取建筑垃圾的接口(北京专版)
export function getBuildWaster() {
  return Axios({
    url: `/bjzb/BuildWaste/StatisticBuildWaster`,
    method: "get"
  });
}

// 获取车辆冲洗的接口
export function getCarWash() {
  return Axios({
    url: `/api/car-wash-module/car-wash-statistic-info`,
    method: "post"
  });
}

// 获取进度管理的接口
export function getProgressManage() {
  return Axios({
    url: `/api/construction-module/progress-statistic-info`,
    method: "post"
  });
}

// 获取智能烟感的接口
export function getSmartSmoke() {
  return Axios({
    url: `/api/iintelligent-module/getsm-equ-state-pie`,
    method: "get"
  });
}

// 获取安全检查的接口
export function getSecurityCheck(val) {
  return Axios({
    url: `/api/security-check-module/everyday-check-status-pie/${val}`,
    method: "get"
  });
}

// 获取质量检查的接口
export function getQualityTest(val) {
  return Axios({
    url: `/api/quality-check-module/quality-check-pie-chart/${val}`,
    method: "post"
  });
}

// 获取现场试验的接口
export function getFieldTest(val) {
  return Axios({
    url: `/api/fileldtest-module/file-test-pie-chart/${val}`,
    method: "post"
  });
}

// 获取智能安全帽的接口
export function getSmartHelmet() {
  return Axios({
    url: `/api/alert-record-module/statistic-smart-helmet`,
    method: "get"
  });
}

// 获取方案管理的接口
export function getConstructionSch() {
  return Axios({
    url: `/api/cosm-module/construction-sch-statistics`,
    method: "get"
  });
}

// 获取基坑监测的接口
export function getDeepExcavation() {
  return Axios({
    url: `/api/deep-excavation-module/deep-bar-chart-data-new`,
    method: "post"
  });
}

// 获取标养室的接口
export function getStandardRoom() {
  return Axios({
    url: `/api/room-module/record-info-by-prj`,
    method: "get"
  });
}

// 获取监理旁站的接口
export function getSiteSupervision() {
  return Axios({
    url: `/api/supervision-side-module/supervision-bar-chart-list`,
    method: "post"
  });
}

// 获取塔吊监测的接口
export function getTowerCrane() {
  return Axios({
    url: `/api/tower-crane-module/equipment-run-log-get-circular`,
    method: "post"
  });
}

// 获取卸料平台的接口
export function getUnloadPlatform() {
  return Axios({
    url: `/api/discharging-platform-module/dis-charg-plantform-statistic-info`,
    method: "post"
  });
}

// 获取升降机监测的接口
export function getElevatorSurvey(val) {
  return Axios({
    url: `/api/lift-module/monitor-point-alarm-statistics/${val}`,
    method: "get"
  });
}

// 获取升降机监测的接口
export function getHighFormwork(val) {
  return Axios({
    url: `/api/high-modulus-module/monitor-point-alarm-statistics/${val}`,
    method: "get"
  });
}

// 获取安全验收的接口
export function getSafetyAcceptance() {
  return Axios({
    url: `/api/safety-accept-module/security-itme-pie`,
    method: "get"
  });
}

// 获取安全交底的接口
export function getSafetyDisclosure() {
  return Axios({
    url: `/api/sfdm-module/safety-dis-type-pie`,
    method: "get"
  });
}

// 获取爱国卫生的接口(北京专版)
export function getPatrioticHealth() {
  return Axios({
    url: `/bjzb/Patriotic/StatisticPatriotic`,
    method: "get"
  });
}

// 获取防尘隔离的接口(北京专版)
export function getDustproofIsolation() {
  return Axios({
    url: `/bjzb/DustIsolation/StatisticDustIsolation`,
    method: "get"
  });
}

// 获取自动喷淋的接口(北京专版)
export function getAutomaticSpray(val) {
  return Axios({
    url: `/bjzb/Spray/StatisticSpray?userId=${val}`,
    method: "get"
  });
}

// 获取车辆管理的接口
export function getVehicleManage(val) {
  return Axios({
    url: `/api/parking-management-module/default-statistics/${val}`,
    method: "get"
  });
}

// 获取智能眼镜的接口
export function getSmartGlasses(data) {
  return Axios({
    url: `/other-operate/project-glass-pie`,
    method: "post",
    data
  });
}

//获取token
export function getToken(data) {
  return Axios({
    url: `/company/gettoken/${data}`,
    method: "post",
    data
  });
}

//获取工程资料的接口
export function getEngineering(data) {
  return Axios({
    url: `/gczl/project-manage/get-project-projectid-zl`,
    method: "post",
    data
  });
}

//获取质量验收一次通过率的接口
export function getProjectQuality(val) {
  return Axios({
    url: `/api/quality-accept-module/project-quality/${val}`,
    method: "get"
  });
}

//获取风险的接口
export function getRiskData(data) {
  return Axios({
    url: `/api/risk-control-module/default-risk-change-data`,
    method: "post",
    data
  });
}

//获取隐患的接口
export function getHazardData(data) {
  return Axios({
    url: `/api/security-check-module/default-hazard-change-data`,
    method: "post",
    data
  });
}

//高边坡-施工设备监测
export function equipmentStatics() {
  return Axios({
    url: `/api/construction-equipment-module/equipment-index-statics-pie`,
    method: "get"
  });
}
//高边坡-设备报警数量统计
export function deviceRecord() {
  return Axios({
    url: `/api/high-slope-module/index-bar-of-device-record`,
    method: "get"
  });
}

//高边坡-水雨情监测
export function rainwaterAnalysis(data) {
  return Axios({
    url: `/api/rainwater-module/index-rainwater-analysis`,
    method: "post",
    data
  });
}
//水雨情设备列表
export function deviceList() {
  return Axios({
    url: `/api/rainwater-module/rainwater-device-list`,
    method: "get"
  });
}
