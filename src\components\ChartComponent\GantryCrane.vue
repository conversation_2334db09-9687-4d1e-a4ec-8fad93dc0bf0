<!--
 * @Description: 龙门吊监测
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-08-01 16:03:24
 * @LastEditors: dong<PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div class="box" ref="GantryCranebonRef">
        <div
          id="GantryCranebonChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>

import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getGantryCrane } from "@/api/echrtsApi";
export default {
  components: {},
  name: "GantryCranebon",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "GantryCranebonChart",
        data: [],
        subtext:this.$t("customization.gantryCraneTotal"),
        seriesCenter: ["25%", "58%"],
        nameTitle: null,
        seriesLabel: false,
        costomLegendFormatter:function(name){
          return name;
        },
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.GantryCranebonRef.offsetWidth;
      this.barHeight = this.$refs.GantryCranebonRef.offsetHeight;
    },
    getBarData() {
      getGantryCrane()
        .then((res) => {
          const { statusCode, data } = res.data;
          if (statusCode == 200) {
            this.pieParams.data = data.map((item) => {
              switch (item.name) {
                case "重量报警":
                  item.name = this.$t("customization.alarmWeight");
                  break;
                case "电流报警":
                  item.name = this.$t("customization.alarmElectricity");
                  break;
                case "倾斜X报警":
                  item.name = this.$t("customization.alarmX");
                  break;
                case "倾斜Y报警":
                  item.name = this.$t("customization.alarmY");
                  break;
              }
              return item;
            });
            drawAnnularChart(this.pieParams);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
