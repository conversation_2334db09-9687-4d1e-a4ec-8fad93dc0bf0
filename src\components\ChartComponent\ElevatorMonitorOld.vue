<!--
 * @Description: 吊篮监测
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2023-01-30 16:36:41
 * @LastEditors: lihanbing
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div class="box" ref="ElevatorMonitorRef">
        <div
          id="ElevatorMonitorChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawBarLineTotal } from "@/components/constructionRecord/Echarts/echartsOne.js";
import { getBaskData } from "@/api/echrtsApi";
export default {
  components: {},
  name: "ElevatorMonitor",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      barParams: {
        dom: "ElevatorMonitorChart",
        xAxisData: [],
        seriesData: [],
        boundaryGap: true,
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        yminInterval: 1,
        axisLabelFormatter: function (value) {
          var res = value;
          if (res.length > 3) {
            res = res.substring(0, 3) + "..";
          }
          return res;
        },
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    })|| getUrlParams().projectId;
    this.companyId = getStore({
      name: "companyId",
    })|| getUrlParams().companyId;
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.ElevatorMonitorRef.offsetWidth - 40;
      this.barHeight = this.$refs.ElevatorMonitorRef.offsetHeight;
    },
    getBarData() {
      getBaskData()
        .then((res) => {
          const { data, statusCode } = res.data;
          if (statusCode == 200) {
            let seriesData = [
              {
                name: this.$t("customization.alarmsNumber"),
                data: data.data,
                type: "bar",
                itemStyle: {
                  normal: {
                    color: "#ff4d4f",
                  },
                },
              },
            ];
            this.barParams.xAxisData = data.name.map((item) => {
              let target = "";
              switch (item) {
                case "重量报警":
                  target = this.$t("customization.alarmWeight");
                  break;
                case "电流报警":
                  target = this.$t("customization.alarmElectricity");
                  break;
                case "倾斜X报警":
                  target = this.$t("customization.alarmX");
                  break;
                case "倾斜Y报警":
                  target = this.$t("customization.alarmY");
              }
              return target;
            });
            this.barParams.seriesData = seriesData;
            drawBarLineTotal(this.barParams);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
}
</style>
