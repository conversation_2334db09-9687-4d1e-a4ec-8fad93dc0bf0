<!--
 * @Description: 安全交底
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-25 14:59:45
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="SafetyDisclosureRef"
      >
        <div
          id="SafetyDisclosureChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getSafetyDisclosure } from "@/api/echrtsApi";
export default {
  components: {},
  name: "SafetyDisclosure",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "SafetyDisclosureChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.safetyDisclosureTotal"),
        seriesCenter: ["25%", "58%"],
        richNameWidth: 40,
        legendTop: '10px',
        noTooltipShow: true, //不显示
        itemStyleEmphasis: {
          label: {
            show: true,
            // position: 'center',
            x: "20%",
            y: "10%",
            textStyle: {
              rich: {
                numText: {
                  color: "#fff",
                  fontSize: 13,
                  width: 30,
                  textAlign: "center",
                },
                text: {
                  color: "#fff",
                  fontSize: 13,
                  padding: [0, 0, 0, 0],
                  width: 30,
                  textAlign: "center",
                },
              },
            },
            formatter: (params) => {
              return `{text| ${params.name}\n${params.value
                }}\n{numText|${this.$t("Proportion")}： ${params.percent || 0}%}`;
            },
          },
        },
        costomLegendFormatter:function(name){
          return name;
        },
      },
      totalCount: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.SafetyDisclosureRef.offsetWidth;
      this.barHeight = this.$refs.SafetyDisclosureRef.offsetHeight;
    },
    getBarData() {
      getSafetyDisclosure()
        .then((res) => {
          const {
            statusCode,
            data: { dataList, totalCount },
          } = res.data;
          if (statusCode == 200) {
            this.setEcharts(dataList);
            this.totalCount = totalCount;
          }
        })
        .catch(() => { });
    },
    setEcharts(val) {
      let dataList = val;
      // this.pieParams.titleInfor.text = totalCount;
      if (dataList.length > 0) {
        let legendFormatter = (name) => {
          const item = dataList.find((i) => {
            return i.name === name;
          });
          const p = item.value;
          let newName = name.length > 6 ? name.slice(0, 6) + "..." : name;
          return "{name|" + newName + "}" + "{percent|" + p + "}";
        };
        this.pieParams.legendFormatter = legendFormatter;
      }
      this.pieParams.data = dataList.map((item) => {
        switch (item.name) {
          case "施工作业交底":
            item.name = this.$t("customization.constructionWorkDisclosure");
            break;
          case "安全措施交底":
            item.name = this.$t("customization.safetyMeasuresDisclosure");
            break;
          case "工种作业交底":
            item.name = this.$t("customization.workTypeDisclosure");
            break;
          case "施工用电交底":
            item.name = this.$t("customization.constructionElectricityDisclosure");
            break;
          case "机械操作交底":
            item.name = this.$t("customization.mechanicalOperationDisclosure");
            break;
        }
        return item;
      });

      drawAnnularChart(this.pieParams);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
