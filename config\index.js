/*
 * @Description:
 * @Author:
 * @Date: 2022-01-27 18:12:56
 * @LastEditTime: 2025-07-17 16:11:45
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Usage:
 */
"use strict";
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require("path");
// const url = 'http://************:7008/api/index.html?urls.primaryName=T%20Special%20Check'

// const url = 'http://************:9070'
const url = "http://**************:9001";
const url2 = "http://**************:9100";
const url3 = "http://**************:20000";
const url4 = "http://gczl360.com:8084";
const url5 = "http://**************:8280";
module.exports = {
  dev: {
    // 开发环境下面的配置
    // Paths
    assetsSubDirectory: "static", // 除了 index.html 之外的静态资源要存放的路径
    assetsPublicPath: "/", // 打包后，index.html里面引用资源的的相对地址
    proxyTable: {
      // 代理 本地调试跨域配置
      // '/login': {
      //   target: url2,
      //   changeOrigin: true,
      //   ws: false, // 需要websocket 开启
      //   pathRewrite: {
      //     '^/login': '/'
      //   }
      // },
      // "/Spray": {
      //   target: url3,
      //   changeOrigin: true
      // },
      // "/DustIsolation": {
      //   target: url3,
      //   changeOrigin: true
      // },
      // "/Patriotic": {
      //   target: url3,
      //   changeOrigin: true
      // },
      // "/BuildWaste": {
      //   target: url3,
      //   changeOrigin: true
      // },
      // "/Carbonmanagement": {
      //   target: url3,
      //   changeOrigin: true
      // },
      "/gczl": {
        target: url4,
        changeOrigin: true
      },
      "/bjzb": {
        target: url3,
        changeOrigin: true,
        ws: false, // 需要websocket 开启
        pathRewrite: {
          "^/bjzb": "/"
        }
      },
      '/GB': {
        target: url5,
        changeOrigin: true,
        pathRewrite: {
          '^/GB': '/'
        }
      },
      "/modul": {
        target: url2,
        changeOrigin: true
      },
      "/project-manage": {
        target: url2,
        changeOrigin: true
      },
      "/login": {
        target: url2,
        changeOrigin: true
      },
      "/company": {
        target: url2,
        changeOrigin: true
      },
      "/other-operate": {
        target: url2,
        changeOrigin: true
      },
      "/public-common": {
        target: url2,
        changeOrigin: true,
      },
      "/": {
        target: url,
        changeOrigin: false,
        ws: false, // 需要websocket 开启
        pathRewrite: {
          "^/": ""
        },
        secure: false
      }
    },
    // Various Dev Server settings
    host: "localhost", // can be overwritten by process.env.HOST
    port: 8604, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: true,
    errorOverlay: true,
    notifyOnErrors: true,
    //使用文件系统(file system)获取文件改动的通知devServer.watchOptions
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-
    /**
     * Source Maps
     */
    // https://webpack.js.org/configuration/devtool/#development
    devtool: "cheap-module-eval-source-map", //增加调试，该属性为原始源代码（仅限行）不可在生产环境中使用
    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true, //使缓存失效
    cssSourceMap: true //代码压缩后进行调bug定位将非常困难，于是引入sourcemap记录压缩前后的位置信息记录，当产生错误时直接定位到未压缩前的位置，将大大的方便我们调试
  },

  build: {
    // 生产环境下面的配置
    // Template for index.html
    index: path.resolve(__dirname, "../projectIndex/index.html"), // index编译后生成的位置和名字，根据需要改变后缀，比如index.php
    // Paths
    assetsRoot: path.resolve(__dirname, "../projectIndex"), //在当前目录的上一级 的 dist目录下输出资源文件
    assetsSubDirectory: "static", //js,css,images存放文件夹名
    assetsPublicPath: "./", //发布的根目录，通常本地打包dist后打开文件会报错，此处修改为./。如果是上线的文件，可根据文件存放位置进行更改路径
    /**
     * Source Maps
     */
    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    // devtool: '#source-map',

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    //unit的gzip命令用来压缩文件，gzip模式下需要压缩的文件的扩展名有js和css
    productionGzip: true,
    productionGzipExtensions: ["js", "css"],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report
  }
};
