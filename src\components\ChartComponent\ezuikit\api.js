import Axios from "@/router/axios";

/**
 * 获取视频详情，萤石assetsToken通过视频详情接口获取 -- 刘贺
 * <AUTHOR>
 */
export function getVideoDetail(videoId) {
  return Axios({
    url: `/api/video-module/${videoId}`,
    method: "get",
  });
}

// 增加预设点
export function addVideoPreset(data) {
  return Axios({
    url: `/api/video-module/save-video-point`,
    method: "post",
    data,
  });
}

// 获取视频预设点
export function getVideoPresetList(videoId) {
  return Axios({
    url: `/api/video-module/video-point-get-list/${videoId}`,
    method: "post",
  });
}

/**
 * 删除视频监控预设点
 */
export function delVideoPreset(id) {
  return Axios({
    url: `/api/video-module/del-video-point/${id}`,
    method: "post",
  });
}

/**
 * 删除预设点后，需要删除里程碑定时抓拍设置点 -- 郑浩渊
 * <AUTHOR>
 */
export function delMileStoneCapture(data) {
  return Axios({
    url: `/api/construction-module/mile-stone-capture-del`,
    method: "post",
    data,
  });
}
