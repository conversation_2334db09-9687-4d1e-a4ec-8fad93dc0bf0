<!--
 * @Description: 智能烟感
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-25 15:36:20
 * @LastEditors: dong<PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="SmartSmokeRef"
      >
        <div
          id="SmartSmokeChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>

import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getSmartSmoke } from "@/api/echrtsApi";
export default {
  components: {},
  name: "SmartSmoke",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "SmartSmokeChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.smartSmokeTotal"),
        seriesCenter: ["25%", "58%"],
        tooltipFormatter: `{b}<br /> ${this.$t("number")}：{c} <br />${this.$t(
          "Proportion"
        )}：{d}%`,
        richNameWidth: 30,
        legendTop: '10px',
        costomLegendFormatter:function(name){
          return name;
        },
      },
      totalCount: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.SmartSmokeRef.offsetWidth;
      this.barHeight = this.$refs.SmartSmokeRef.offsetHeight;
    },
    getBarData() {
      getSmartSmoke()
        .then((res) => {
          const {
            statusCode,
            data: { dataList, totalCount },
          } = res.data;
          if (statusCode == 200) {
            if (dataList.length > 0) {
              let legendFormatter = (name) => {
                const item = dataList.find((i) => {
                  return i.name === name;
                });
                const p = item.value;
                return "{name|" + name + "}" + "{percent|" + p + "}";
              };
              this.pieParams.legendFormatter = legendFormatter;
            }
            this.totalCount = totalCount;
            // this.pieParams.titleInfor.text = totalCount;
            this.pieParams.data = dataList.map((item) => {
              switch (item.name) {
                case "正常":
                  item.name = this.$t("customization.normal");
                  break;
                case "报警":
                  item.name = this.$t("customization.alarm");
                  break;
                case "故障":
                  item.name = this.$t("customization.fault");
                  break;
                case "离线":
                  item.name = this.$t("Offline");
                  break;
              }
              return item;
            });
            drawAnnularChart(this.pieParams);
          }
        })
        .catch(() => { });
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.areaContent {
}
</style>
