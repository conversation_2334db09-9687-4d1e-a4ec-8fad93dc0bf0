<!--
 * @Description: 水利专版-高边坡监测
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="highSlopeRef"
      >
        <div
          id="highSlopeChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawBarLineTotal } from "@/components/constructionRecord/Echarts/echartsOne.js";
import { deviceRecord } from "@/api/echrtsApi";
export default {
  components: {},
  name: "VehicleManage",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      userId: "",
      barWidth: null,
      barHeight: null,
      barParams: {
        dom: "highSlopeChart",
        xAxisData: [],
        seriesData: [],
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        yminInterval: 1,
        boundaryGap: true,
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.userId = getStore({
      name: "userId",
    });
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
    this.$nextTick(() => {
      this.getBarData();
    })
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.highSlopeRef.offsetWidth;
      this.barHeight = this.$refs.highSlopeRef.offsetHeight;
    },
    getBarData() {
      deviceRecord()
        .then((res) => {
          let result = res.data;
          const { data, statusCode } = result;
          console.log(result, '----------------------')
          if (statusCode == 200) {

            var xInfor = [];
            var yInfor = [];
            var yInfor2 = [];

            if (data.length > 0) {
              data.forEach((items) => {
                xInfor.push(items.staticDate);
                yInfor.push(items.staticWarn);
                yInfor2.push(items.staticAlarm);
              });
            }

            // alert(23)
            let seriesData = [
              {
                name: '预警',
                type: "bar",
                stack: 'stack',
                data: yInfor,
                itemStyle: {
                  color: "yellow",

                },
              },
              {
                name: '报警',
                type: "bar",
                stack: 'stack',
                data: yInfor2,
                interval: 0,
                itemStyle: {
                  color: "red",
                },
              },
            ];
            this.barParams.xAxisData = xInfor;
            this.barParams.seriesData = seriesData;
            drawBarLineTotal(this.barParams);
          }
        })
        .catch(() => { });
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
