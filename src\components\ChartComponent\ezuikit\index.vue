<template>
  <div
    class="ezuikitPlayerWrap"
    ref="root"
  >
    <div
      class="videoWrap"
      @click="onVideo"
    >
      <div :id="domId ? domId : `video-container_${videoId}`"></div>
    </div>

    <!-- 不要把style挪到class中,否则有适配问题，需要和自带的云盘高度一致 -->

    <div
      class="presetWrap"
      v-if="radioValue == realTime && allowPreset"
    >
      <Preset
        :videoId="videoId"
        :videoCfg="videoCfg"
        @fetchYSAPIAfter="fetchYSAPIAfter"
      />
    </div>

    <div
      class="scaleWrap"
      style="height: 82px;padding: 3px;top: calc(50% - 93px);"
      v-if="pantileShow"
    >
      <i
        class="el-icon-zoom-in btn"
        style="font-size: 20px;"
        @mousedown="onMousedownPTZ(8)"
        @mouseup="onMouseUpPTZ(8)"
      ></i>
      <i
        class="el-icon-zoom-out btn"
        style="font-size: 20px;"
        @mousedown="onMousedownPTZ(9)"
        @mouseup="onMouseUpPTZ(9)"
      ></i>
    </div>

    <div
      class="tabSelect"
      v-if="videoType"
    >
      <el-radio-group
        v-model="radioValue"
        @input="setRadioValue"
      >
        <el-radio-button :label="realTime"></el-radio-button>
        <el-radio-button :label="playBack"></el-radio-button>
      </el-radio-group>
      <div v-if="radioValue === playBack">
        <el-date-picker
          v-model="value1"
          type="datetimerange"
          :range-separator="$t(`To`)"
          :start-placeholder="$t(`StartDate`)"
          :end-placeholder="$t(`EndDate`)"
          @change="handlerChange"
          value-format="timestamp"
        >
        </el-date-picker>
      </div>
    </div>
  </div>
</template>

<script>
import EZUIKit from "ezuikit-js";
import JkyUtils from "jky-utils";
import dayjs from "dayjs";

import { getVideoDetail } from "./api";

import Preset from "./preset";

const { getDeviceCapacity, videoControlPTZ } = JkyUtils.videoPTZ;

export default {
  name: "EZUIKitJs",
  components: { Preset },
  props: {
    domId: String,
    videoId: {
      type: [String, Number],
      retuired: true,
    },
    showPreset: {
      type: Boolean,
      default: false,
    },
    videoType: {
      type: Boolean,
      default: false,
    },
  },
  data: function () {
    return {
      videoCfg: {
        deviceSerial: "",
        channelNo: "",
        accessToken: "",
      },
      allowPreset: false,
      realTime: "",
      playBack: "",
      radioValue: "实时",
      value1: "",
      language: "",
      player: null,
      pantileShow: false,// 云台控制盘是否显示了,来同步显示隐藏缩放按钮
    };
  },
  watch: {
    videoId: {
      handler(newVal) {
        this.radioValue = '实时'
        this.getVideoDetail(newVal);
      },
    },
    // "$i18n.locale"(val) {
    //   if (val) {
    //     this.language = val
    //     this.languageChange();
    //     if (this.$IsProjectShow) {
    //       if (this.language == 'zh' || translate.language.getLocal() == 'zh') {
    //         translate.changeLanguage('chinese_simplified')
    //       }
    //       if (this.language == 'en' || translate.language.getLocal() == 'en') {
    //         translate.changeLanguage('english')
    //       }
    //       translate.execute() //进行翻译
    //     }
    //   }
    // },
  },

  mounted() {
    this.language = getStore({
      name: 'language'
    })
    this.initDatas(this.videoId);
    this.languageChange();
  },
  created() { },
  methods: {
    onVideo() {
      this.pantileShow = this.player && this.player.Theme.decoderState.state.pantile
    },
    async onMousedownPTZ(direction) {
      const cfg = {
        ...this.videoCfg
      }
      let res = await videoControlPTZ("start", { ...cfg, speed: 1, direction });
    },
    async onMouseUpPTZ(direction) {
      const cfg = {
        ...this.videoCfg
      }
      let res = await videoControlPTZ("stop", { ...cfg, direction });
      res = res.data;
      if (res.code != 200) this.$message.error(res.msg || "操作失败");
    },
    languageChange() {
      // if (this.radioValue == this.realTime) {
      //   this.radioValue = this.$t("Realtime")
      // } else {
      //   this.radioValue = this.$t("Playback")
      // }
      this.realTime = this.$t("Realtime")
      this.playBack = this.$t("Playback")
    },
    destroyPlayer() {
      this.player && this.player.stop();
    },
    handlerChange(val) {
      if (this.radioValue === this.playBack) {
        const startTime = dayjs(val[0]).format("YYYYMMDDHHmmss");
        const endTime = dayjs(val[1]).format("YYYYMMDDHHmmss");
        this.player
          .changePlayUrl({
            url: this.recEzOpenUrl + `?begin=${startTime}&end=${endTime}`,
          })
          .then(() => {
            console.log("切换成功");
          });
      }
    },
    // 初始化页面数据
    initDatas(videoId) {
      this.$nextTick(() => {
        this.getVideoDetail(videoId);
      });
    },

    // resetWSize() {
    //   if (this.player) {
    //     let [w, h] = this.computedSize();

    //     this.player.reSize(w, h);
    //   }
    // },

    // 根据萤石接口返回的code码确认是否需要刷新token
    fetchYSAPIAfter(data = {}) {
      if (data.code === "200") return;

      if (data.code === "10002") {
        this.$message.error("操作失败，请稍后再试");
        return this.resetAccessToken();
      }

      this.$message.error(data.msg || "操作失败");
    },

    // 刷新萤石accessToken
    resetAccessToken() {
      this.getVideoDetail(this.videoId);
    },

    // 初始化视频基本信息
    async getVideoDetail(videoId) {
      if (!videoId) return;

      const res = await getVideoDetail(videoId);
      let data = res.data || {};

      data = data.data || {};

      const { deviceSerial, channelNo, token, hdLiveEzOpenUrl, recEzOpenUrl } =
        data;

      this.videoCfg = {
        deviceSerial,
        channelNo,
        accessToken: token,
      };

      this.playerUri = hdLiveEzOpenUrl;
      this.recEzOpenUrl = recEzOpenUrl;
      if (
        this.player &&
        this.player.accessToken === this.videoCfg.accessToken
      ) {
        this.player.changePlayUrl({
          type: "live",
          url: hdLiveEzOpenUrl,
          hd: false,
        });
      }
      this.getDeviceCapacity();
    },

    // 查询设备能力集
    // https://open.ys7.com/help/77
    async getDeviceCapacity() {
      if (!this.showPreset) {
        this.allowPreset = false;
        if (
          !this.player ||
          this.player.accessToken !== this.videoCfg.accessToken
        ) {
          this.initVideo();
        }
        return;
      }

      let res = await getDeviceCapacity({ ...this.videoCfg });
      res = res.data || {};

      const data = res.data || {};

      let allowPreset = true;

      if (!data.ptz_preset || data.ptz_preset !== "1") {
        allowPreset = false;
      }

      this.allowPreset = allowPreset && this.showPreset;
      this.pantileShow = false

      if (!this.player || this.player.accessToken !== this.videoCfg.accessToken)
        this.initVideo();
    },

    computedSize() {
      const { offsetWidth, offsetHeight } = this.$refs.root;

      let h = offsetHeight;
      // if (this.allowPreset) {
      //   h = h - 40;
      // }
      h = h - 40;
      return [offsetWidth, h];
    },
    // 直播按钮功能
    getPlayerThemeData() {
      const params = {
        autoFocus: 5,
        poster:
          "https://resource.eziot.com/group1/M00/00/89/CtwQEmLl8r-AZU7wAAETKlvgerU237.png",
        header: {
          color: "#1890ff",
          activeColor: "#FFFFFF",
          backgroundColor: "#000",
          btnList: [
            {
              iconId: "deviceID",
              part: "left",
              defaultActive: 0,
              memo: "顶部设备名称",
              isrender: 0,
            },
            {
              iconId: "deviceName",
              part: "left",
              defaultActive: 0,
              memo: "顶部设备ID",
              isrender: 0,
            },
          ],
        },
        footer: {
          color: "#FFFFFF",
          activeColor: "#1890FF",
          backgroundColor: "#00000021",
          btnList: [
            {
              iconId: "play",
              part: "left",
              defaultActive: 1,
              memo: "播放",
              isrender: 1,
            },
            {
              iconId: "capturePicture",
              part: "left",
              defaultActive: 0,
              memo: "截屏按钮",
              isrender: 1,
            },
            {
              iconId: "sound",
              part: "left",
              defaultActive: 0,
              memo: "声音按钮",
              isrender: 1,
            },
            {
              iconId: "pantile",
              part: "left",
              defaultActive: 0,
              memo: "云台控制按钮",
              isrender: 1,
            },
            {
              iconId: "recordvideo",
              part: "left",
              defaultActive: 0,
              memo: "录制按钮",
              isrender: 1,
            },
            {
              iconId: "talk",
              part: "left",
              defaultActive: 0,
              memo: "对讲按钮",
              isrender: 1,
            },
            {
              iconId: "zoom",
              part: "left",
              defaultActive: 1,
              memo: "电子放大",
              isrender: 1,
            },
            {
              iconId: "sd",
              part: "right",
              defaultActive: 1,
              memo: "清晰度切换按钮",
              isrender: 1,
            },
            {
              iconId: "webExpend",
              part: "right",
              defaultActive: 0,
              memo: "网页全屏按钮",
              isrender: 0,
            },
            {
              iconId: "expend",
              part: "right",
              defaultActive: 0,
              memo: "全局全屏按钮",
              isrender: 1,
            },
          ],
        },
      };

      return params;
    },
    // 回放按钮功能
    getPlaybackThemeData() {
      const params = {
        // type: 'rec',
        autoFocus: 5,
        poster:
          "https://resource.eziot.com/group1/M00/00/89/CtwQEmLl8r-AZU7wAAETKlvgerU237.png",
        header: {
          color: "red",
          backgroundColor: "red",
          activeColor: "#1890FF",
          btnList: [
            // {
            //   iconId: 'deviceID',
            //   part: 'left',
            //   defaultActive: 0,
            //   isrender: 1,
            // },
            // {
            //   iconId: 'deviceName',
            //   part: 'left',
            //   defaultActive: 0,
            //   isrender: 1,
            // },
            // {
            //   iconId: 'cloudRec',
            //   part: 'right',
            //   defaultActive: 0,
            //   isrender: 1,
            // },
            // 回放的cloudRec、rec 存储方式必须有一个存在
            {
              iconId: "rec",
              part: "right",
              defaultActive: 0,
              isrender: 1,
            },
          ],
        },
        footer: {
          color: "#FFFFFF",
          backgroundColor: "#00000080",
          activeColor: "#1890FF",
          btnList: [
            {
              iconId: "play",
              part: "left",
              defaultActive: 1,
              isrender: 1,
            },
            {
              iconId: "capturePicture",
              part: "left",
              defaultActive: 0,
              isrender: 1,
            },
            {
              iconId: "sound",
              part: "left",
              defaultActive: 1,
              isrender: 1,
            },
            {
              iconId: "pantile",
              part: "left",
              defaultActive: 0,
              isrender: 0,
            },
            {
              iconId: "recordvideo",
              part: "left",
              defaultActive: 0,
              isrender: 1,
            },
            // {
            //   iconId: 'zoom',
            //   part: 'left',
            //   defaultActive: 0,
            //   isrender: 1,
            // },
            {
              iconId: "speed",
              part: "right",
              defaultActive: 0,
              isrender: 1,
            },
            // {
            //   iconId: 'hd',
            //   part: 'right',
            //   defaultActive: 0,
            //   isrender: 0,
            // },
            // {
            //   iconId: 'webExpend',
            //   part: 'right',
            //   defaultActive: 0,
            //   isrender: 1,
            // },
            {
              iconId: "expend",
              part: "right",
              defaultActive: 0,
              isrender: 1,
            },
          ],
        },
      };

      return params;
    },

    initVideo() {
      let that = this;

      const [w, h] = this.computedSize();

      const { accessToken } = this.videoCfg;

      that.player = new EZUIKit.EZUIKitPlayer({
        autoplay: false, //默认播放
        id: this.domId ? this.domId : `video-container_${this.videoId}`, // 视频容器ID
        accessToken: accessToken,
        url: this.playerUri,
        startTalk: () => that.player.startTalk(),
        stopTalk: () => that.player.stopTalk(),
        audio: 0, //是否开启声音  0 - 关闭 1 - 开启
        width: w,
        height: h,
        template: "pcLive",
        handleSuccess: () => {
          setTimeout(() => {
            this.player.closeSound();
            this.pantileShow = this.player && this.player.Theme.decoderState.state.pantile
          }, 1500);
        },
      });
    },
    setRadioValue(val) {
      this.radioValue = val;

      const { playerUri, recEzOpenUrl } = this;
      if (val == this.realTime) {
        this.player.changePlayUrl({ type: "live" });
        this.player.Theme.changeTheme(this.getPlayerThemeData());

        this.player
          .changePlayUrl({
            url: playerUri,
          })
          .then(() => {
            console.log("切换成功");
          });
      } else if (val == this.playBack) {
        this.player.changePlayUrl({ type: "rec" });
        this.player.Theme.changeTheme();
        // this.player.changePlayUrl({
        //   url: recEzOpenUrl,
        // }).then(() => {
        //   console.log('切换成功');
        // });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.ezuikitPlayerWrap {
  width: 100%;
  height: 100%;
  position: relative;
  .tabSelect {
    position: absolute;
    top: 15px;
    right: 20px;
  }
  .scaleWrap {
    background: rgba(255, 255, 255, 0.8);
    position: absolute;
    right: 20px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    justify-content: space-between;
    .btn {
      color: #1890ff;
      cursor: pointer;
    }

    .btn:active {
      background-image: linear-gradient(
        rgb(29, 141, 216),
        rgba(100, 143, 252, 0)
      );
    }
  }

  >>> .ez-ptz-wrap {
    right: 40px;
  }
}

.videoWrap {
  >>> .header-controls {
    display: none !important;
  }
}

.presetWrap {
}
</style>

<style type="text/css">
video {
  object-fit: fill;
}
</style>
