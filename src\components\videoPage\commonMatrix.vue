<!-- 视屏监控 -->
<template>
  <el-row class="listWrap" :gutter="10">
    <el-col :span="colSpan" class="list" v-for="(item, idx) in showList" :key="item.id" :style="{ height: `${listH}%` }">
      <div class="videoTitle">
        <span>{{ item.deviceName }}</span>
        <p class="videoChacnge" @click="changeVideo(idx)">视频切换</p>
      </div>

      <div class="videoWrap">
        <Player v-if="curPlayVideo.deviceSerial" :domId="`${item.id}_${idx}`" :key="`${item.id}_${idx}`"
          :showPreset="false" :videoId="item.id" />
      </div>
    </el-col>
    <el-dialog :visible.sync="dialogVisible" width="20%" title="视频切换">
      <ul class="switchList">
        <li v-for="(item) in dataList" :key="item.id" @click="switchVideo(item)">
          {{ item.deviceName }} ({{item.onlineStatusTxt}})
        </li>
      </ul>
    </el-dialog>
  </el-row>
</template>

<script>

import Player from './ezuikit'

export default {
  props: {
    dataList: {
      type: Array,
      default: () => []
    },
    matrixNum: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      showList: [],
      dialogVisible: false,
      colSpan: 12,

      listH: 50
    };
  },
  components: {
    Player
  },
  created() {
  },
  mounted() {
    const num = this.matrixNum;

    let showNum = num * num

    this.colSpan = 24 / num;
    this.listH = 100 / num;

    if (!this.dataList.length) {
      return;
    }

    const len = this.dataList.length >= showNum ? showNum : this.dataList.length;

    const list = []
    for (let i = 0; i < len; i++) {
      list.push({ ...this.dataList[i] })
    }

    this.showList = list;
    this.curPlayVideo = this.dataList[0]
  },
  methods: {
    changeVideo(idx) {
      this.changeIdx = idx;
      this.dialogVisible = true;
    },

    switchVideo(item) {
      this.showList[this.changeIdx] = { ...item }
      this.dialogVisible = false;
    }
  },
};

</script>


<style lang="scss" scoped>
.listWrap {
  width: 100%;
  height: 100%;

  .list {
    position: relative;

    display: flex;
    flex-direction: column;
    margin-bottom: 10px;

    .videoTitle {
      height: 40px;
      line-height: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0;
      padding: 0 16px;
      background: linear-gradient(270deg, #103594, #1e57b2);
    }

    .videoWrap {
      flex-grow: 1;
    }
  }


  .videoChacnge {
    cursor: pointer;
  }
}

.switchList {
  max-height: 400px;
  overflow: auto;

  li {
    height: 42px;
    line-height: 42px;
    padding-left: 10px;
    cursor: pointer;
  }

}

>>>.el-dialog__body {
  padding-top: 0;
}
</style>

