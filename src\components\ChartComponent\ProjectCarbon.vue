<!--
 * @Description: 项目碳管理
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-25 15:01:59
 * @LastEditors: dongqianqian
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="ProjectCarbonRef"
      >
        <div
          id="ProjectCarbonChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>

import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getCarbonmanagement } from "@/api/echrtsApi";
export default {
  components: {},
  name: "ProjectCarbon",
  props: {
    moduleName: String
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "ProjectCarbonChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.projectCarbonTotal"),
        seriesCenter: ["25%", "58%"],
        tooltipFormatter: function (infor) {
          let msg = `${infor.marker}${infor.data.name}
              <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
              <b>${infor.data.value}</b>kg/㎡`;
          return msg;
        }
      },
      costomLegendFormatter:function(name){
          return name;
        },
      arr: []
    };
  },
  watch: {
    "$i18n.locale"(val) {
      if (val && this.$IsProjectShow) {
        this.languageChange();
      }
    },
  },
  created() {
    this.projectId = getStore({
      name: "projectId"
    });
    this.companyId = getStore({
      name: "companyId"
    });
    if (this.$IsProjectShow) {
      this.languageChange()
    } else {
      this.getBarData();
    }
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    languageChange() {
      this.getBarData();
    },
    setEchartsWidth() {
      this.barWidth = this.$refs.ProjectCarbonRef.offsetWidth;
      this.barHeight = this.$refs.ProjectCarbonRef.offsetHeight;
    },
    getBarData() {
      getCarbonmanagement(this.projectId)
        .then(res => {
          const { status, data } = res;
          if (status == 200) {
            data.forEach(ele => {
              switch (ele.name) {
                case "人工碳排":
                  ele.name = this.$t("customization.projectCarbonArtificial");
                  break;
                case "材料碳排":
                  ele.name = this.$t("customization.projectCarbonMaterial");
                  break;
                case "机械碳排":
                  ele.name = this.$t("customization.projectCarbonMechanical");
                  break;
                case "建筑垃圾排放":
                  ele.name = this.$t("customization.projectCarbonWaste");
                  break;
                case "回收减排":
                  ele.name = this.$t("customization.projectCarbonRecycle");
                  break;
              }
            });
            this.pieParams.data = data;
            drawAnnularChart(this.pieParams);
          }
        })
        .catch(() => { });
    }
  }
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
