<!-- 危大工程-施工方案-的风险管理(查看)的-隐患管理(查看)--新增-->
<template>
  <el-dialog
    class="dialog-component"
    :visible.sync="showDialog"
    :append-to-body="true"
    height="75%"
    width="80%"
    @close="showDialog = false"
    :before-close="handleClose"
  >
    <div slot="title" class="header-title">
      <span class="title-name">现场劳务</span>
    </div>
    <el-row>
      <el-form ref="form" :model="trainForm" label-width="100px">
        <el-col v-if="false">
          <el-form-item label="工程名称:">
            <el-select
              v-model="trainForm.name"
              placeholder="请选择"
              @change="checkPersonSelect"
              style="width: 250px"
            >
              <el-option
                v-for="item in monitorPointList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                style="width: 250px"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <labourContentCharts ref="chartOverview" />
  </el-dialog>
</template>

<script>
import { getDataDailog } from "@/api/constructionRecord";
import labourContentCharts from "./labourContentCharts.vue";
export default {
  components: {
    labourContentCharts,
  },
  data() {
    return {
      title: "现场劳务",
      showDialog: false,
      trainForm: { name: null },
      monitorPointList: [
        {
          value: 1,
          text: "北京市政2",
        },
        {
          value: 2,
          text: "房地产项目",
        },
      ],
      projectId: "",
    };
  },
  mounted() {},
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    // this.getSelectData();
  },
  methods: {
    // getSelectData() {
    //   let para = {
    //     projectId: this.projectId,
    //     type: 0,
    //   };
    //   getDataDailog(para)
    //     .then((res) => {
    //       let result = res.data;
    //       if (result.statusCode == 200) {
    //         if (result.data.tList && result.data.tList.length > 0) {
    //           let arr = [];
    //           result.data.tList.forEach((ele) => {
    //             arr.push({ value: ele.id, label: ele.projectName });
    //           });
    //           this.monitorPointList = arr;
    //         }
    //       }
    //     })
    //     .catch(() => {});
    // },
    checkPersonSelect(val) {
      this.monitorPointList.forEach((ele) => {
        if (ele.value == val) {
          this.trainForm.name = ele.label;
          // this.searchForm.checkPersonName = ele.label;
          //传递参数值，图表数据发生变化
        }
      });
    },
    // open() {
    //   this.showDialog = true;
    // },
    handleClose() {
      this.showDialog = false;
      this.trainForm = {};
    },
  },
};
</script>
<style lang="scss" scoped>
// [data-v-4e21be86] .el-dialog__title {
//   margin-left: 45%;
// }
// /deep/ .el-dialog__body {
//   background: #0b3472;
// }
// /deep/ .el-date-editor.el-input,
// .el-date-editor.el-input__inner {
//   width: 250px;
// }
// /deep/ .el-dialog__title {
//   color: skyblue;
//   margin-left: 250px;
//   font-weight: 700;
// }
// /deep/ .el-dialog__header {
//   border-bottom: 1px solid blue;
//   background: #0b3472;
// }
.header-title {
  border-bottom: 1px solid #4fa8d0;
}
.title-name {
  height: 0.5rem;
  margin-left: 46%;
  line-height: 0.5rem;
  color: #00deff;
  // font-size: 0.4rem;
  font-size: 20px;
  font-weight: bold;
}
.content-box {
  display: flex;
  justify-content: center;
  .box {
    width: 55%;
    min-height: 550px;
    // background: #0b3472;
    margin-right: 70px;
  }
  .el-button {
    background: blue;
    color: #fff;
  }
}
</style>
