<!--
 * @Description: 见证取样
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-25 15:04:43
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="WitnessSampRef"
      >
        <div
          id="WitnessSampChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>

import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getWitnessSamp } from "@/api/echrtsApi";
export default {
  components: {},
  name: "WitnessSamp",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "WitnessSampChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.witnessSampTotal"),
        seriesCenter: ["25%", "58%"],
        richNameWidth: 70,
        tooltipFormatter: function (infor) {
          let msg = `${infor.marker}${infor.data.name}
              <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
              <b>${infor.data.value}</b>`;
          return msg;
        },
        costomLegendFormatter:function(name){
          return name;
        },
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.WitnessSampRef.offsetWidth;
      this.barHeight = this.$refs.WitnessSampRef.offsetHeight;
    },
    getBarData() {
      getWitnessSamp()
        .then((res) => {
          const { statusCode, data } = res.data;
          if (statusCode == 200) {
            let legendFormatter = (name) => {
              const item = data.find((i) => {
                return i.name === name;
              });
              const p = item.value;
              let clientWidth = document.documentElement.clientWidth;
              let newName = name.length > 7 ? name.slice(0, 7) + "..." : name;
              if (clientWidth < 1900) {
                newName = name.slice(0, 5) + "...";
                this.pieParams.richNameWidth = 60;
              }
              return "{name|" + newName + "}" + "{percent|" + p + "}";
            };
            this.pieParams.legendFormatter = legendFormatter;
            this.pieParams.data = data.map((item) => {
              switch (item.name) {
                case "砌体及砌筑砂浆":
                  item.name = this.$t("customization.masonry");
                  break;
                case "混凝土":
                  item.name = this.$t("customization.concrete");
                  break;
                case "防水材料":
                  item.name = this.$t("customization.WaterproofMaterials");
                  break;
                case "保温材料":
                  item.name = this.$t(
                    "customization.ThermalInsulationMaterials"
                  );
                  break;
                case "钢筋及接头":
                  item.name = this.$t("customization.ReinforcementAndJoints");
                  break;
                case "其他":
                  item.name = this.$t("customization.others");
                  break;
              }
              return item;
            });

            drawAnnularChart(this.pieParams);
          }
        })
        .catch(() => { });
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
