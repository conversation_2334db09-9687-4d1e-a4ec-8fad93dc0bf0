<!--
 * @Description: 卸料平台监测
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-25 15:31:29
 * @LastEditors: dong<PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="UnloadPlatformRef"
      >
        <div
          id="UnloadPlatformChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
      <p class="barCStyle">
        <span
          :class="barActive == items ? 'active' : null"
          @click="setActive(items)"
          v-for="(items, key) in barList"
          :key="key"
        >{{ items }}</span>
      </p>
      <!-- <p class="countStyle">
        {{ this.$t("customization.total") }} {{ barActive }}
        {{ this.$t("customization.cumulative") }}：{{ totalCount }}
      </p> -->
    </div>
  </div>
</template>
<script>
import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getUnloadPlatform } from "@/api/echrtsApi";
export default {
  components: {},
  name: "UnloadPlatform",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "UnloadPlatformChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.unloadPlatformTotal"),
        seriesCenter: ["25%", "58%"],
        richNameWidth: 40,
        // legendTop: '10px',
        noTooltipShow: true, //不显示
        itemStyleEmphasis: {
          label: {
            show: true,
            // position: 'center',
            x: "20%",
            y: "10%",
            textStyle: {
              rich: {
                numText: {
                  color: "#fff",
                  fontSize: 13,
                  width: 30,
                  textAlign: "center",
                },
                text: {
                  color: "#fff",
                  fontSize: 13,
                  padding: [0, 0, 10, 0],
                  width: 30,
                  textAlign: "center",
                },
              },
            },
            formatter: (params) => {
              return `{text| ${params.name} ${this.$t(
                "customization.cumulative"
              )}：${params.value}}\n{numText|${this.$t("Proportion")}： ${params.percent || 0
                }%}`;
            },
          },
        },
        costomLegendFormatter:function(name){
          return name;
        },
      },
      forcastStatistic: [], // 预警数据
      alarmStatistic: [], // 报警数据
      barList: [this.$t("earlywarning"), this.$t("giveAlarm")],
      barActive: this.$t("earlywarning"),
      totalCount: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.UnloadPlatformRef.offsetWidth;
      this.barHeight = this.$refs.UnloadPlatformRef.offsetHeight;
    },
    getBarData() {
      getUnloadPlatform()
        .then((res) => {
          const {
            statusCode,
            data: { forcastStatistic, alarmStatistic },
          } = res.data;
          if (statusCode == 200) {
            this.forcastStatistic = forcastStatistic;
            this.alarmStatistic = alarmStatistic;
            this.setEcharts(forcastStatistic);
          }
        })
        .catch(() => { });
    },
    setActive(val) {
      const { forcastStatistic, alarmStatistic } = this;
      this.barActive = val;
      if (val == this.$t("earlywarning")) {
        this.setEcharts(forcastStatistic);
      } else {
        this.setEcharts(alarmStatistic);
      }
    },
    setEcharts(val) {
      let dataList = val;
      let sum = 0;
      dataList.forEach((ele) => {
        sum = sum + Number(ele.value);
      });

      this.totalCount = sum;
      // this.pieParams.titleInfor.text = totalCount;
      this.pieParams.data = dataList.map((item) => {
        switch (item.name) {
          case "X倾斜报警":
            item.name = this.$t("customization.xAlarm");
            break;
          case "Y倾斜报警":
            item.name = this.$t("customization.yAlarm");
            break;
          case "超重报警":
            item.name = this.$t("customization.overWeightAlarm");
            break;
          case "X倾斜预警":
            item.name = this.$t("customization.xeAlarm");
            break;
          case "Y倾斜预警":
            item.name = this.$t("customization.yeAlarm");
            break;
          case "超重预警":
            item.name = this.$t("customization.overWeighteAlarm");
            break;
        }
        return item;
      });

      drawAnnularChart(this.pieParams);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
