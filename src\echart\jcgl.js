function ShowJCChart(name,data) {
    var echartleft1 = echarts.init($(".jcgl")[0]);
    var option1 = {
        tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
                type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [{
            type: 'category',
            axisLine: {
                lineStyle: {
                    type: 'solid',
                    color: 'rgb(104,104,104)',
                    width: '1'
                }
            },
            axisTick: {
                show: false
            },
            data: name,
            axisLabel: {
                interval: 0,
                show: true,
                splitNumber: 15,
                textStyle: {
                    fontSize: 10,
                    textAline: 'center',
                    color: '#fff'
                },
            }
        }

        ],
        yAxis: [{
            type: 'value',
            splitLine: {
                show: true,
                lineStyle: {
                    color: 'rgb(104,104,104)'
                }
            },
            axisLabel: {
                interval: 0,
                show: true,
                splitNumber: 15,
                textStyle: {
                    fontSize: 10,
                    textAline: 'center',
                    color: '#fff'
                },
            }
        }],
        series: [
            {
                name: '',
                type: 'bar',
                barWidth: '40%',
                itemStyle: {
                    normal: {
                        color: function (params) {
                            var colorList = [
                                'rgb(255,255,26)',
                                'rgb(30,255,0)',
                                'rgb(225,32,32)'
                            ];
                            return colorList[params.dataIndex]
                        },
                        label: {
                            show: false
                        }
                    }
                },
                data: data
            }
        ]
    };
    echartleft1.setOption(option1);
}