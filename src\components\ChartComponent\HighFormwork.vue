<!--
 * @Description: 高支模监测
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-08-01 14:58:06
 * @LastEditors: dongqi<PERSON>qian
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="HighFormworkRef"
      >
        <div
          id="HighFormworkChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
      <p class="barCStyle">
        <span
          :class="barActive == items ? 'active' : null"
          @click="setActive(items)"
          v-for="(items, key) in barList"
          :key="key"
        >{{ items }}</span>
      </p>
      <!-- <p class="countStyle">
        {{ this.$t("customization.total") }} {{ barActive }}
        {{ this.$t("customization.cumulative") }}：{{ totalCount }}
      </p> -->
    </div>
  </div>
</template>
<script>
import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getHighFormwork } from "@/api/echrtsApi";
export default {
  components: {},
  name: "HighFormwork",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "HighFormworkChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.highFormworkMonitoringTotal"),
        seriesCenter: ["25%", "58%"],
        richNameWidth: 40,
        legendTop: '10px',
        noTooltipShow: true, //不显示
        itemStyleEmphasis: {
          label: {
            show: true,
            // position: 'center',
            x: "20%",
            y: "10%",
            textStyle: {
              rich: {
                numText: {
                  color: "#fff",
                  fontSize: 13,
                  width: 30,
                  textAlign: "center",
                },
                text: {
                  color: "#fff",
                  fontSize: 13,
                  padding: [0, 0, 10, 0],
                  width: 30,
                  textAlign: "center",
                },
              },
            },
            formatter: (params) => {
              return `{text| ${params.name} ${this.$t(
                "customization.cumulative"
              )}：${params.value}}\n{numText|${this.$t("Proportion")}： ${params.percent || 0
                }%}`;
            },
          },
        },
        costomLegendFormatter: function (name) {
          return name;
        },
      },
      forcastStatistic: [], // 预警数据
      alarmStatistic: [], // 报警数据
      forcastNumber: 0,
      alarmNumber: 0,
      barList: [this.$t("earlywarning"), this.$t("giveAlarm")],
      barActive: this.$t("earlywarning"),
      totalCount: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.HighFormworkRef.offsetWidth;
      this.barHeight = this.$refs.HighFormworkRef.offsetHeight;
    },
    getBarData() {
      getHighFormwork(1)
        .then((res) => {
          const {
            statusCode,
            data: { dataList, totalCount },
          } = res.data;
          if (statusCode == 200) {
            this.forcastStatistic = dataList;
            this.forcastNumber = totalCount;
            this.totalCount = this.forcastNumber;
            this.setEcharts(dataList);
          }
        })
        .catch(() => { });
      getHighFormwork(2)
        .then((res) => {
          const {
            statusCode,
            data: { dataList, totalCount },
          } = res.data;
          if (statusCode == 200) {
            this.alarmStatistic = dataList;
            this.alarmNumber = totalCount;
          }
        })
        .catch(() => { });
    },
    setActive(val) {
      const { forcastStatistic, alarmStatistic } = this;
      this.barActive = val;
      if (val == this.$t("earlywarning")) {
        this.setEcharts(forcastStatistic);
        this.totalCount = this.forcastNumber;
      } else {
        this.setEcharts(alarmStatistic);
        this.totalCount = this.alarmNumber;
      }
    },
    setEcharts(val) {
      let dataList = val;
      // this.pieParams.titleInfor.text = totalCount;
      this.pieParams.data = dataList.map((item) => {
        switch (item.name) {
          case "立杆倾斜":
            item.name = this.$t("customization.poleInclination");
            break;
          case "立杆轴力":
            item.name = this.$t("customization.poleForce");
            break;
          case "模板沉降":
            item.name = this.$t("customization.templateDown");
            break;
          case "水平位移":
            item.name = this.$t("customization.horizontalMove");
            break;
        }
        return item;
      });

      drawAnnularChart(this.pieParams);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
