<!-- 隐患|风险 管理弹框 -->
<template>
  <el-dialog
    :visible.sync="scheduleDialog"
    :append-to-body="true"
    width="60%"
    @close="closeCli"
  >
    <div slot="title" class="header-title">
      <span class="title-name1" @click="HiddenClick">隐患|</span
      ><span class="title-name2" @click="RiskClick">风险</span>
      <!-- |<span class="title-name">风险</span> -->
    </div>
    <el-row>
      <el-col>
        <el-form ref="form">
          <el-col v-if="this.hidden">
            <TitleType @titleClick="titleClick"></TitleType>
          </el-col>
          <el-col v-if="this.risk">
            <TitleType2 @titleClickRight="titleClickRight"></TitleType2>
          </el-col>
          <el-col class="content" :key="keyTimer">
            <el-col v-if="this.hidden">
              <ContentCharts :tabShow="tabShow"></ContentCharts>
            </el-col>
            <el-col v-if="this.risk">
              <ContentCharts2 :tabShowRight="tabShowRight"></ContentCharts2>
            </el-col>
          </el-col>
        </el-form>
      </el-col>
    </el-row>
  </el-dialog>
</template>
<script>
import ContentCharts from "./contentCharts";
import TitleType from "./titleType";
import ContentCharts2 from "./contentCharts2";
import TitleType2 from "./titleType2";
export default {
  name: "riskDialog",
  components: { ContentCharts, TitleType, TitleType2, ContentCharts2 },
  props: {},
  data() {
    return {
      tabShow: "隐患级别对比",
      tabShowRight: "风险级别",
      hidden: true,
      risk: false,
      // hidden: false,
      // risk: true,
      keyTimer: "",
      scheduleDialog: false,
    };
  },
  created() {},
  mounted() {},
  methods: {
    HiddenClick() {
      this.hidden = true;
      this.risk = false;
    },
    RiskClick() {
      this.hidden = false;
      this.risk = true;
    },
    titleClick(val) {
      this.tabShow = val.name;
      this.keyTimer = +new Date();
    },
    titleClickRight(val) {
      this.tabShowRight = val.name;
      this.keyTimer = +new Date();
    },
    closeCli() {
      this.scheduleDialog = false;
      this.hidden = true;
      this.risk = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.header-title {
  border-bottom: 1px solid #4fa8d0;
}
.title-name1 {
  width: 100%;
  height: 0.5rem;
  line-height: 0.5rem;
  text-align: center;
  color: #00deff;
  font-size: 20px;
  // font-weight: bold;
  margin-left: 45%;
  background: rgba(13, 51, 134, 0.8);
  // border-bottom: 1px solid #4fa8d0;
}
.title-name2 {
  width: 100%;
  height: 0.5rem;
  line-height: 0.5rem;
  text-align: center;
  color: #00deff;
  font-size: 20px;
  // font-weight: bold;
  // margin-left: 45%;
  background: rgba(13, 51, 134, 0.8);
  // border-bottom: 1px solid #4fa8d0;
}
.content {
  width: 100%;
  height: 675px;
  float: left;
  .task {
    width: 80px;
    height: 100%;
    display: inline-block;
    float: right;
    margin-right: 8%;
    margin-top: 5%;
    cursor: pointer;
    text-align: center;
    .top {
      font-size: 16px;
      text-align: center;
    }
    .bottom {
      font-size: 16px;
      text-align: center;
    }
  }
}
</style>

