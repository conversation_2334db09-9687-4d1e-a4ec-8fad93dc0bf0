.device-management-wrap,.reportClass{


.el-button{
    background: #295AEA;
    border-radius: 4px 4px 4px 4px;
    border:1px solid #295AEA;
    color: #FFFFFF;
}
.el-button--primary:hover, .el-button--primary:focus{
    background: #295AEA;
    border-radius: 4px 4px 4px 4px;
    border:1px solid #295AEA;
    color: #FFFFFF;
}
.el-button:hover, .el-button:focus {
  border-color: transparent;
}
.el-checkbox{
    color: #FFFFFF;
}
.el-checkbox__inner::after{
    border: 2px solid #0686F9;
    border-left: 0;
    border-top: 0;
 }
.el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
    border-color: #0686F9;
    background: transparent;
}
.el-checkbox__input.is-checked+.el-checkbox__label {
  color: #fff;
}
.el-checkbox__input.is-disabled .el-checkbox__inner {
  background-color: #AAAAAA;
  border-color: #AAAAAA;
}
.el-checkbox__input.is-disabled+span.el-checkbox__label {
  color: #C0C4CC;
  cursor: not-allowed;
}
.el-dialog{
  background: #0E2E67 !important;
}
.el-dialog__body {
    background: #0E2E67 !important;
    border: 2px solid #469ECA;
}
.el-dialog__header {
    background: #0E2E67 !important;
    border: 2px solid #469ECA  !important;
    border-bottom: none !important;
    text-align: center !important;
}
.el-dialog__title {
    font-size: 18px  !important;
    font-family: PingFang SC, PingFang SC !important;
    font-weight: 600 !important;
    color: #01CEF5 !important;
}
.el-icon-close{
  color: #00D9FF !important;
  font-size: 25px  !important;
}
/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 4px  !important;
  border-radius: 2px  !important;
  background: rgba(0,145,255,0.2) !important;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
    // -webkit-box-shadow: inset 0 0 6px rgb(186, 183, 183);
    width: 4px !important;
    background: rgba(0,145,255,0.2) !important;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
    border-radius: 10px !important;
    // -webkit-box-shadow: inset 0 0 6px rgb(186, 183, 183);
    background: rgba(0,145,255,0.6) !important;
}

.el-table{
   background: rgba(17,69,125,0.6) !important;
}
.el-table td .el-button{
    background: none !important;
    border: none !important;
}
.el-table tr {
  background: transparent !important;
}
.el-table td {
  background: transparent !important;
}
.el-table .el-table__header th {
    // background-color:  rgba(31,198,255,0.1)!important;
    background-color:  #115893 !important;
    height: 58px !important;
    line-height: 58px;
    font-size: 16px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
}
.el-table .cell{
  height: auto;
  line-height: auto;
}
.el-table th.el-table__cell.is-leaf, .el-table td.el-table__cell {
   border-bottom: none;
}
.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  border-bottom: none;
}
  //避免单元格之间出现白色边框
  .el-table__row > td {
    border: none;
  }

  .el-table tbody tr:hover > td {
			background-color: rgba(31,198,255,0.1) !important;
      border-radius: 0px 0px 0px 0px;
      height: 46px !important;
      line-height: 46px !important;
      padding: none !important;
      color: #1FC6FF;
}
.el-table::before {
  background-color: transparent!important;
}
.el-table--border .el-table__cell, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
   border-right: none;
}
.el-table--group, .el-table--border {
   border:transparent;
}
.el-table--enable-row-transition .el-table__body td.el-table__cell {
  height: 46px !important;
  line-height: 46px !important;
}
// .el-table thead > .el-table-column--selection  {
// 	background-color: rgba(31,198,255,0.1) !important;
// }
.el-table__fixed {
  top: 0.5px;
}
.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
   border-bottom: none !important;
}
.el-table .cell.el-tooltip {
  white-space: normal !important;
  height: auto !important;
  margin:10px !important;
}
}

.el-message-box {
  background-color: #0b3472  !important;
  border: none !important;
  .el-button{
    background: #295AEA;
    border-radius: 4px 4px 4px 4px;
    border:1px solid #295AEA;
    color: #FFFFFF;
  }
  .el-button--primary:hover, .el-button--primary:focus{
    background: #295AEA;
    border-radius: 4px 4px 4px 4px;
    border:1px solid #295AEA;
    color: #FFFFFF;
  }
  .el-button:hover, .el-button:focus {
  border-color: transparent;
  background: #295AEA;
  }
}
.el-message-box__content {
  color: #fff !important;
}
.el-message-box__title {
  color: #fff !important;
}

.el-select-dropdown__item.selected{
  color: #1FC6FF !important;
  background: rgba(31,198,255,0.2) !important;
}
.el-select-dropdown__item.hover,.el-select-dropdown__item:hover{
  background: rgba(31,198,255,0.2) !important;
}
.time-select-item.selected:not(.disabled) {
  color: #1FC6FF !important;
  background: rgba(31,198,255,0.2) !important;
}
.time-select-item:hover {
  background-color: rgba(31,198,255,0.2) !important;
}
.evidencesClass {
  .el-dialog{
    margin-top: 5vh !important;
  }
    .el-table .cell.el-tooltip{
      overflow: hidden  !important; /* 隐藏超出容器宽度的文本 */
      text-overflow: ellipsis  !important; /* 显示省略号 */
      white-space: nowrap  !important; /* 禁止换行 */
    }
}
