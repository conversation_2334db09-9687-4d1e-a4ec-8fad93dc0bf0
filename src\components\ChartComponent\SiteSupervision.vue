<!--
 * @Description: 监理旁站
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-24 17:26:54
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div class="box" ref="SiteSupervisionRef">
        <div
          id="SiteSupervisionChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawBarLineTotal } from "@/components/constructionRecord/Echarts/echartsOne.js";
import { drawCustomBar } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getSiteSupervision } from "@/api/echrtsApi";
export default {
  components: {},
  name: "SiteSupervision",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      lineParams: {
        dom: "SiteSupervisionChart",
        xAxisData: [],
        seriesData: [],
        boundaryGap: true,
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        yminInterval: 1,
        tooltipFormatter: function (val) {
          let msg = `${val[0].axisValue}`;
          if (val.length) {
            val.forEach((ele) => {
              msg += `<br/>${ele.marker}${ele.seriesName}
              <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
              <b>${ele.data}</b>`;
            });
          }
          return msg;
        },
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.SiteSupervisionRef.offsetWidth;
      this.barHeight = this.$refs.SiteSupervisionRef.offsetHeight;
    },
    getData() {
      getSiteSupervision(this.projectId)
        .then((res) => {
          let result = res.data;
          const {
            data: { xdata, ydata },
            statusCode,
          } = result;
          if (statusCode == 200) {
            let seriesData = [];
            ydata.forEach((ele) => {
              seriesData.push({
                ...ele,
                type: "bar",
              });
            });

            this.lineParams.xAxisData = xdata;
            this.lineParams.seriesData = seriesData.map((item) => {
              switch (item.name) {
                case "旁站次数":
                  item.name = this.$t("customization.sideSite");
                  break;
                case "问题数量":
                  item.name = this.$t("customization.problemNum");
                  break;
              }
              return item;
            });
            drawCustomBar(this.lineParams);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
