<!--
 * @Description:
 * @Author:
 * @Date: 2022-09-08 09:19:28
 * @LastEditTime: 2025-07-15 17:10:03
 * @LastEditors: dong<PERSON><PERSON><PERSON><PERSON>
 * @Usage:
-->
<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <meta name="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <!-- <link rel="icon" href="./static/favicon.ico"> -->
  <link rel="icon" href="http://<%= process.env.npm_config_ip %>:9001/uploads/static/favicon.ico">
  <link rel="stylesheet" href="https://cdn-image.jiankeyan.com/Other/package/<EMAIL>">
  <link rel="stylesheet" href="https://api.cloud.pkpm.cn/bimviewer/viewer/v5/obv.css" type="text/css" />

  <script src="https://image-zhgd.oss-cn-beijing.aliyuncs.com/Other/package/<EMAIL>"></script>
  <script src="https://cdn-image.jiankeyan.com/Other/package/<EMAIL>"></script>
  <script src="https://cdn-image.jiankeyan.com/Other/package/<EMAIL>"></script>
  <script src="https://cdn-image.jiankeyan.com/Other/package/<EMAIL>"></script>
  <script src="https://cdn-image.jiankeyan.com/Other/package/<EMAIL>"></script>
  <script src="https://cdn-image.jiankeyan.com/Other/package/<EMAIL>"></script>
  <!-- <script src="https://cdn-image.jiankeyan.com/Other/package/HeaderTopPlugin.js?v=<%= new Date().getTime()"></script> -->
  <script src="https://api.cloud.pkpm.cn/bimviewer/viewer/v5/obv.js"></script>
  <script src="https://cdn-image.jiankeyan.com/Other/package/echarts-gl.min.js"></script>
  <script src="https://cdn-image.jiankeyan.com/Other/package/HeaderTopPlugin.js?v=<%= new Date().getTime()"></script>

  <title>项目首页</title>
  <script type="text/javascript" src="./static/js/jessibuca/jessibuca.js"></script>
</head>

<body>
  <div id="app"></div>
  <script>
    // 设置 rem 函数
    function setRem() {
      // rem等比适配配置文件
      // 基准大小
      const baseSize = 16;
      // console.log(document.documentElement.clientHeight)

      let oldWidth = document.documentElement.clientWidth
      // 当前页面屏幕分辨率相对于 1280宽的缩放比例，可根据自己需要修改

      const scale = oldWidth >= 1200 ? oldWidth / 1920 : 1200 / 1920;
      // 设置页面根节点字体大小（“Math.min(scale, 3)” 指最高放大比例为3，可根据实际业务需求调整）
      let newSize = (baseSize * Math.min(scale, 3)).toFixed(2)

      document.documentElement.style.fontSize = newSize + 'px';
      console.log(document.documentElement.style.fontSize)
    }
    // 初始化
    setRem();
    // 改变窗口大小时重新设置 rem
    let tid = null
    window.addEventListener('resize', function () {
      // 防抖 节流
      clearTimeout(tid);
      tid = setTimeout(setRem, 300);
    }, false)
  </script>
</body>

</html>