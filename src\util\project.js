/*
 * @Author: Jky200gu<PERSON> <EMAIL>
 * @Date: 2024-02-26 10:26:26
 * @LastEditors: Jky200guo <EMAIL>
 * @LastEditTime: 2024-03-06 10:43:43
 * @FilePath: \ProjectHomePage\src\util\project.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 阿联酋详细翻译，projectId为444、192
export const getProjectId = () => {
  var id =getStore({ name: "projectId"})
  var isShow=false
  let arr =['444','192']
  let index = arr.findIndex(item => {
    return item === id;
})
  if(index>-1){
    isShow=true
  }
  return isShow
}
