html,
body {
  font-family: "Microsoft Yahei";
  font-size: 16px;
  width: 100%;
  height: 100%;
  -webkit-tap-highlight-color: transparent;
  overflow: hidden;
  margin: 0;
  padding: 0;
  color: #fff;
}

body,
button,
h1,
h2,
h3,
h4,
h5,
h6,
input,
ul,
li,
ol,
dl,
dt,
dd,
p,
i,
b,
textarea,
header {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

li {
  list-style: none;
}

a {
  text-decoration: none;
  color: #333;
}

body::-webkit-scrollbar {
  display: none;
}

body {
  -moz-user-select: none;
  /* Firefox私有属性 */
  -webkit-user-select: none;
  /* WebKit内核私有属性 */
  -ms-user-select: none;
  /* IE私有属性(IE10及以后) */
  -khtml-user-select: none;
  /* KHTML内核私有属性 */
  -o-user-select: none;
  /* Opera私有属性 */
  user-select: none;
  /* CSS3属性 */
}

.textAlignCenter {
  text-align: center;
}

.danger {
  color: red !important;
}

.contentBg {
  background: #0b3472;
  padding: 10px 20px;
  box-sizing: border-box;
}