<template>
    <div class="device-management-wrap">
      <div class="left contentStyle">
        <div
          v-for="(item, index) in operateList"
          :key="item.id"
          :class="{ isactive: index == isActive, item: 'item' }"
          @click="leftClick(index, item.item1)"
        >
            <div class="statusNo">{{ item.item2 }}</div>
        </div>
      </div>
      <div
        class="middle contentStyle"
        style="overflow: auto;padding: 1.25rem !important;"
      >
        <el-row>
          <el-col>
            <el-form
              ref="form"
              :model="myForm"
              label-width="100px"
            >
            <el-col
                :span="6"
              >
                <el-form-item label="查询平台模块:">
                  <el-select
                    placeholder="请选择"
                    v-model="myForm.menuId"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.menuId"
                      :label="item.menuName"
                      :value="item.menuId"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col  :offset="1" :span="6">
                <el-form-item label="查询条件:">
                  <el-input   v-model="myForm.keyContent"  placeholder="请输入关键字查询"></el-input>
                </el-form-item>
              </el-col>

              <el-col
                :span="5"
                :offset="1"
              >
                <el-button
                  type="primary"
                  class="buttonType"
                  @click="selectInfo()"
                >查询</el-button>
              </el-col>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
              <TableList
              :showTooltip="true"
              :border="false"
                :containerHeight="640"
                :tHeader="columns"
                :tableData="listData"
              >
                <template #options="scope">
                  <el-button type="text" @click="detailClick(scope.row)"
                    >查看</el-button
                  >
                </template>
                <template #menu="scope">
                  <div v-if="scope.row.menuList.length>0">
                    <div v-for="(e,index) in scope.row.menuList" :key="index" style="display: inline;">
                      <span v-if="e.isProjectAuth"  @click="itemClick(e)">
                        <span class="isAuth">{{ e.menuName }}</span>
                        <span v-if="index>=0&&index<scope.row.menuList.length-1">、</span>
                      </span>
                      <span v-else >
                        <span class="unAuth">{{ e.menuName }}</span>
                        <span v-if="index>=0&&index<scope.row.menuList.length-1">、</span>
                      </span>
                    </div>
                  </div>
                  <div v-else>
                    暂无
                  </div>
                </template>
                <template #snapMenu="scope">
                  <div v-if="scope.row.snapMenuList.length>0">
                    <div v-for="(e,index) in scope.row.snapMenuList" :key="index" style="display: inline;">
                      <span>
                        {{ e.menuName }}
                        <span v-if="index>=0&&index<scope.row.snapMenuList.length-1">、</span>
                      </span>
                    </div>
                  </div>
                  <div v-else>
                    暂无
                  </div>
                </template>
              </TableList>
            </el-col>
        </el-row>
      </div>
      <div>
        <el-dialog
          title="查看"
          :visible.sync="visibleDetail"
          width="30%"
          append-to-body
          class="reportClass"
        ><el-row>
            <el-form :model="detailForm" label-width="130px">
              <el-col>
                <el-form-item label="智慧工地做法：" >
                  <div>{{ detailForm.name }}</div>
                    </el-form-item>
                    <el-form-item label="认证关键点：">
                      <div>{{ detailForm.keyContent }}</div>
                    </el-form-item>
                    <el-form-item label="平台匹配模块："  >
                      <div v-for="(item,index) in detailForm.menuList" :key="index" style="display: inline;">
                      <div style="display: inline;"><span >{{ item.menuName }}</span><span v-if="index>=0&&index<detailForm.menuList.length-1">、</span></div>
                      </div>
                    </el-form-item>
                    <el-form-item label="自定义抓拍模块：" >
                      <el-col v-if="!disabled">
                        <el-col  style="display: inline;cursor: pointer;" >
                          <el-col v-if="isCopy&&detailForm.copySnapMenuList&&detailForm.copySnapMenuList.length>0"  style="display: contents;">
                          <div v-for="(item,index) in detailForm.copySnapMenuList" :key="index" class="snapStyles"  >
                            <div class="snapType">{{ item.menuName }}<i class="el-icon-close"  @click="delClick(index)"></i></div>
                          </div>
                        </el-col>
                        <i class="el-icon-circle-plus" style="cursor: pointer;font-size:18px" @click="addClick"></i>
                        </el-col>
                      </el-col>
                      <el-col v-else >
                        <el-col  style="display: inline;cursor: not-allowed;" >
                          <el-col v-if="isCopy&&detailForm.copySnapMenuList&&detailForm.copySnapMenuList.length>0"  style="display: contents;">
                          <div v-for="(item,index) in detailForm.copySnapMenuList" :key="index" class="snapStyles"  >
                            <div class="snapType">{{ item.menuName }}<i class="el-icon-close"></i></div>
                          </div>
                        </el-col>
                        <i class="el-icon-circle-plus" style="cursor:  not-allowed;font-size:18px"></i>
                        </el-col>
                      </el-col>

                    </el-form-item>
                    <el-form-item label="自动抓拍时间：" style="display: inline-block;" >
                    每月
                          <el-select :disabled="disabled" v-model="detailForm.snapDay" style="width:30%;display: inline-block;margin: 0 10px;" placeholder="请选择">
                            <el-option
                              v-for="item in snapDayOptions"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value">
                            </el-option>
                          </el-select>
                          日
                          <el-time-select
                          :disabled="disabled"
                            style="width:32%;display: inline-block;margin: 0 10px;"
                              v-model="detailForm.snapDayHour"
                              :picker-options="{
                                start: '00:00',
                                step: '01:00',
                                end: '23:00'
                              }"
                            placeholder="请选择">
                          </el-time-select>
                    </el-form-item>
                    <el-col style="text-align: center;">
                      <el-button type="primary" v-if="disabled" @click="editClik">编辑</el-button>
                      <el-button type="primary" v-if="!disabled" @click="handleClose('detailForm')">取消</el-button>
                      <el-button type="primary"  @click="handleAdd('detailForm')">确定</el-button>
                    </el-col>
              </el-col>
            </el-form>
            <el-dialog
            v-if="addDetail"
              title="自定义抓拍模块"
              :visible.sync="addDetail"
              width="380px"
              append-to-body
              v-draggable
              class="reportClass "
              ><el-row>
                  <el-form :model="supplyform" ref="supplyform" label-width="100px">
                    <el-col :span="20" :offset="2">
                      <el-form-item label="" >
                      <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
                      <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange">
                        <el-checkbox v-for="city in cities" :label="city.menuName" :disabled="!city.isProjectAuth" :key="city.menuName">{{city.menuName}}</el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                    </el-col>
                    <el-col style="text-align: center;">
                      <el-button type="primary" @click="CloseClick()">取消</el-button>
                      <el-button type="primary" @click="sureClick()">确定</el-button>
                    </el-col>
                  </el-form>
              </el-row>
            </el-dialog>
          </el-row>
        </el-dialog>
      </div>
    </div>
</template>
<script>

import draggable from './draggable.js';
import {
  getTokenNew,
} from '@/api/home';
import { typeEnum,menuList,getList,practiceList,saveMenu  } from "@/api/report";
export default {
  directives: {
    draggable,
  },
  data: () => ({
    components: {
    },
    isActive: 0,
    operateList: [
    ],
    myForm: {
      menuId: 0,
      keyContent: "",
      type: 0
    },
    options: [
      {
        value: 0,
        label: "全部",
      },
      {
        value: 1,
        label: "劳务管理",
      },
      {
        value: 2,
        label: "视频管理",
      },
    ],
    listData: [
  ],
    columns: [
      {
        prop: "index",
        label: "序号",
        type: "codeNum",
        width: 80
      },
      {
        prop: "name",
        label: "智慧工地做法",
      },
      {
        prop: "keyContent",
        label: "认证关键点",
      },
      {
        prop: "menu",
        label: "平台匹配模块(灰色为未授权模块)",
        type: "slot",
        width: 300
      },
      {
        prop: "snapMenu",
        type: "slot",
        label: "自定义抓拍模块",
      },
      {
        prop: "snapTime",
        label: "自动抓拍时间",
        width: 150
      },
      {
        prop: 'options',
        type: "slot",
        label: '操作',
        width: 100
      },
    ],
    visibleDetail: false,
    addDetail: false,
    detailForm: {
      id: 0,
      name: '',
      keyContent: '',
      menuList: [],
      snapMenuList: [],
      copySnapMenuList: [],
      snapDay: null,
      snapDayHour: null
    },
    supplyform: {region: ''},
    title: '',
     visibleImg: false,
     imgUrl: '',
     checkAll: false,
    checkedCities: [],
    cities: [],
    isIndeterminate: true,
    snapDayOptions: [{
          value: '1',
          label: '1'
        },{
          value: '2',
          label: '2'
        },{
          value: '3',
          label: '3'
        },{
          value: '4',
          label: '4'
        },{
          value: '5',
          label: '5'
        },{
          value: '6',
          label: '6'
        },{
          value: '7',
          label: '7'
        },{
          value: '8',
          label: '8'
        },{
          value: '9',
          label: '9'
        },{
          value: '10',
          label: '10'
        },{
          value: '11',
          label: '11'
        },
        {
          value: '12',
          label: '12'
        },
        {
          value: '13',
          label: '13'
        },
        {
          value: '14',
          label: '14'
        },
        {
          value: '15',
          label: '15'
        },
        {
          value: '16',
          label: '16'
        },
        {
          value: '17',
          label: '17'
        },
        {
          value: '18',
          label: '18'
        },
        {
          value: '19',
          label: '19'
        },
        {
          value: '20',
          label: '20'
        },
        {
          value: '21',
          label: '21'
        },
        {
          value: '22',
          label: '22'
        },
        {
          value: '23',
          label: '23'
        },
        {
          value: '24',
          label: '24'
        },
        {
          value: '25',
          label: '25'
        },
        // {
        //   value: '26',
        //   label: '26'
        // },
        // {
        //   value: '27',
        //   label: '27'
        // },
        // {
        //   value: '28',
        //   label: '28'
        // },
        // {
        //   value: '29',
        //   label: '29'
        // },
        // {
        //   value: '30',
        //   label: '30'
        // },
        // {
        //   value: '31',
        //   label: '31'
        // },
      ],
    disabled: true,
    menuIdList: [],
    isCopy: true
  }),
  // 生命周期函数
  created() {
    this.getData();
  },
  methods: {
    // 全选
    handleCheckAllChange(val) {
      let isAuth = [],unAuth=[],allCheckedItem=[]
      this.cities.forEach((ele)=>{
        if(ele.isProjectAuth){
          isAuth.push(ele)
          allCheckedItem.push(ele)
        }else{
          unAuth.push(ele)
        }
      })
      let isAuthChecked = [],unAuthChecked=[]
      this.detailForm.copySnapMenuList.forEach((ele)=>{
        if(ele.isProjectAuth){
          isAuthChecked.push(ele)
        }else{
          unAuthChecked.push(ele)
          allCheckedItem.push(ele)
        }
      })
      let checkedsItem = val ? allCheckedItem : [];
      this.checkedCities=[]
      // console.log(checkedsItem,"checkedsItem")
        this.isIndeterminate = false;
        if (checkedsItem.length > 0) {
          checkedsItem.forEach((item) => {
            this.checkedCities.push(item.menuName);
          });
        } else {
          this.checkedCities = unAuthChecked.map((item) => item.menuName);
        }
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.cities.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length;
    },
    editClik(){
      this.disabled=false
    },
    // 自定义抓拍模块
    addClick(){
      this.addDetail=true
      this.checkedCities=[]
      practiceList(this.detailForm.id).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          this.cities = result.data;
          this.detailForm.copySnapMenuList.forEach((ele)=>{
            result.data.forEach((e)=>{
              if(ele.menuId==e.menuId){
                ele.isProjectAuth = e.isProjectAuth
              }
            })
          })
          this.checkedCities= this.detailForm.copySnapMenuList.map((item) => item.menuName);
          this.isIndeterminate = this.checkedCities.length > 0 && this.checkedCities.length < this.cities.length;
          this.checkAll = this.checkedCities.length === this.cities.length;
        }else{
          this.$message.error(result.errors);
        }
      });
    },
    CloseClick(){
      this.addDetail=false
    },
    sureClick(){
      this.menuIdList =[]
      this.detailForm.copySnapMenuList=[]
      this.checkedCities.forEach((e)=>{
        this.cities.forEach((ele)=>{
          if(e==ele.menuName){
            this.menuIdList.push(ele.menuId)
            this.detailForm.copySnapMenuList.push(ele)
          }
        })
      })
      this.addDetail = false
    },
    delClick(index){
      this.isCopy=false
      this.menuIdList =[]
      this.detailForm.copySnapMenuList.splice(index,1)
      this.detailForm.copySnapMenuList.forEach((e)=>{
        this.menuIdList.push(e.menuId)
      })
      this.isCopy=true
    },

    // 关闭
    handleClose() {
      this.visibleDetail=false
      this.disabled=true
    },
    // 保存
    handleAdd() {

      if(this.detailForm.copySnapMenuList.length==0&&this.menuIdList.length==0){
          this.detailForm.snapDay=0
          this.detailForm.snapDayHour=0
      }else{
          if(!this.detailForm.snapDay){
            this.$message.error('请选择自动抓拍日期')
            return;
          }
          if(!this.detailForm.snapDayHour){
              this.$message.error('请选择自动抓拍时间')
              return;
          }else{
              this.detailForm.snapDayHour= this.detailForm.snapDayHour.slice(0,2)
          }
      }
      let params = {
        practiceId: this.detailForm.id,
        menuIdList: this.menuIdList,
        snapDay: this.detailForm.snapDay,
        snapDayHour: this.detailForm.snapDayHour,
      }
        saveMenu(params)
          .then(res => {
            let {
              data: { statusCode, errors }
            } = res;
            if (statusCode == 200) {
              this.visibleDetail = false
              this.disabled=true
              this.leftClick(this.isActive,this.myForm.type);
              setTimeout(()=>{
                this.$message.success("保存成功");
              },300)
            } else {
              this.$message.error(errors);
            }
          })
          .catch(() => {
            this.visibleDetail = false;
          });
    },
    // 查看详情
    detailClick(val){
      this.detailForm ={
      id: 0,
      name: '',
      keyContent: '',
      menuList: [],
      snapMenuList: [],
      copySnapMenuList: [],
      snapDay: null,
      snapDayHour: null
    }
      this.detailForm=val
      if(this.detailForm.snapDayHour){
        if(this.detailForm.snapDayHour.toString().length>1){
          this.detailForm.snapDayHour=this.detailForm.snapDayHour+':00'
        }else{
          this.detailForm.snapDayHour= '0'+this.detailForm.snapDayHour+':00'
        }
      }else{
        this.detailForm.snapDayHour=null
      }

      this.detailForm.copySnapMenuList = JSON.parse(JSON.stringify(val.snapMenuList))
      this.menuIdList =[]
      if(this.detailForm.snapMenuList){
        this.detailForm.snapMenuList.forEach((e)=>{
          this.menuIdList.push(e.menuId)
        })
      }
      this.disabled=true
      this.visibleDetail = true;
    },
    // 点击整条
    leftClick(index, itemid) {
      this.operateList.map((item) => {
        item.active = 0;
      });
      this.operateList[index].active = 1;
      this.isActive = index;
      if (itemid) {
        this.myForm.type = itemid;
      } else {
        this.myForm.type = "";
      }
      this.getOptions(itemid)
      this.selectInfo();
    },
    getOptions(val){
      menuList(val).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          this.options = result.data;
          this.options.unshift({menuId: 0,menuName: '全部'})
        }else{
          this.$message.error(result.errors);
        }
      });
    },
    getData() {
      typeEnum().then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          this.operateList = result.data;
          this.leftClick(0, this.operateList[0].item1);
        }else{
          this.$message.error(result.errors);
        }
      });
    },
    // 平台匹配模块
    itemClick(val){
      if (val.menuName === '智能安全帽监测') {
        // 智能安全帽需要跳转https域名（因为高级安全帽视频语音功能）
        getTokenNew().then((res) => {
          let {
            data: { data, statusCode },
          } = res;
          if (data && statusCode === 200) window.open(val.menuUrl, data);
        });
      } else{
        window.open(val.menuUrl)
      }
    },
    // 获取列表
    selectInfo() {
      let params = {
        type: this.myForm.type,
        menuId: this.myForm.menuId,
        keyContent: this.myForm.keyContent
      };
      getList(params).then((res) => {
        let result = res.data;
        if (result.statusCode == 200) {
          this.listData = result.data || [];
        }else{
          this.$message.error(result.errors);
        }
      });
    },
    // 页码
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.selectInfo();
    },
    // 页数
    handleSizeChange(val) {
      this.pagination.currentPage = 1;
      this.pagination.pageSize = val;
      this.selectInfo();
    },
  },
};
</script>
<style lang="scss">
.isAuth{
  text-decoration: underline;
  cursor: pointer;
}
.isAuth:hover{
  text-decoration: none;
  color: #00ffde;
}
.unAuth{
  color: #a4a4a4;
  cursor: pointer;
}
 .snapStyles{
  display: inline-block;
  .snapType{
  display: inline;
  width: auto;
  background-color: #AAAAAA;
  margin-right: 10px;
  word-break: keep-all;
  padding: 2px 0 2px 4px;
  .el-icon-close{
    font-size: 14px !important;
    padding: 2px;
  }
 }
 }

.contentStyle {
  background: #0E2E67;
  box-sizing: border-box;
}
.device-management-wrap {
  display: flex;
  .flex-align {
    display: flex;
    align-items: center;
  }

  .title {
    font-size: 16px;
    font-weight: 600;
    height: 42px;
    background: linear-gradient(270deg, #103594 0%, #1e57b2 100%);
    padding: 0 16px;
    line-height: 42px;
  }

  .first-title::before {
    content: "";
    display: inline-block;
    width: 8px;
    height: 16px;
    background-color: #61d2f7;
    margin-right: 10px;
  }

  .left {
    width: 350px;
    // border: 1px solid #365f7d;
    padding:20px ;

    .title {
      color: #fff;
      height: 42px;
      padding: 0 16px;
      line-height: 42px;
      font-weight: 600;
      border-bottom: 2px solid #365f7d;
      display: flex;
      align-items: center;
      padding: 0 20px;
      justify-content: space-between;
    }

    .item {
      height: 50px;
      // border-bottom: 1px solid #365f7d;
      display: flex;
      align-items: center;
      padding: 0 20px;
      margin:10px 20px;
      justify-content: space-between;
      cursor: pointer;
    }

    // .item:hover {
    //   background-color: #1890FF;
    // }

    .isactive {
      background-color: #295AEA;
    }

    .point {
      height: 10px;
      width: 5px;
      background: #61d2f7;
    }

    .statusNo {
      margin: 0 20px;
      width: 100%;
    }

    // .status {
    //   padding: 5px 10px;
    //   // border-radius: 20px;
    //   // border: 1px solid #365f7d;
    //   display: inline-block;
    // }
    .status {
      padding: 5px 10px;
      // border-radius: 20px;
      // border: 1px solid #365f7d;
      display: inline-block;
      float: right;
    }
    .status img {
      display: flex;
      align-items: center;
    }
    .errorlevel {
      padding: 5px 10px;
      border-radius: 20px;
      border: 1px solid #797979;
      background: #797979;
      // margin-left: 90px;
      margin-right: 20px;
      width: 35px;
      //  float: right;
      // display: inline-block;
      float: right;
    }

    .rightlevel {
      padding: 5px 10px;
      border-radius: 20px;
      float: right;
      // margin-right: 40px;
      // margin-left: 40px;
      margin-right: 20px;
      color: #61d2f7;
      border: 1px solid #61d2f7;
      width: 35px;
      // display: inline-block;
    }

    .view {
      width: 50px;
      color: #61d2f7;
      // float: right;
      display: inline-block;
      //  margin-right:20px;
    }

    .text {
      cursor: pointer;
      font-size: 16px;
    }

    .del {
      width: 50px;
      float: right;
      // margin-right:20px;
    }
  }

  .middle {
    flex: 1;
    min-height: 750px;
    border: 1px solid #365f7d;
    margin: 0 30px;
    padding: 20px;
  }
}

</style>
