<template>
    <el-row>
        <el-col :span="24" class="list-table">
            <el-table
                    :show-summary="showSummary"
                    :summary-method="getSum"
                    :filter-multiple='false'
                    :clearSelection="clearSelection"
                    ref="listTable"
                    :data="tableData"
                    :key="tableKey"
                    style="width: 100%"
                    :height="tabHeight"
                    @sort-change="sortChange"
                    @filter-change="filterChange"
                    :min-height="minHeight"
                    :border="border"
                    :stripe="stripe"
                    :highlight-current-row="highlightCurrentRow"
                    @row-click="handleRowClick"
                    @current-change="currentRowChange"
                    @selection-change="selectionChange"
                    :default-sort = "defaultSort"
                    :row-class-name="tableRowClassName?tableRowClassName:selfTableRowClassName">
                <!--支持多选 col-->
                <div v-if="mulSelect">
                    <el-table-column
                            align="left"
                            type="selection"
                            :fixed="mulSelectFixed"
                            width="60">
                    </el-table-column>
                </div>
                <slot v-for="(column,colIndex) in columns" :name="column.code">
                    <el-table-column
                            v-if="!column.isShow"
                            :show-overflow-tooltip="column.showOverflowTootip || true"
                            :key="column.id"
                            :align='column.align || "center"'
                            :fixed="column.fixed"
                            :prop="column.code"
                            :label="column.label"
                            :sortable="column.sortable"
                            :filters="column.filters"
                            :filter-placement="column.filterPlacement"
                            :filter-multiple="column.filters?column.filterMultiple:null"
                            :render-header="column.tooltip?showTooltip.bind(null,column.tooltip):null"
                            :min-width="column.width"
                            :width="column.minWidth">
                        <template slot-scope="scope">
                            <span v-if="scope.row[children] && scope.row[children].length>0 && colIndex==0" class="hht-expand-icon" @click= "iconClick(scope)">
                            	<a href="javascript:void(0)">
                            		<i v-if ="scope.row[children][0].index == 'noData' " class="el-icon-minus"></i>
                                    <i v-else :class="scope.row.rowStatus == 'minus'?'el-icon-minus':'el-icon-plus'"></i>
                            	</a>
                            </span>
                            <!--按钮类型-->
                            <span v-else-if="scope.row[plusKey] && colIndex==0" style="padding-left:40px"></span>
                            <span v-else-if="colIndex==0" style="padding-left:27px"></span>
                            <template v-if="column.type == 'button'">
                                <span v-for="(opt,index) in column.opts"  :key="index+column.code" style="padding-right:5px;">
                                <el-button  v-if="opt.btnType!='more' && !opt.btnShow"
                                            @click="opt.clickEvent?opt.clickEvent(scope.row,opt,opt.param):buttonClick(scope.row,opt)"
                                            :type="opt.type" :size="opt.size"
                                            >{{opt.name}}</el-button>
                                </span>
                                <span v-for="(opt,index) in column.opts"  :key="index" style="padding-right:5px;">
                                <el-dropdown :tigger="opt.tigger ? opt.tigger : 'hover'" @command="opt.clickEvent?opt.clickEvent($event,scope.row,opt):buttonClick($event,scope.row)" v-if="opt.btnType === 'more' && !opt.btnShow">
                                    <span class="el-dropdown-link">{{opt.name}}<i class="el-icon-caret-bottom el-icon--right"></i></span>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item v-for="item in opt.dropdownItems" :command="item.command" :key="item.param">{{item.name}}</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                                </span>
                            </template>
                            <!--链接类型-->
                            <template v-else-if="column.type == 'link'" class="name-link">
                                <span v-if="column.opt">
                                    <a v-for="(opt) in column.opts"
                                       :key="opt.name"
                                       href="javascript:void(0)"
                                       @click="column.clickEvent?column.clickEvent(scope.row):buttonClick(scope.row,column.type)">
                                    {{opt.name}}
                                    </a>
                                </span>
                                <span v-else>
                                     <a href="javascript:void(0)"
                                        @click="column.clickEvent?column.clickEvent(scope.row):buttonClick(scope.row,column.type)">{{scope.row[column.code]}}</a>
                                </span>
                            </template>
                            <!--序号-->
                            <template v-else-if="column.type == 'linkPost'">
                                <a href="javascript:void(0)"
                                   @click="column.clickEventPost ? column.clickEventPost(scope.row):buttonClick(scope.row,column.type)">{{scope.row[column.code]}}</a>
                            </template>
                            <template v-else-if="column.type == 'num'">
                                <span>{{(scope.$index)+1}}</span>
                            </template>
                            <template v-else-if="column.type == 'enum'">
                            <span v-for="val in column.formatVal" :key="val.value">
                                <span v-if="scope.row[column.code] == val.value">{{val.label}}</span>
                            </span>
                            </template>
                            <!--money千分位-->
                            <template v-else-if="column.type == 'money'">
                            	<span>
                                {{moneyToThousands(scope.row[column.code])}}
                            </span>
                            </template>
                            <template v-else-if="column.type == 'mulLine'">
                                <span v-for="(line,index) in scope.row[column.detailPropty]" class="detail-line" :key="index">
                                    <div v-if="column.detailType == 'enum'">
                                        <span v-for="val in column.formatVal" :key="val.value">
                                            <span v-if="line[column.code] == val.value">{{val.label}}</span>
                                        </span>
                                    </div>
                                    <div v-else>
                                        {{line[column.code] || line[column.code]==0 ? line[column.code]:'　'}}
                                    </div>
                                </span>
                            </template>
                            <template v-else class="cell-font-overflow">
                                <span>{{scope.row[column.code]}}</span>
                            </template>
                        </template>
                    </el-table-column>
                </slot>
            </el-table>
        </el-col>
        <el-col class="table-bottom-page" :span="24" v-if="pagination && pagination.totalSize>0" style="text-align: right;padding-top:20px;">
            <bottom-page :pagination="pagination"
                         v-on:currentChange="handleCurrentChange"
                         v-on:sizeChange="handleSizeChange"></bottom-page>
        </el-col>
    </el-row>
</template>
<script>
    import BottomPage from './BottomPage.vue';
   import {toThousands} from '@/util/util';
    export default {
        components:{
            BottomPage:BottomPage,
            ListColumn:{
                render(h){
                    var parent = this.$parent;
                    return
                }
            }
        },
        props:{
            eleIdName: String,
            sumNum:Array,
            getSummaries:Function,
            plusKey:{
                type:String,
                default:function(){
                }
            },
            keyName:String,
            children:String,
            border:Boolean,
            mulSelect:Boolean,
            mulSelectFixed:String,
            highlightCurrentRow:Boolean,//高亮显示选中的行
            columns:Array,
            dataSource:Array,
            pagination:Object,
            tableRowClassName:Function,
            defaultSort:Object,
            showSummary:Boolean,
            minHeight:Number,
            height:Number,
            isCalHeight:{
                default:true,
                type:Boolean
            },
            extHeight:{
                default:140,
                type:Number
            },
            extTreeHeight:{
                default:40,
                type:Number
            },
            tableKey:String,
            stripe:{
                default:false,
                type:Boolean
            },
            allScreen:{
                default:false,
                type:Boolean
            },
            isHeight:{
                default:false,
                type:Boolean
            }
        },

        data:function() {
            return {
                tableData:[],
                tabHeight:this.height
            }
        },
        watch:{
            dataSource(val){
                this.tableData = val;
                // for(var i = 0;i<this.tableData.length; i++){
                //     if(!this.tableData.key){
                //         this.tableData[i].key = this.generateUUID();
                //     }
                // }
            },
            /*tabHeight:{
                immediate: true,
                handler: function (v, oldv) {
                    if(v>0){
                        this.height = v;
                    }
                }
            }*/
        },

        created:function () {
            this.tableData = this.dataSource;
            if(!this.isHeight){
                this.tabHeight = this.height || 350
            }
            var _self = this;
            if(_self.isCalHeight){
                _self.$nextTick(function () {
                    _self.setDivHeight();
                });
                window.onresize = function () {
                    _self.setDivHeight();
                };
            }
        },
        methods: {
            getSum(param){
                const { columns, data } = param;
                const sums =this.sumNum;
                // columns.forEach((column, index) => {
                //     if (index === 0) {
                //         sums[index] = '合计';
                //         return;
                //     }
                // sums[index]=999
                // const values = data.map(item => Number(item[column.property]));
                // if (!values.every(value => isNaN(value))) {
                //     sums[index] = values.reduce((prev, curr) => {
                //         const value = Number(curr);
                //         if (!isNaN(value)) {
                //             return prev + curr;
                //         } else {
                //             return prev;
                //         }
                //     }, 0);
                //     sums[index] += ' 元';
                // } else {
                //     sums[index] = 'N/A';
                // }
                // });

                return sums;
            },
            generateUUID() {
                var d = new Date().getTime();
                var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                    var r = (d + Math.random()*16)%16 | 0;
                    d = Math.floor(d/16);
                    return (c=='x' ? r : (r&0x3|0x8)).toString(16);
                });
                return uuid;
            },
            //icon点击
            iconClick(scope){
                if(scope.row.childDishList[0].index == 'noData'){
                    return false;
                }
                if(this.keyName){
                    if(scope.row.rowStatus == 'minus'){
                        //等于plus时候，点击触发收起事件
                        this.packUp(scope);
                    }else{
                        //触发展开事件
                        this.expand(scope);
                    };
                }else {
                    if(scope.row.rowStatus == 'minus'){
                        //等于plus时候，点击触发收起事件
                        this.deleteClick(scope)
                    }else{
                        //触发展开事件
                        this.addClick(scope);
                    };
                };
            },
            //收起
            packUp(scope){
                for(var i = this.tableData.length-1; i>=0;i--){
                    if(this.plusKey && this.plusKey==this.keyName){
                        if(parseInt(this.tableData[i][this.keyName])==scope.row[this.keyName]){
                            if(this.tableData[i][this.keyName]%1!=0){
                                this.tableData.splice(i,1);
                            }
                        }
                    }else{
                        if(this.tableData[i][this.plusKey] == scope.row[this.keyName]){
                            this.tableData.splice(i,1);
                        }
                    }
                }
                scope.row.rowStatus = "plus";
            },
            //展开
            expand(scope){
                var resultTableData = [];
                var children = scope.row[this.children];
                if(children && children.length>0){
                    var n=0;
                    for(var i = 0;i<=scope.$index; i++){
                        resultTableData.push(this.tableData[i]);
                    }
                    for(var i = 0;i<children.length; i++){
                        // if(!scope.row[this.keyName]){
                        //     console.error("table传入的data必须有唯一的key属性");
                        // };
                        if(this.plusKey==this.keyName){
                            children[i][this.plusKey] = scope.row[this.keyName]+'.'+(++n);
                        }else{
                            children[i][this.plusKey] = scope.row[this.keyName];
                        };
                        resultTableData.push(children[i]);
                    };
                    if(scope.$index<this.tableData.length-1){
                        for(var i = scope.$index+1;i<this.tableData.length; i++){
                            resultTableData.push(this.tableData[i]);
                        };
                    };
                };
                this.tableData = resultTableData;
                scope.row.rowStatus = "minus";
            },
            /**
             * 供外部组件调用
             * 自适应高度方法
             */
            calTableHeight:function () {
                var _self = this;
                _self.$nextTick(function () {
                    _self.setDivHeight();
                });
                window.onresize = function () {
                    _self.setDivHeight();
                };
            },
            /**
             * 计算div的高度
             */
            setDivHeight:function () {
                var _self = this;
                let eleIdHeight = this.eleIdName ? document.getElementById(this.eleIdName).clientHeight : 0
                let navBar = document.getElementById("nav-bar").clientHeight;
                let HeadTop = document.getElementById("HeadTop").clientHeight;
                let bottom = document.getElementById('table-bottom') ? document.getElementById('table-bottom').clientHeight : 0
               if(_self.isCalHeight){
                    var cardHeight = getStore({name:'routeContent'});
                    if(this.allScreen){
                        if(document.getElementById('table-bottom')){
                            _self.tabHeight = document.getElementById('app').clientHeight - bottom;
                        }else{
                            _self.tabHeight = document.body.clientHeight;
                        }
                    }else{
                        _self.tabHeight = parseInt(cardHeight) - navBar - HeadTop - bottom - eleIdHeight - 50;
                    }
                    _self.calElTreeHeight();
                }
            },
            /**
             * 计算列表页面左侧树的高度
             */
            calElTreeHeight:function () {
                var _self = this;
                var treeHeight = _self.tabHeight
                    // +_self.extTreeHeight;
                var listTrees = document.getElementsByClassName("cal-size-list-tree");
                if(listTrees && listTrees.length>0) {
                    for(var i=0;i<listTrees.length;i++){
                        listTrees[i].style.height = treeHeight + 'px';
                    }
                }
                var elTrees = document.getElementsByClassName("el-tree");
                if(elTrees && elTrees.length>0){
                    for(var i=0;i<elTrees.length;i++){
                        elTrees[i].style.height = treeHeight+'px';
                    }
                }
            },

            selfTableRowClassName(row, index) {
                if ((!this.stripe) && ((index+1)%2 === 0)) {
                    return 'positive-row';
                }
                /*else if ((index+1)%4 === 0) {
                    return 'positive-row';
                }*/
                return '';
            },

            /**
             * 多选事件
             * @param val--对应项的对象
             */
            selectionChange:function(val){
                this.$emit('selectionChange',val);
            },
            handleCurrentChange:function(val) {

                this.$emit('currentChange',val);
            },
            handleSizeChange:function (val) {

                this.$emit('sizeChange',val);
            },
            currentRowChange:function(row){
                this.$emit('currentRowChange',row);
            },
            handleRowClick:function(row){
                this.$emit('handleRowClick',row);
            },
            /**
             * 按千分位格式化金额
             * @param val
             * @returns {*|string}
             */
            moneyToThousands:function (val) {
                return toThousands(val);
            },
            /**
             * table 按钮点击事件
             * @param rowdata
             * @param btn
             * @param command
             */
            buttonClick:function (rowdata, btn,command) {
                this.$emit('buttonClick',rowdata, btn,command);
            },
            /**
             *设置默认选中事件
             */
            defaultCheckedRows:function(row){
                var _self = this;
                if (row) {
                    if(_self.$refs.listTable){
                        _self.$refs.listTable.toggleRowSelection(row,true);
                    }
                } else {
                    _self.$refs.listTable.clearSelection();
                }
            },
            /**
             * table 排序事件
             * @param column
             * @param prop
             * @param order
             */
            deleteClick(scope){
                this.$emit("deleteClick",scope);
            },
            addClick(scope){
                this.$emit("addClick",scope);
            },
            sortChange:function(column,prop,order){
                this.$emit("sortChange",column,prop,order);
            },
            filterChange:function(filters){
                this.$emit("filterChange",filters);
            },
            clearSelection:function(){
                if(this.$refs.listTable){
                    this.$refs.listTable.clearSelection();
                }
            },
            showTooltip:function(tooltip,h,event,c){
                var column = event.column;
                return [<span>{column.label}</span>
                    ,<el-tooltip class="column-tooptip" content={tooltip} placement="bottom" popper-class='hht-tooltip'>
                    <i class={"icon iconfont icon-wenhao"}></i>
                    </el-tooltip>];
            }
        }
    }
</script>
<style>
</style>
