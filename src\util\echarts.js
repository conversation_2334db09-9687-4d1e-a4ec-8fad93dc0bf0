import * as echarts from "echarts";
import { call } from "file-loader";
// 入参说明：
// 1. data 初始数据对象数组
// 2. dom 所挂载的dom id名
// 3. pernum 指定一屏需展示的数据量，利用它自动计算出数据窗口范围的结束百分比，最终一屏展示的数据量可能略有误差
// 4.encode 指定x,y轴需要展示的数据所在字段，形如{x: 'name', y: 'total'}
//柱状图
export const drawBar = function(...params) {
  let dom = params[0].dom,
    data = params[0].data || [],
    encode = params[0].encode || {},
    pernum = params[0].pernum || 5,
    callback = params[0].callback;
  // 计算数据窗口范围的结束百分比
  let dataZoomEnd = (pernum / data.length) * 100;
  let id = document.getElementById(dom);
  let myChart = echarts.init(id);
  let option = {
    // 提示框组件配置
    tooltip: {
      // 自定义框内内容
      formatter(params) {
        let value = params.value[params.dimensionNames[params.encode.y[0]]];
        let wrapper =
          "display: flex;justify-content: space-between;align-items: center;";
        let point =
          "display: inline-block;width: 10px;height: 10px;background: orange;border-radius: 50%;margin-right: 5px;";
        let tipContent = `<div style="${wrapper}"><div style='${point}'></div>销售额<br/>￥${value}</div>`;
        return tipContent;
      },
      borderColor: "#dadada",
      borderWidth: "1",
      backgroundColor: "white",
      textStyle: { color: "black" },
      padding: [5, 10],
      position: "inside"
    },
    // x轴配置
    xAxis: {
      // 坐标轴类型
      type: "category",
      // 坐标轴轴线相关设置
      axisLine: { show: true },
      // 坐标轴刻度相关设置
      axisTick: false
    },
    yAxis: {
      type: "value",
      axisLine: { show: true },
      axisTick: false,
      // 坐标轴在 grid 区域中的分隔线
      splitLine: { lineStyle: { type: "dashed" } }
    },
    // 数据集配置，数据可以单独管理，被多个组件复用，并且可以自由指定数据到视觉的映射
    dataset: {
      source: data
    },
    // 系列配置
    series: [
      {
        type: "bar",
        showBackground: false,
        color: "#61a0a8",
        // 柱条的宽度,百分数基于自动计算出的每一类目的宽度
        barWidth: "40%",
        // 柱条的最大宽度
        barMaxWidth: 60,
        encode
      }
    ],
    // 数据区域缩放组件配置
    dataZoom: [
      {
        type: "inside",
        // 数据窗口范围的起始百分比
        start: 0,
        // 数据窗口范围的结束百分比
        end: dataZoomEnd,
        // 是否锁定选择区域（或叫做数据窗口）的大小，如果设置为 true 则锁定选择区域的大小，也就是说，只能平移，不能缩放
        zoomLock: true
      },
      // 手柄的icon相关配置
      {
        handleIcon:
          "M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",
        handleSize: "50%",
        handleStyle: {
          color: "#294b97",
          shadowBlur: 3,
          shadowColor: "rgba(0, 0, 0, 0.6)",
          shadowOffsetX: 2,
          shadowOffsetY: 2
        }
      }
    ]
  };
  myChart.setOption(option);
  //柱形图点击事件
  if (callback) {
    myChart.on("click", function(params) {
      return callback(params);
    });
  }
};
//基础柱状图
export const drawBar4 = function(...params) {
  let dom = params[0].dom,
    data = params[0].data || [],
    encode = params[0].encode || {},
    pernum = params[0].pernum || 5,
    callback = params[0].callback,
    xAxisData = params[0].xAxisData,
    seriesData = params[0].seriesData;

  // 计算数据窗口范围的结束百分比
  let dataZoomEnd = (pernum / data.length) * 100;
  let id = document.getElementById(dom);
  let myChart = echarts.init(id);
  let option = {
    xAxis: {
      type: "category",
      data: xAxisData,
      splitLine: { show: false }, //去除网格线
      // splitArea : {show : true},//保留网格区域
      axisLine: {
        lineStyle: {
          type: "solid",
          color: "#fff", //左边线的颜色
          width: "1" //坐标线的宽度
        }
      },
      axisLabel: {
        formatter: function(value) {
          let res = value;
          if (res.length > 5) {
            res = res.substring(0, 4) + "..";
          }
          return res;
        },
        textStyle: {
          color: "#fff" //坐标值得具体的颜色
        }
      }
    },
    yAxis: {
      type: "value",
      splitLine: { show: true }, //去除网格线
      // splitArea : {show : true},//保留网格区域
      axisLine: {
        lineStyle: {
          type: "solid",
          color: "#fff", //左边线的颜色
          width: "1" //坐标线的宽度
        }
      },
      axisLabel: {
        textStyle: {
          color: "#fff" //坐标值得具体的颜色
        }
      }
    },
    series: [
      {
        data: seriesData,
        type: "bar"
      }
    ]
  };
  myChart.setOption(option);
  //柱形图点击事件
  if (callback) {
    myChart.on("click", function(params) {
      return callback(params);
    });
  }
};
//柱状图纵向堆叠
export const drawBar2 = function(...params) {
  console.log("params[0]====", JSON.stringify(params[0], null, 4));
  let data1 = params[0].data1 || [],
    data2 = params[0].data2 || [],
    data3 = params[0].data3 || [];
  let dom = params[0].dom,
    data = params[0].data || [],
    encode = params[0].encode || {},
    pernum = params[0].pernum || 5,
    callback = params[0].callback;
  // 计算数据窗口范围的结束百分比
  let dataZoomEnd = (pernum / data.length) * 100;
  let id = document.getElementById(dom);
  let myChart = echarts.init(id);
  let option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
      }
    },
    legend: {
      data: ["一般隐患", "重大隐患"]
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: data1
      }
    ],
    yAxis: [
      {
        type: "value"
      }
    ],
    series: [
      {
        name: "一般隐患",
        type: "bar",
        stack: "广告",
        emphasis: {
          focus: "series"
        },
        data: data2
      },
      {
        name: "重大隐患",
        type: "bar",
        stack: "广告",
        emphasis: {
          focus: "series"
        },
        data: data3
      }
    ]
  };

  myChart.setOption(option);
  //柱形图点击事件
  if (callback) {
    myChart.on("click", function(params) {
      return callback(params);
    });
  }
};
//柱状图横向堆叠
export const drawBar3 = function(...params) {
  let dom = params[0].dom,
    data = params[0].data || [],
    encode = params[0].encode || {},
    pernum = params[0].pernum || 5,
    callback = params[0].callback;
  // 计算数据窗口范围的结束百分比
  let dataZoomEnd = (pernum / data.length) * 100;
  let id = document.getElementById(dom);
  let myChart = echarts.init(id);
  let option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        // Use axis to trigger tooltip
        type: "shadow" // 'shadow' as default; can also be 'line' or 'shadow'
      }
    },
    // legend: {},
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "value"
    },
    yAxis: {
      type: "category",
      data: data
    },
    series: [
      {
        name: "Direct",
        type: "bar",
        stack: "total",
        label: {
          show: true
        },
        emphasis: {
          focus: "series"
        },
        data: [320, 302, 301, 334, 390, 330, 320]
      },
      {
        name: "Mail Ad",
        type: "bar",
        stack: "total",
        label: {
          show: true
        },
        emphasis: {
          focus: "series"
        },
        data: [120, 132, 101, 134, 90, 230, 210]
      },
      {
        name: "Affiliate Ad",
        type: "bar",
        stack: "total",
        label: {
          show: true
        },
        emphasis: {
          focus: "series"
        },
        data: [220, 182, 191, 234, 290, 330, 310]
      },
      {
        name: "Video Ad",
        type: "bar",
        stack: "total",
        label: {
          show: true
        },
        emphasis: {
          focus: "series"
        },
        data: [150, 212, 201, 154, 190, 330, 410]
      },
      {
        name: "Search Engine",
        type: "bar",
        stack: "total",
        label: {
          show: true
        },
        emphasis: {
          focus: "series"
        },
        data: [820, 832, 901, 934, 1290, 1330, 1320]
      }
    ]
  };
  myChart.setOption(option);
  //柱形图点击事件
  if (callback) {
    myChart.on("click", function(params) {
      return callback(params);
    });
  }
};
// 饼图
export const drawPie = function(...params) {
  let dom = params[0].dom,
    data = params[0].data,
    nameTitle = params[0].nameTitle,
    radius = params[0].radius || "50%",
    colorArray = params[0].colorArray || [],
    callback = params[0].callback,
    total = params[0].total;
  let id = document.getElementById(dom);
  let myChart = echarts.init(id);
  let option = {
    title: {
      text: total ? total : nameTitle,
      left: "center",
      top: total ? "center" : "top",
      textStyle: {
        color: "#fff"
      }
    },
    tooltip: {
      trigger: "item",
      confine: true
    },
    legend: {
      orient: "vertical",
      left: "right",
      textStyle: {
        color: "#fff"
      }
    },
    series: [
      {
        name: nameTitle,
        type: "pie",
        radius: radius,
        data: data,
        itemStyle: {
          normal: {
            borderWidth: 1, // 每一个类型之间空隙
            borderColor: "#fff"
            // color: function (params) {
            //   const colorList = colorArray
            //   return colorArray.length > 0 ? colorList[params.dataIndex] : '#EA8187'
            // }
          }
        },
        emphasis: {
          itemStyle: {
            color: "#fff",
            borderWidth: 1,
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(255, 255, 255, 0.5)"
          }
        }
      }
    ]
  };
  myChart.setOption(option);
  if (callback) {
    myChart.on("click", function(params) {
      return callback(params);
    });
  }
};
// 饼图(占比)
export const drawPie2 = function(...params) {
  let dom = params[0].dom,
    data1 = params[0].data1,
    data2 = params[0].data2,
    nameTitle = params[0].nameTitle,
    colorArray = params[0].colorArray || [],
    callback = params[0].callback;
  let id = document.getElementById(dom);
  let myChart = echarts.init(id);
  let option = {
    title: {
      text: nameTitle,
      // subtext: nameTitle,
      left: "center"
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)"
    },
    legend: {
      bottom: 10,
      left: "center",
      data: data1
    },
    series: [
      {
        type: "pie",
        radius: "65%",
        center: ["50%", "50%"],
        selectedMode: "single",
        data: data2,
        emphasis: {
          itemStyle: {
            color: "#fff",
            borderWidth: 1,
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        }
      }
    ]
  };
  myChart.setOption(option);
  if (callback) {
    myChart.on("click", function(params) {
      return callback(params);
    });
  }
};
// 环图(占比)
export const drawRing = function(...params) {
  let dom = params[0].dom,
    data = params[0].data,
    nameTitle = params[0].nameTitle,
    colorArray = params[0].colorArray || [],
    callback = params[0].callback;
  titleX = params[0].titleX;
  let id = document.getElementById(dom);
  let myChart = echarts.init(id);
  let option = {
    tooltip: {
      trigger: "item"
    },
    series: [
      {
        name: "环境监测",
        type: "pie",
        radius: ["60%", "70%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          // borderColor: '#fff',
          borderWidth: 1
        },
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "40",
            fontWeight: "bold",
            fontColor: "#fff"
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  };
  myChart.setOption(option);
  if (callback) {
    myChart.on("click", function(params) {
      return callback(params);
    });
  }
};
// 环图(没有空格)
export const drawRing2 = function(...params) {
  //console.log(params);
  let dom = params[0].dom,
    data = params[0].data,
    nameTitle = params[0].nameTitle,
    subTitle = params[0].subTitle,
    center = params[0].center,
    show = params[0].show,
    // legend = params[0].legend,
    titleX = params[0].titleX,
    colorArray = params[0].colorArray || [],
    callback = params[0].callback;
  let id = document.getElementById(dom);
  let myChart = echarts.init(id);
  let option = {
    tooltip: {
      trigger: "item"
    },
    title: {
      show: show,
      text: nameTitle,
      subtext: subTitle,
      x: titleX,
      y: "center",
      textAlign: "center",
      textStyle: {
        //文字颜色
        color: "#fff",
        fontStyle: "normal",
        fontWeight: "bold"
      },
      subtextStyle: {
        color: "#fff",
        fontSize: 24
      }
    },
    // legend: {
    //   x: legend,
    //   y: "center",
    //   orient: "vertical",
    //   type: 'scroll',
    //   textStyle: {
    //     //图例文字的样式
    //     color: "#ccc",
    //     fontSize: 16
    //   },
    //   formatter: function (value) {
    //     let res = value;
    //     if (res.length > 5) {
    //       res = res.substring(0, 4) + "..";
    //     }
    //     return res;
    //   },
    //   // padding: -20
    // },
    // 设置环形中间的数据
    //   graphic: [{
    //     type: 'text',
    //     left: '34%',
    //     top: '55%',
    //     z: 10,
    //     style: {
    //         fill: '#1a1a1a',
    //         text: gailanTotal,
    //         font: '16px Microsoft YaHei'
    //     }
    // }],
    series: [
      {
        // name: "环境监测",
        type: "pie",
        radius: ["50%", "70%"],
        center: center || ["35%", "50%"],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: "center"
        },
        // emphasis: {
        //   label: {
        //     show: true,
        //     fontSize: "30",
        //     fontWeight: "bold"
        //   }
        // },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  };
  myChart.setOption(option);
  window.onresize = myChart.resize;
  if (callback) {
    myChart.on("click", function(params) {
      return callback(params);
    });
  }
};
// 折线图
/**
 * isMoreLine 是否多条折线
 * seriesData 类型 Object(单折线) || Array（多折线） 若为Array 则 isMoreLine 必为true
 * **/
export const drawLine = function(...params) {
  let xAxisData = params[0].xAxisData,
    seriesData = params[0].seriesData;
  let callback = params[0].callback;
  let isMoreLine = params[0].isMoreLine || false;
  let newLegend = (function() {
    let arr = [];
    if (isMoreLine) {
      seriesData.forEach(ele => {
        arr.push(ele.name);
      });
    } else {
      arr.push(seriesData.name);
    }
    return arr;
  })();
  let legendData = params[0].legendData || newLegend;
  let dom = params[0].dom;
  let pernum = params[0].pernum || 5;
  let toolboxShow = params[0].toolboxShow || false;
  let nameTitle = params[0].nameTitle;
  let dataZoomEnd = (pernum / xAxisData.length) * 10;
  let id = document.getElementById(dom);
  let myChart = echarts.init(id);
  let option = {
    title: {
      text: nameTitle,
      left: "center",
      textStyle: {
        color: "#fff"
      }
    },
    tooltip: {
      trigger: "axis"
    },
    legend: {
      data: legendData,
      // left: 'right',
      orient: "horizontal", // 'vertical'
      x: "right", // 'center' | 'left' | {number},
      y: 10, // 'center' | 'bottom' | {number}
      // backgroundColor: '#fff',
      // borderColor: 'rgba(178,34,34,0.8)',
      // borderWidth: 4,
      padding: 10, // [5, 10, 15, 20]
      itemGap: 20,
      textStyle: { color: "#fff" }
      // selected: {
      //     '降水量': false
      // },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "10%",
      containLabel: true
    },
    toolbox: {
      show: toolboxShow,
      feature: {
        saveAsImage: {}
      }
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      axisLine: { show: true },
      data: xAxisData
    },
    yAxis: {
      type: "value",
      axisLine: { show: true }
    },
    series: (function() {
      let obj = {
        type: "line",
        stack: "总量"
      };
      let result = [];
      if (isMoreLine) {
        seriesData.forEach(ele => {
          result.push(Object.assign({}, obj, ele));
        });
      } else {
        result.push(Object.assign({}, obj, seriesData));
      }
      return result;
    })(),
    // 缩放平移组件
    dataZoom: [
      {
        type: "inside", //slider有滑块，inside内置
        disabled: false, //是否停止组件功能
        xAxisIndex: 0, //x轴,可以用数组表示多个轴
        zoomLock: true, //是否锁定区域大小（true,只能平移不能缩放）
        preventDefaultMouseMove: false,
        filterMode: "empty",
        startValue: 0, //一行有几个（起始数组下标）
        endValue: dataZoomEnd, //一行有几个（结束数组下标）
        start: null,
        end: null
      },
      {
        handleIcon:
          "M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",
        handleSize: "50%",
        handleStyle: {
          color: "#294b97",
          shadowBlur: 3,
          shadowColor: "rgba(0, 0, 0, 0.6)",
          shadowOffsetX: 2,
          shadowOffsetY: 2
        }
      }
    ]
  };
  myChart.setOption(option);
  if (callback) {
    myChart.on("click", function(params) {
      return callback(params);
    });
  }
};
//折线图纵向堆叠
export const drawLine2 = function(...params) {
  // console.log("params[0]====", JSON.stringify(params[0], null, 4));
  // let data1 = params[0].data1 || [],
  //   data2 = params[0].data2 || [],
  //   data3 = params[0].data3 || []
  let dom = params[0].dom,
    data = params[0].data || [],
    encode = params[0].encode || {},
    pernum = params[0].pernum || 5,
    callback = params[0].callback,
    xAxisData = params[0].xAxisData,
    seriesData = params[0].seriesData;

  // 计算数据窗口范围的结束百分比
  let dataZoomEnd = (pernum / data.length) * 100;
  let id = document.getElementById(dom);
  let myChart = echarts.init(id);
  let option = {
    title: {
      // text: 'Stacked Line'
    },
    tooltip: {
      trigger: "axis"
    },
    legend: {
      data: ["重大隐患", "一般隐患"],
      textStyle: { color: "#fff" }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    // toolbox: {
    //   feature: {
    //     saveAsImage: {}
    //   }
    // },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: xAxisData,
      splitLine: { show: false }, //去除网格线
      // splitArea : {show : true},//保留网格区域
      axisLine: {
        lineStyle: {
          type: "solid",
          color: "#fff", //左边线的颜色
          width: "1" //坐标线的宽度
        }
      },
      axisLabel: {
        textStyle: {
          color: "#fff" //坐标值得具体的颜色
        }
      }
    },
    yAxis: {
      type: "value",
      splitLine: { show: true }, //去除网格线
      // splitArea : {show : true},//保留网格区域
      axisLine: {
        lineStyle: {
          type: "solid",
          color: "#fff",
          width: "1"
        }
      },
      axisLabel: {
        textStyle: {
          color: "#fff"
        }
      }
    },
    series: seriesData
  };
  myChart.setOption(option);
  //柱形图点击事件
  if (callback) {
    myChart.on("click", function(params) {
      return callback(params);
    });
  }
};
//仪表盘
export const drawGauge = function(...params) {
  let dom = params[0].dom,
    data = params[0].data || [],
    encode = params[0].encode || {},
    pernum = params[0].pernum || 5,
    callback = params[0].callback;
  // 计算数据窗口范围的结束百分比
  let dataZoomEnd = (pernum / data.length) * 100;
  let id = document.getElementById(dom);
  let myChart = echarts.init(id);
  let option = {
    series: [
      {
        type: "gauge",
        radius: "100%",
        center: ["50%", "60%"],
        axisLine: {
          lineStyle: {
            width: 8,
            color: [
              [0.1, "#96A66C"],
              [0.3, "#EFEF69"],
              [0.4, "#D5973F"],
              [0.6, "#AF1D18"],
              [0.7, "#7A2646"],
              [1, "#311016"]
            ]
          }
        },
        pointer: {
          itemStyle: {
            color: "#96A66C"
          }
        },
        axisTick: {
          distance: -30,
          length: 0,
          lineStyle: {
            color: "#fff",
            width: 5
          }
        },
        splitLine: {
          distance: -30,
          length: 0,
          lineStyle: {
            color: "#fff",
            width: 5
          }
        },
        axisLabel: {
          color: "#fff",
          distance: 40,
          fontSize: 10
        },
        detail: {
          valueAnimation: true,
          formatter: "{value}",
          color: "#fff",
          fontSize: 12
        },
        data: [
          {
            value: data[0].value
          }
        ]
      }
    ]
  };
  // setInterval(function () {
  //   myChart.setOption({
  //     series: [
  //       {
  //         data: [
  //           {
  //             value: +(Math.random() * 100).toFixed(2)
  //           }
  //         ]
  //       }
  //     ]
  //   });
  // }, 2000);
  myChart.setOption(option);
  window.onresize = myChart.resize;
  //柱形图点击事件
  if (callback) {
    myChart.on("click", function(params) {
      return callback(params);
    });
  }
};
