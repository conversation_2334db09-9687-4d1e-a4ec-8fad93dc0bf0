<!--
 * @Description: 配电箱监测
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2024-02-26 10:56:31
 * @LastEditors: Jky200guo <EMAIL>
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div class="box" ref="DistributionBoxRef">
        <div
          id="DistributionBoxChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawBarLineTotal } from "@/components/constructionRecord/Echarts/echartsOne.js";
import { getPowerBox } from "@/api/echrtsApi";
export default {
  components: {},
  name: "DistributionBox",
  props: {
    moduleName: String,
  },
  watch: {
    "$i18n.locale"(val) {
      if (val && this.$IsProjectShow) {
        this.languageChange();
      }
    },
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      barParams: {
        dom: "DistributionBoxChart",
        xAxisData: [],
        seriesData: [],
        boundaryGap: true,
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        unit: "次",
        yminInterval: 1,
        axisLabelFormatter: function (value) {
          var res = value;
          if (res.length > 3) {
            res = res.substring(0, 3) + "..";
          }
          return res;
        },
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    if (this.$IsProjectShow) {
      this.languageChange();
    } else {
      this.getBarData();
    }
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    languageChange() {
      this.getBarData();
    },
    setEchartsWidth() {
      this.barWidth = this.$refs.DistributionBoxRef.offsetWidth - 40;
      this.barHeight = this.$refs.DistributionBoxRef.offsetHeight;
    },
    getBarData() {
      getPowerBox(this.projectId)
        .then((res) => {
          const { data, statusCode } = res.data;
          if (statusCode == 200) {
            let xName = [];
            let yData = [];
            if (data.length > 0) {
              data.forEach((ele) => {
                // if(this.$IsProjectShow){
                if (ele.name == "接地") {
                  ele.name = this.$t(`grounding`);
                }
                if (ele.name == "断开") {
                  ele.name = this.$t("break");
                }
                if (ele.name == "烟感") {
                  ele.name = this.$t("SmokeDetector");
                }
                if (ele.name == "高温") {
                  ele.name = this.$t("hightemperature");
                }
                if (ele.name == "短路") {
                  ele.name = this.$t("shortcircuit");
                }
                if (ele.name == "漏电") {
                  ele.name = this.$t("leakageofelectricity");
                }
                if (ele.name == "过载") {
                  ele.name = this.$t("overload");
                }
                if (ele.name == "断电") {
                  ele.name = this.$t("outage");
                }
                // }
                xName.unshift(ele.name);
                yData.unshift(ele.value);
              });
            }
            let seriesData = [
              {
                data: yData,
                type: "bar",
                itemStyle: {
                  normal: {
                    color: "#d59b5d",
                  },
                },
                label: {
                  normal: {
                    show: true,
                    position: "top",
                    textStyle: {
                      fontSize: 10,
                    },
                    formatter: function (num) {
                      return num.value > 0 ? num.value : "";
                    },
                  },
                },
              },
            ];
            this.barParams.xAxisData = xName;
            this.barParams.seriesData = seriesData;
            drawBarLineTotal(this.barParams);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
}
</style>
