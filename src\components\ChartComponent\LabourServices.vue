<!--
 * @Description: 劳务管理
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-24 15:49:08
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="LabourServicesRef"
      >
        <!-- <div
          id="LabourServicesChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div> -->
        <div class="labour_content">
          <div class="item">
            <img src="../../assets/customize/labour.png">
            <p class="num"><span>56</span>个</p>
            <p>今日出勤数</p>
          </div>
          <div class="item">
            <img src="../../assets/customize/labour.png">
            <p class="num"><span>98</span>%</p>
            <p>今日出勤率</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>

import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getLabourServices } from "@/api/echrtsApi";
export default {
  components: {},
  name: "LabourServices",
  props: {
    moduleName: String
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "LabourServicesChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: "总人数",
        seriesCenter: ["25%", "50%"],
        titleInfor: {
          text: "0人",
          subtext: "今日出勤总人数",
          x: "30%",
          y: "30%",
          textAlign: "center",
          textStyle: {
            color: "#fff",
            fontSize: 20
            // fontStyle: 'bolder',
          },
          subtextStyle: {
            color: "#fff",
            fontSize: 12
          }
          // left: 'center',
        },
        tooltipFormatter: function (infor) {
          let msg = `${infor.marker}${infor.data.name}
              <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
              <b>${infor.data.value}</b>人`;
          return msg;
        },
        costomLegendFormatter: function (name) {
          return name;
        },
      },
      arr: [],
      language: 'zh',
      laborlist: [{ dickey: "砌筑工", dicvlue: "Bricklayer", extendJson: null },
      { dickey: "钢筋工", dicvlue: "Steel fixer", extendJson: null },
      { dickey: "钢结构", dicvlue: "Steel structure", extendJson: null },
      { dickey: "抹灰工", dicvlue: "Plasterer", extendJson: null },
      { dickey: "架子工", dicvlue: "Scaffolder", extendJson: null },
      { dickey: "材料工", dicvlue: "Material worker", extendJson: null },
      { dickey: "混凝土工", dicvlue: "Concrete worker", extendJson: null },
      { dickey: "岩土勘探工", dicvlue: "Geotechnical exploration worker", extendJson: null },
      { dickey: "模板工", dicvlue: "Form fixer", extendJson: null },
      { dickey: "灌浆工", dicvlue: "Grouting worker", extendJson: null },
      { dickey: "机械设备安装工", dicvlue: "Mechanical equipment installation worker", extendJson: null },
      { dickey: "排水工", dicvlue: "Drainage worker", extendJson: null },
      { dickey: "通风工", dicvlue: "Ventilation Worker", extendJson: null },
      { dickey: "安装起重工", dicvlue: "Installation crane worker", extendJson: null },
      { dickey: "保温工", dicvlue: "Insulation worker", extendJson: null },
      { dickey: "安装钳工", dicvlue: "Installation fitter", extendJson: null },
      { dickey: "环卫道路清扫保洁工", dicvlue: "Environmental sanitation road cleaning worker", extendJson: null },
      { dickey: "电气设备安装调试工", dicvlue: "Electrical equipment installation and commissioning worker", extendJson: null },
      { dickey: "管道工", dicvlue: "Plumber", extendJson: null },
      { dickey: "变电安装工", dicvlue: "Substation installation worker", extendJson: null },
      { dickey: "建筑电工", dicvlue: "Construction electrician", extendJson: null },
      { dickey: "司泵工", dicvlue: "Pump driver", extendJson: null },
      { dickey: "挖掘铲运和桩工机械司机", dicvlue: "Excavation, shoveling, and piling machinery drivers", extendJson: null },
      { dickey: "桩机操作工", dicvlue: "Pile driver operator", extendJson: null },
      { dickey: "起重信号工", dicvlue: "Crane signal worker", extendJson: null },
      { dickey: "塔吊司机（起重工）", dicvlue: "Tower crane driver (crane operator)", extendJson: null },
      { dickey: "建筑起重机械安装拆卸工", dicvlue: "Construction crane installation and dismantling worker", extendJson: null },
      { dickey: "泵车", dicvlue: "Pump", extendJson: null },
      { dickey: "装饰装修工", dicvlue: "Decoration and renovation workers", extendJson: null },
      { dickey: "室内成套设施安装工", dicvlue: "Indoor complete facility installation worker", extendJson: null },
      { dickey: "建筑门窗幕墙安装工", dicvlue: "Installation worker for building doors, windows, and curtain walls", extendJson: null },
      { dickey: "幕墙制作工", dicvlue: "Curtain wall maker", extendJson: null },
      { dickey: "防水工", dicvlue: "Waterproof-Worker", extendJson: null },
      { dickey: "水电工", dicvlue: "Plumber and electrician", extendJson: null },
      { dickey: "木工", dicvlue: "Carpentry", extendJson: null },
      { dickey: "喷砼工", dicvlue: "Shotcrete worker", extendJson: null },
      { dickey: "石工", dicvlue: "Masonry", extendJson: null },
      { dickey: "电焊工", dicvlue: "Electric welder", extendJson: null },
      { dickey: "爆破工", dicvlue: "Blasting worker", extendJson: null },
      { dickey: "除尘工", dicvlue: "Duster", extendJson: null },
      { dickey: "测量放线工", dicvlue: "Surveying and laying out workers", extendJson: null },
      { dickey: "线路架设工", dicvlue: "Line installation worker", extendJson: null },
      { dickey: "古建筑传统石工", dicvlue: "Traditional stonework in ancient architecture", extendJson: null },
      { dickey: "古建筑传统瓦工", dicvlue: "Traditional bricklayers in ancient architecture", extendJson: null },
      { dickey: "古建筑传统彩画工", dicvlue: "Traditional Colored Painters of Ancient Buildings", extendJson: null },
      { dickey: "古建筑传统木工", dicvlue: "Traditional carpentry in ancient architecture", extendJson: null },
      { dickey: "古建筑传统油工", dicvlue: "Traditional oil workers in ancient architecture", extendJson: null },
      { dickey: "金属工", dicvlue: "Metal worker", extendJson: null },
      { dickey: "杂工", dicvlue: "Handyman", extendJson: null },
      { dickey: "普工", dicvlue: "General workers", extendJson: null },
      { dickey: "建材试验工", dicvlue: "Building Materials Testing Worker", extendJson: null },
      { dickey: "工长", dicvlue: "Foreman", extendJson: null },
      { dickey: "帮厨", dicvlue: "Kitchen helper", extendJson: null },
      { dickey: "主检", dicvlue: "Main inspection", extendJson: null },
      { dickey: "管理人员", dicvlue: "Administrative staff", extendJson: null },
      { dickey: "其它", dicvlue: "Other", extendJson: null }]
    };
  },
  watch: {
    "$i18n.locale"(val) {
      if (val && this.$IsProjectShow) {
        this.language = val
        this.languageChange();
      }
    },
  },
  created() {
    this.projectId = getStore({
      name: "projectId"
    });
    this.companyId = getStore({
      name: "companyId"
    });
    this.language = getStore({
      name: "language"
    });
    if (this.$IsProjectShow) {
      this.languageChange()
    } else {
      this.getBarData();
    }
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    languageChange() {
      this.pieParams.titleInfor.subtext = this.$t(`Totalattendancetoday`)
      if (this.language == 'en') {
        this.pieParams = {
          dom: "LabourServicesChart",
          data: [],
          nameTitle: null,
          seriesLabel: false,
          subtext: "总人数",
          seriesCenter: ["25%", "50%"],
          titleInfor: {
            text: "0人",
            subtext: this.$t(`Totalattendancetoday`),
            x: "28%",
            y: "30%",
            textAlign: "center",
            textStyle: {
              color: "#fff",
              fontSize: 20
              // fontStyle: 'bolder',
            },
            subtextStyle: {
              color: "#fff",
              fontSize: 12
            }
            // left: 'center',
          },
          tooltipFormatter: function (infor) {
            let msg = `${infor.marker}${infor.data.name}
              <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
              <b>${infor.data.value}</b>people`;
            return msg;
          },
          costomLegendFormatter: function (name) {
            return name;
          },
        }
      } else {
        this.pieParams = {
          dom: "LabourServicesChart",
          data: [],
          nameTitle: null,
          seriesLabel: false,
          subtext: "总人数",
          seriesCenter: ["25%", "50%"],
          titleInfor: {
            text: "0人",
            subtext: this.$t(`Totalattendancetoday`),
            x: "28%",
            y: "30%",
            textAlign: "center",
            textStyle: {
              color: "#fff",
              fontSize: 20
              // fontStyle: 'bolder',
            },
            subtextStyle: {
              color: "#fff",
              fontSize: 12
            }
            // left: 'center',
          },
          tooltipFormatter: function (infor) {
            let msg = `${infor.marker}${infor.data.name}
              <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
              <b>${infor.data.value}</b>人`;
            return msg;
          },
          costomLegendFormatter: function (name) {
            return name;
          },
        }
      }

      this.getBarData()
    },
    setEchartsWidth() {
      this.barWidth = this.$refs.LabourServicesRef.offsetWidth - 40;
      this.barHeight = this.$refs.LabourServicesRef.offsetHeight;
    },
    getBarData() {
      const { projectId } = this;
      getLabourServices({
        projectID: projectId,
        type: 0
      })
        .then(res => {
          const { statusCode, data } = res.data;
          if (statusCode == 200) {
            // console.log(data);
            let piecharValue =
              data[0].piecharValue.length > 0 ? data[0].piecharValue : [];
            let sum = 0;

            // let piecharValue = [
            //   { field: '管理人员', fieldvalues: 3 },
            //   { field: '钢筋工', fieldvalues: 6 },
            //   { field: '起重信号工', fieldvalues: 3 },
            //   { field: '木工', fieldvalues: 7 },
            //   { field: '测量放线工', fieldvalues: 1 },
            //   { field: '混凝土工', fieldvalues: 5 },
            //   { field: '建筑电工', fieldvalues: 3 },
            //   { field: '管道工', fieldvalues: 2 },
            //   { field: '架子工', fieldvalues: 1 },
            //   { field: '模板工', fieldvalues: 2 },
            // ];
            if (piecharValue.length > 0) {
              piecharValue.forEach(element => {
                element.value = element.fieldvalues;
                element.name = element.field;
                sum = Number(sum) + Number(element.fieldvalues);
                delete element.fieldvalues;
                delete element.field;
                if (this.$IsProjectShow && this.language == 'en') {
                  let index = this.laborlist.findIndex(item => {
                    return item.dickey == element.name
                  })
                  element.name = this.laborlist[index].dicvlue
                  // if(element.name=='砌筑工'){
                  //   element.name=this.$t(`Bricklayer`)
                  // }
                  // if(element.name=='工长'){
                  //   element.name=this.$t(`foreman`)
                  // }
                  // if(element.name=='普工'){
                  //   element.name=this.$t(`Generalworkers`)
                  // }
                  // if(element.name=='桩机操作工'){
                  //   element.name=this.$t(`Piledriveroperator`)
                  // }
                  // if(element.name=='管理人员'){
                  //   element.name=this.$t(`administrativestaff`)
                  // }
                  // if(element.name=='杂工'){
                  //   element.name=this.$t(`Handyman`)
                  // }
                  // if(element.name=='电焊工'){
                  //   element.name=this.$t(`electricWelder`)
                  // }
                }
              });
              let legendFormatter = name => {
                const item = piecharValue.find(i => {
                  return i.name === name;
                });
                const p = item.value;
                let newName = name.length > 10 ? name.slice(0, 9) + "..." : name;

                return "{name|" + newName + "}" + "{percent|" + p + "}" + this.$t(`people`);
              };
              this.pieParams.legendFormatter = legendFormatter;
            }

            this.pieParams.titleInfor.text = sum + this.$t(`people`);
            this.pieParams.data = piecharValue;
            console.log(this.pieParams, "劳务管理")
            drawAnnularChart(this.pieParams);
          }
        })
        .catch(() => { });
    }
  }
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.labour_content {
  display: flex;
  .item {
    color: #b4c0cc;
    font-size: 14px;
    padding-top: 30px;
    text-align: center;
    img {
      width: 134px;
    }
    .num {
      padding: 10px 0 8px;
    }
    span {
      padding-right: 5px;
      font-size: 24px;
      font-family: PangMenZhengDaoBiaoTiTiMianFeiBan-4,
        PangMenZhengDaoBiaoTiTiMianFeiBan-4;
      background: linear-gradient(to bottom, #aadaff, #ffffff);
      background-clip: text;
      -webkit-background-clip: text;
      text-fill-color: transparent;
      -webkit-text-fill-color: transparent;
    }
  }
}
</style>
