<!-- 考勤记录头部标签tab -->
<template>
  <div>
    <el-row class="title-type">
      <el-col class="module-type">
        <div class="content_divs bomcondiv">
          <div
            class="leftrows"
            v-if="isShow"
            @click="rowClicks(0)"
          ></div>
          <div
            class="rightrows"
            v-if="isShow"
            @click="rowClicks(1)"
          ></div>
          <div class="ulboxs">
            <div
              class="scroll_lists"
              :class="isShow ? '' : 'width0'"
            >
              <ul
                class="tab_ul"
                id="menu_ul"
                :key="keyTimer"
              >
                <li
                  v-for="item in list"
                  :key="item.value"
                  :class="
                    item.isChecked
                      ? item.name && item.code > 20
                        ? 'actived12'
                        : item.name && item.code >= 12
                        ? 'actived12'
                        : 'actived'
                      : ''
                  "
                  :style="language==='en'?'width:220px !important':''"
                  @click="itemClick(item,1)"
                >
                  <div
                    :class="
                      item.name && item.code > 20
                        ? 'maxSize'
                        : item.name && item.code >= 12
                        ? 'mediumSize'
                        : 'minSize'
                    "
                    :style="language==='en'?'width:220px !important':''"
                  >
                    {{ item.name }}
                  </div>
                  <!-- <div>{{item.num || 0}}</div> -->
                  <!-- <div class="name">{{ item.name }}</div> -->
                </li>
              </ul>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row class="module-type">
      <div class="content_div bomcondiv">
        <div
          class="leftrow"
          @click="rowClick(0)"
        ></div>
        <div
          class="rightrow"
          @click="rowClick(1)"
        ></div>
        <div class="ulbox">
          <div
            class="scroll_list"
            style="left: 0px"
          >
            <ul class="bottombtn">
              <template v-for="item in menuList">
                <li
                  v-if="item.menuUrl.indexOf('http') > -1"
                  :key="item.menuName"
                  title
                  :style="language !=='en'?'margin-top:5px':''"
                  @click="clickBarImg(item)"
                >
                  <div style="width:100%;height: 100%;">
                    <img
                      class="imgTypes"
                      :src="$BASEURL+item.menuImage"
                    />
                    <!-- <img   :src="item.menuImage"/> -->
                    <div class="imgFont">{{item.menuName}}</div>
                  </div>
                </li>
              </template>
            </ul>
            <!-- <ul class="bottombtn" v-else>
              <li
                v-for="item in menuList"
                :key="item.menuName"
                :class="item.menuImage"
                title
                :style="setBarImg(item)"
                @click="clickBarImg(item)"
              >
                <a href></a>
              </li>
            </ul> -->

          </div>
        </div>
      </div>
    </el-row>
  </div>
</template>

<script>
import { getUrlParams } from "@/util/util.js";
import { setStore, getStore } from '@/util/store';
import {
  getBarMenus,
  getBottomMenu,
  getProBottomMenu,
  getToken,
  getTokenNew,
  getGlassInfo,
  buryPoint,
} from '@/api/home';

import { getUserInfo } from '@/api/common';

import { BASE_URL } from '@/util/constant.js';
export default {
  components: {},
  name: 'titleType',
  props: {
    defaultChecked: {
      type: String,
      default: 'totalCardNum',
    },
    disabled: Array,
  },
  data() {
    return {
      list: [
        // {
        //   name: "综合管理",
        //   id: 0,
        //   value: "totalCardNum",
        //   isChecked: false,
        //   children: [
        //     { name: "待办事项", url: "dbsx" },
        //     { name: "前期管理", url: "qqgl" },
        //     { name: "进度管理", url: "jdgl" },
        //     { name: "周进度执行", url: "zjdzx" },
        //     { name: "工作日志", url: "sgrz" },
        //     { name: "合同管理", url: "htgl" },
        //     { name: "施工进度", url: "sgjd" },
        //     { name: "视频管理", url: "spgl" },
        //     { name: "劳务管理", url: "rygl" },
        //     { name: "地磅管理", url: "dbgl" },
        //     { name: "模型管理", url: "mxgl" },
        //     { name: "工程管理", url: "gcgl" },
        //     { name: "机械设备", url: "jxsb" },
        //     { name: "权限管理", url: "qxgl" }
        //   ]
        // },
        // {
        //   name: "安全管理",
        //   id: 1,
        //   value: "1",
        //   isChecked: false,
        //   children: [
        //     { name: "安全检查", url: "aqjc" },
        //     { name: "安全验收", url: "aqys" },
        //     { name: "事故管理", url: "sggl" },
        //     { name: "安全交底", url: "aqjd" },
        //     { name: "危大工程", url: "wdgc" },
        //     { name: "风险管控", url: "fxgk" }
        //   ]
        // },
        // {
        //   name: "质量管理",
        //   id: 2,
        //   value: "2",
        //   isChecked: false,
        //   children: [
        //     { name: "材料管理", url: "clgl" },
        //     { name: "监理旁站", url: "jlpz" },
        //     { name: "见证取样", url: "jzqy" },
        //     { name: "施工记录", url: "sgjl" },
        //     { name: "图纸管理", url: "tzgl" },
        //     { name: "物资验收", url: "wzys" },
        //     { name: "现场实验", url: "xcsy" },
        //     { name: "质量检查", url: "zljc" },
        //     { name: "质量验收", url: "zlys" },
        //     { name: "监理通知", url: "jltz" }
        //   ]
        // },
        // { name: "安全监测", id: 3, value: "3", isChecked: false },
        // {
        //   name: "节能环保",
        //   id: 4,
        //   value: "4",
        //   isChecked: false,
        //   children: [
        //     { name: "环境监测", url: "hjjc" },
        //     { name: "施工方案", url: "sgfa" }
        //   ]
        // },
        // {
        //   name: "制度管理",
        //   id: 5,
        //   value: "5",
        //   isChecked: false,
        //   children: [{ name: "施工方案", url: "sgfa" }]
        // },
        // {
        //   name: "工程资料",
        //   id: 6,
        //   value: "6",
        //   isChecked: false,
        //   children: [{ name: "工程资料", url: "gczl" }]
        // }
      ],
      menuList: [],
      listNum: {},
      keyTimer: null,
      userId: 0,
      companyId: '',
      projectId: '',
      projectName: '',
      indexs: 0,
      currentMenuTab: getStore({ name: 'currentMenuTab' }),
      glassUserId: '', // 接收智能眼镜接口userId
      token: '',
      limitSpaceType: 0,
      fireWorkType: 0,
      userType: '',
      interUserType: '', // 从接口取的
      isShow: true,
      language: "zh", // 默认为false不显示加载
    };
  },
  watch: {
    "$i18n.locale"(val) {
      if (val) {
        this.language = val;
        this.languageChange();
      }
    },
  },
  created() {
    // console.log(getStore({ name: 'currentMenuTab' }),"currentMenuTab")
    this.userId = getStore({ name: 'userId' }) || getUrlParams().userId;;
    this.companyId = getStore({ name: 'companyId' }) || getUrlParams().companyId;;
    this.projectId = getStore({ name: 'projectId' }) || getUrlParams().projectId;;
    this.projectName = getStore({ name: 'projectName' });
    // this.userType = getStore({ name: "userType" }); 这个取得不对，结果是8
  },
  mounted() {
    // if (this.currentMenuTab) {
    //   console.log("mounted",this.currentMenuTab)
    //   this.itemClick(this.currentMenuTab)
    // }
    this.language = getStore({ name: "language" });
    this.$i18n.locale = this.language;
    this.getUserInfoFun();
    this.getGlassInfo();
    this.getToken();
  },
  methods: {
    languageChange() {
      this.getBarMenus()
    },
    async getUserInfoFun() {
      let { data } = await getUserInfo({
        userId: this.userId,
      });
      if (data.statusCode === 200) {
        this.fireWorkType = data.data.fireWorkType;
        this.limitSpaceType = data.data.limitSpaceType;
        // console.log(this.fireWorkType, 'this.fireWorkType');
        this.interUserType = data.data.userType;
        this.getBarMenus();
      }
    },
    strCode(str) {
      //获取字符串的字节数
      var count = 0; //初始化字节数递加变量并获取字符串参数的字符个数
      // console.log(str, "str");
      if (str) {
        //如果存在字符串，则执行
        let len = str.length;
        for (var i = 0; i < len; i++) {
          //遍历字符串，枚举每个字符
          if (str.charCodeAt(i) > 255) {
            //字符编码大于255，说明是双字节字符(即是中文)
            count += 2; //则累加2个
          } else {
            count++; //否则递加一次
          }
        }
        // console.log(count, "count");
        return count; //返回字节数
      } else {
        // console.log(0);
        return 0; //如果参数为空，则返回0个
      }
    },
    // 获取首页菜单
    getBarMenus() {
      this.list = [];
      // console.log('getBarMenus=', this.interUserType);
      // console.log('this.interUserType == 7', this.interUserType == 7);
      if (this.interUserType == 7) {
        // 公司级账号传参数projectId
        // console.log('this.projectId', this.projectId);
        getBottomMenu(this.projectId).then((res) => {
          const result = res.data;
          if (result.statusCode == 200) {
            result.data.map((item, index) => {
              this.list.push({
                name: item.groupName,
                barImage: item.barImage,
                id: item.id,
                value: index === 0 ? 'totalCardNum' : index,
                isChecked: false,
                children: item.listTreeMenus,
                code: this.strCode(item.groupName),
              });
            });
            this.list.forEach((ele, index) => {
              ele.isChecked = ele.value === this.defaultChecked;
              // TODO 默认上次选中的
              if (this.currentMenuTab) {
                if (this.currentMenuTab.id == ele.id) {
                  setTimeout(() => {
                    this.itemClick(ele, 0);
                  }, 20);
                } else {
                  ele.isChecked = false;
                }
              }
              if (this.disabled && this.disabled.length > 0) {
                this.disabled.forEach((v) => {
                  v == ele.value &&
                    ((ele.isChecked = false), (ele.disabled = true));
                });
              }
            });
            this.menuList = this.list[0].children;
            setTimeout(() => {
              // console.log('**********----------------------');
              // 菜单固定长度
              let liWidth =
                document.getElementsByClassName('leftrows')[0].clientWidth;
              let ulWidth =
                document.getElementsByClassName('ulboxs')[0].clientWidth;
              // ul中li长度总和
              let ulele = document.getElementById('menu_ul');
              let liElements = ulele.getElementsByTagName('li');
              var allLength = 0;
              // console.log(
              //   ulele,
              //   liElements,
              //   liElements.length,
              //   liElements[0].clientWidth,
              //   'ul li'
              // );
              for (var i = 0; i < liElements.length; i++) {
                // console.log('0477');
                allLength = allLength + liElements[i].clientWidth;
                // console.log(allLength, liElements[i].clientWidth, '----');
              }
              // console.log(liWidth, ulWidth, allLength, '00000');
              if (ulWidth < allLength) {
                // console.log('1111', this.isShow);
                this.isShow = true;
              } else {
                // console.log('2222', this.isShow);
                this.isShow = false;
              }
              // console.log('3333', this.isShow);
            }, 300);
          }
        });
      } else {
        // 项目级账号不传参数
        getProBottomMenu().then((res) => {
          const result = res.data;
          if (result.statusCode == 200) {
            result.data.map((item, index) => {
              this.list.push({
                name: item.groupName,
                barImage: item.barImage,
                id: item.id,
                value: index === 0 ? 'totalCardNum' : index,
                isChecked: false,
                children: item.listTreeMenus,
                code: this.strCode(item.groupName),
              });
            });
            this.currentMenuTab = getStore({ name: 'currentMenuTab' })
            this.list.forEach((ele, index) => {
              ele.isChecked = ele.value === this.defaultChecked;
              // TODO 默认上次选中的
              if (this.currentMenuTab) {
                if (this.currentMenuTab.id == ele.id) {
                  setTimeout(() => {
                    this.itemClick(ele, 0);
                  }, 20);
                } else {
                  ele.isChecked = false;
                }
              }
              if (this.disabled && this.disabled.length > 0) {
                this.disabled.forEach((v) => {
                  v == ele.value &&
                    ((ele.isChecked = false), (ele.disabled = true));
                });
              }
            });
            // if(this.$IsProjectShow){
            //   this.list.forEach((ele) => {
            //     if(ele.name=='智慧管理'){
            //       ele.name=this.$t(`IntelligentManageability`)
            //     }
            //     if(ele.name=='智慧创安'){
            //       ele.name=this.$t(`SmartChuang`)
            //     }
            //     if(ele.name=='智慧创卫'){
            //       ele.name=this.$t(`SmartHealthCreation`)
            //     }
            //     if(ele.name=='智慧建造'){
            //       ele.name=this.$t(`SmartHealthCreation`)
            //     }
            //     if(ele.name=='智慧提质'){
            //       ele.name=this.$t(`Intelligentimprovement`)
            //     }
            //     if(ele.name.includes('增绿')){
            //       ele.name=this.$t(`Smartgreening`)
            //     }
            //   })
            // }
            this.menuList = this.list[0].children;
            setTimeout(() => {
              // console.log('**********----------------------');
              // 菜单固定长度
              let liWidth =
                document.getElementsByClassName('leftrows')[0].clientWidth;
              let ulWidth =
                document.getElementsByClassName('ulboxs')[0].clientWidth;
              // ul中li长度总和
              let ulele = document.getElementById('menu_ul');
              let liElements = ulele.getElementsByTagName('li');
              var allLength = 0;
              // console.log(
              //   ulele,
              //   liElements,
              //   liElements.length,
              //   liElements[0].clientWidth,
              //   'ul li'
              // );
              for (var i = 0; i < liElements.length; i++) {
                // console.log('0477');
                allLength = allLength + liElements[i].clientWidth;
                // console.log(allLength, liElements[i].clientWidth, '----');
              }
              // console.log(liWidth, ulWidth, allLength, '00000');
              if (ulWidth < allLength) {
                // console.log('1111', this.isShow);
                this.isShow = true;
              } else {
                // console.log('2222', this.isShow);
                this.isShow = false;
              }

              // console.log('3333', this.isShow);
            }, 300);
          }
        });
      }
      //   getBarMenus(this.userId).then((res) => {
      //     const result = res.data;
      //     if (result.statusCode == 200) {
      //       result.data.map((item, index) => {
      //         this.list.push({
      //           name: item.barName,
      //           barImage: item.barImage,
      //           id: item.id,
      //           value: index === 0 ? "totalCardNum" : index,
      //           isChecked: false,
      //           children: item.list,
      //         });
      //       });
      //       this.list.forEach((ele, index) => {
      //         ele.isChecked = ele.value === this.defaultChecked;
      //         // TODO 默认上次选中的
      //         if (this.currentMenuTab) {
      //           if (this.currentMenuTab.id == ele.id) {
      //             setTimeout(() => {
      //               this.itemClick(ele);
      //             }, 20);
      //           } else {
      //             ele.isChecked = false;
      //           }
      //         }
      //         if (this.disabled && this.disabled.length > 0) {
      //           this.disabled.forEach((v) => {
      //             v == ele.value &&
      //               ((ele.isChecked = false), (ele.disabled = true));
      //           });
      //         }
      //       });
      //       this.menuList = this.list[0].children;
      //     }
      //   });
    },
    setBarImg(item) {
      const conTop = {
        background: 'url(' + BASE_URL + item.menuImage + ')',
        // background: "url(" + require("../../assets/aqys.png") + ")",
        backgroundSize: '100% 100%',
      };
      return conTop;

    },
    getToken() {
      getToken(this.userId).then((res) => {
        if (res.data.statusCode == 200) {
          this.token = res.data.data;
        }
      });
    },
    getGlassInfo() {
      const params = {
        companyId: this.companyId,
        projectId: this.projectId,
      };
      getGlassInfo(params).then((res) => {
        const result = res.data;
        if (result.statusCode == 200) {
          this.glassUserId = result.data.userId;
          this.appId = result.data.appId;
          this.appSecret = result.data.appSecret;
        }
      });
    },
    async clickBarImg(item) {
      await this.buryPointData(item);
      if (item.menuName == '工程资料') {
        window.open(
          `${item.menuUrl}&zhgduserId=${this.userId}&token=${this.token}&projectId=${this.projectId}&companyId=${this.companyId}`
        );
      } else if (item.menuName == '智能眼镜') {
        window.open(
          `${item.menuUrl}?userId=${this.glassUserId}&appId=${this.appId}&appSecret=${this.appSecret}`
        );
      } else if (item.menuName === '动火管理') {
        if (this.fireWorkType == 2)
          window.location.href = process.env.npm_config_domin ? `${process.env.npm_config_domin}:8834?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}` : `http://${process.env.npm_config_ip}:8834?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}`;
        // 动火管理需要跳转https域名（因为高级安全帽视频语音功能）
        else
          getTokenNew().then((res) => {
            let {
              data: { data, statusCode },
            } = res;
            if (data && statusCode === 200) window.open(item.menuUrl, data);
          });
      } else if (item.menuName === '智能安全帽监测') {
        // 智能安全帽需要跳转https域名（因为高级安全帽视频语音功能）
        getTokenNew().then((res) => {
          let {
            data: { data, statusCode },
          } = res;
          if (data && statusCode === 200) window.open(item.menuUrl, data);
        });
      } else if (item.menuName == '有限空间作业' && this.limitSpaceType == 2) {
        window.location.href = process.env.npm_config_domin ? `${process.env.npm_config_domin}:8836?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}` : `http://${process.env.npm_config_ip}:8836?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}`;
      } else {
        if (item.menuUrl.indexOf('20000') > -1) {//北京专版传分类名称
          window.location.href = `${item.menuUrl}?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}&language=${this.language}&firstName=${encodeURI(getStore({ name: 'currentMenuTab' }).name)}&secondName=${item.menuName}`;
        } else {
          window.location.href = `${item.menuUrl}?userId=${this.userId}&companyId=${this.companyId}&projectId=${this.projectId}&projectName=${this.projectName}&language=${this.language}`;
        }
      }
    },
    // 埋点注入
    buryPointData(data) {
      let params = {
        moduleId: data.id,
        moduleName: data.menuName,
        logType: '0003-0000-0001',
        extendJson: '',
        origin: 1,
      };
      buryPoint(params).then((res) => {
        console.log(res, '埋点');
      });
    },
    itemClick(val, val2) {
      // 记录上次点击的
      setStore({ name: 'currentMenuTab', content: val })
      this.indexs = 0;
      if (val.disabled) return false;
      this.list.forEach((ele) => {
        if (ele.id == val.id) {
          ele.isChecked = true;
          setStore({ name: 'labourType', content: ele.value, type: 'session' });
        } else {
          ele.isChecked = false;
        }
      });
      // if(this.$IsProjectShow){
      //   val.children.forEach((item)=>{
      //   if(item.menuName=='节电管理'){
      //     // item.menuName=this.$t(`Energysaving`)
      //     item.menuImage= require("../../assets/jd2.png")
      //   }
      //   if(item.menuName=='节水管理'){
      //     // item.menuName=this.$t(`Watersaving`)
      //     item.menuImage=require("../../assets/js2.png")
      //   }
      //   if(item.menuName=='劳务管理'){
      //     // item.menuName=this.$t(`Labormanagement`)
      //     item.menuImage=require("../../assets/lwgl2.png")
      //   }
      //   if(item.menuName=='视频管理'){
      //     // item.menuName=this.$t(`Videomanagement`)
      //     item.menuImage=require("../../assets/spgl2.png")
      //   }
      //   if(item.menuName=='升降机监测'){
      //     // item.menuName=this.$t(`Elevatormonitoring`)
      //     item.menuImage=require("../../assets/sjj2.png")
      //   }
      //   if(item.menuName=='塔吊监测'){
      //     // item.menuName=this.$t(`Towercrane`)
      //     item.menuImage=require("../../assets/tdjc2.png")
      //   }
      //   if(item.menuName=='配电箱监测'){
      //     // item.menuName=this.$t(`Distributionbox`)
      //     item.menuImage=require("../../assets/pdx2.png")
      //   }
      //   if(item.menuName=='智能测量'){
      //     // item.menuName=this.$t(`Intelligentmeasurement`)
      //     item.menuImage=require("../../assets/zncl2.png")
      //   }
      //   if(item.menuName=='车辆冲洗监测'){
      //     // item.menuName=this.$t(`Vehiclewashing`)
      //     item.menuImage=require("../../assets/clcx2.png")
      //   }
      //   if(item.menuName=='项目碳管理'){
      //     // item.menuName=this.$t(`ProjectCarbon`)
      //     item.menuImage=require("../../assets/tgl2.png")
      //   }

      // })
      // }
      this.menuList = val.children || [];
      let scrollUl = document.getElementsByClassName('scroll_list')[0];
      scrollUl.style.left = 0 + 'px';
      scrollUl.style.transition = '0s';
      this.$emit('titleClick', val);
      // 解决菜单children数据太长，翻译不出来的部分
      // 手动点击菜单，判断子模块超过19个的时候，刷新会出现翻译
      if (this.language === 'en' && val2 === 1 && val.children.length > 19) {
        this.$router.go(0)
      }
    },
    rowClicks(right) {
      let liWidth = document.getElementsByClassName('leftrows')[0].clientWidth;
      let ulWidth =
        document.getElementsByClassName('content_divs')[0].clientWidth -
        liWidth * 2;
      let num = document.getElementsByClassName('tab_ul')[0].children.length;
      let round = Math.trunc(((liWidth + 80) * num) / ulWidth);
      // 显示在第几屏
      if ((liWidth + 80) * num > ulWidth) {
        //滚动判断待优化
        let scrollUl = document.getElementsByClassName('scroll_lists')[0];
        if (right) {
          this.indexs++;
          if (this.indexs > round) {
            this.indexs = round;
          }
          scrollUl.style.left = -ulWidth * this.indexs + 'px';
          scrollUl.style.transition = '0.8s';
        } else {
          this.indexs--;
          if (this.indexs < 0) {
            this.indexs = 0;
          }
          scrollUl.style.left = -ulWidth * this.indexs + 'px';
          scrollUl.style.transition = '0.8s';
        }
      } else {
      }
    },
    rowClick(right) {
      let liWidth = document.getElementsByClassName('leftrow')[0].clientWidth;
      let ulWidth = document.getElementsByClassName('ulboxs')[0].clientWidth;
      let num = document.getElementsByClassName('bottombtn')[0].children.length;
      let round = Math.trunc(((liWidth + 20) * num) / ulWidth);
      // 显示在第几屏
      if ((liWidth + 10) * num > ulWidth) {
        //滚动判断待优化
        let scrollUl = document.getElementsByClassName('scroll_list')[0];
        if (right) {
          this.indexs++;
          if (this.indexs > round) {
            this.indexs = round;
          }
          scrollUl.style.left = -ulWidth * this.indexs + 'px';
          scrollUl.style.transition = '0.8s';
        } else {
          this.indexs--;
          if (this.indexs < 0) {
            this.indexs = 0;
          }
          scrollUl.style.left = -ulWidth * this.indexs + 'px';
          scrollUl.style.transition = '0.8s';
        }
      } else {
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.width0 {
  width: auto !important;
  text-align: center !important;
}
.module-type {
  .bomcondiv {
    width: 100%;
    height: 75px;
    margin-top: 10px;
    position: relative;
    overflow: hidden;
    .leftrow {
      width: 88px;
      height: 88px;
      background: url(../../assets/leftrow.png) no-repeat center;
      cursor: pointer;
      position: absolute;
      bottom: 0;
      left: 0;
    }
    .rightrow {
      width: 88px;
      height: 88px;
      background: url(../../assets/rightrow.png) no-repeat center;
      cursor: pointer;
      position: absolute;
      bottom: 0;
      right: 0;
      z-index: 9;
    }
    .leftrows {
      width: 88px;
      height: 78px;
      background: url(../../assets/left.png) no-repeat center;
      cursor: pointer;
      position: absolute;
      bottom: 0;
      left: 0;
    }
    .rightrows {
      width: 88px;
      height: 78px;
      background: url(../../assets/right.png) no-repeat center;
      cursor: pointer;
      position: absolute;
      bottom: 0;
      right: 0;
      z-index: 9;
    }
    .ulboxs {
      width: calc(100% - 120px);
      position: absolute;
      top: 0;
      overflow: hidden;
      height: 88px;
      left: 62px;
      white-space: nowrap;
      .scroll_lists {
        width: 2200px;
        transition: 0.8s;
        position: relative;
        // li {
        //   cursor: pointer;
        //   display: inline-block;
        //   width: 84px;
        //   height: 84px;
        //   margin: 0 -3px;
        //   position: relative;
        //   // float: left;
        // }
        ul {
          // display: flex;
          // justify-content: center;
          margin-top: 14px;
          // padding-left: 0.5rem;
          li {
            cursor: pointer;
            display: inline-block;
            font-size: 16px;
            color: #42edf8;
            text-align: center;
          }
          .maxSize {
            width: 270px;
            background: url(../../assets/bomtabdiv12.png) no-repeat center;
            background-size: 100% 100%;
            height: 45px;
            line-height: 45px;
          }
          .mediumSize {
            width: 145px;
            background: url(../../assets/bomtabdiv12.png) no-repeat center;
            background-size: 100% 100%;
            height: 45px;
            line-height: 45px;
          }
          .minSize {
            width: 130px;
            background: url(../../assets/bomtabdiv.png) no-repeat center;
            background-size: 100% 100%;
            height: 45px;
            line-height: 45px;
          }
          .actived {
            color: #fff;
            width: 130px !important;
            background: url(../../assets/bomtabclick.png) no-repeat center;
            background-size: 100% 100%;
            height: 45px !important;
            line-height: 45px;
          }
          .actived12 {
            color: #fff;
            // width: 130px !important;
            background: url(../../assets/bomtabclick12.png) no-repeat center;
            background-size: 100% 100%;
            height: 45px !important;
            line-height: 45px;
          }
        }
      }
    }
    .ulbox {
      width: calc(100% - 135px);
      position: absolute;
      top: 0;
      overflow: hidden;
      height: 88px;
      left: 62px;
      white-space: nowrap;
      .scroll_list {
        width: 5200px; //总数量宽度
        transition: 0.8s;
        position: relative;
        li {
          cursor: pointer;
          // display: inline-block;
          float: left;
          width: 84px;
          height: 84px;
          margin: 0px 10px;
          position: relative;
          vertical-align: bottom;
          // float: left;
        }
        // .dbsx {
        //   background: url(../../assets/dbsx.png) no-repeat center;
        //   background-size: 100% 100%;
        // }
        // .qqgl {
        //   background: url(../../assets/qqgl.png) no-repeat center;
        //   background-size: 100% 100%;
        // }
        // .jdgl {
        //   background: url(../../assets/jdgl.png) no-repeat center;
        //   background-size: 100% 100%;
        // }
        // .zjdzx {
        //   background: url(../../assets/zjdzx.png) no-repeat center;
        //   background-size: 100% 100%;
        // }
        // .aqjc {
        //   background: url(../../assets/aqjc.png) no-repeat center;
        //   background-size: 100% 100%;
        // }
      }
    }
  }
}
.imgType {
  width: 100%;
  height: 50px;
}
.imgTypes {
  width: 100%;
  height: 40px;
}
.imgFont {
  font-size: 10px;
  white-space: break-spaces;
  word-wrap: break-word;
  text-align: center;
}
</style>
