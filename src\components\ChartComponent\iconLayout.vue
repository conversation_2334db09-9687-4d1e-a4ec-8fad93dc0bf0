<template>
  <div class="icon-layout">
    <div
      class="title"
      v-if="showTitle"
    >
      <img
        :src="titleIcon || defaultIcon"
        class="icon-image"
      >
      <div class="info">
        <p class="info-name">{{ titleObj.text }}</p>
        <p class="info-value">{{ titleObj.value }}</p>
      </div>
    </div>
    <!-- 根据 cols 动态设置列数 -->
    <div
      class="grid-container"
      :style="{ gridTemplateColumns: `repeat(${cols}, 1fr)`, justifyContent: 'space-between' }"
    >

      <div
        v-for="(item, index) in iconList"
        :key="index"
        class="icon-item"
        @click="handleClick(item)"
      >
        <img
          :src="item.icon"
          alt="icon"
          class="icon-image"
        >
        <div class="info">

          <p class="info-name">{{ item.text }}</p>
          <p class="info-value">{{ item.value }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'IconLayout',
  props: {
    // 是否显示标题
    showTitle: {
      type: Boolean,
      default: true
    },
    titleObj: {
      type: Object,
      default: () => { }
    },
    titleIcon: {
      type: String,
      default: require('../../assets/customize/test.png')
    },
    defaultIcon: require('../../assets/customize/test.png'),
    // 图标列表，包含图标路径和文本
    iconList: {
      type: Array,
      required: true
    },
    // 列数，支持 2 或 3
    cols: {
      type: Number,
      default: 3,
      validator: (value) => [2, 3].includes(value)
    },
    // 点击事件处理函数
    onClick: {
      type: Function,
      default: () => { }
    }
  },
  methods: {

    handleClick(item) {
      this.onClick(item);
    }
  }
};
</script>

<style scoped>
.title {
  padding: 10px 0;
  border-bottom: 1px solid #a4abbb;
  text-align: left;
  display: flex;
  margin: 0 10px;
}
.icon-layout {
  width: 100%;
}

.grid-container {
  display: grid;
  gap: 10px;
  padding: 10px;
}

.icon-item {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.icon-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.icon-image {
  width: 44px;
  height: 44px;
  margin-right: 12px;
}
.info {
  flex: 1;
}
.info-name {
  font-size: 14px;
  text-align: left;
  color: #c1c7cf;
  white-space: nowrap;
  max-width: 33%;
}
.info-value {
  font-size: 24px;
  font-family: PangMenZhengDaoBiaoTiTiMianFeiBan-4,
    PangMenZhengDaoBiaoTiTiMianFeiBan-4;
  background: linear-gradient(to bottom, #aadaff, #ffffff);
  background-clip: text;
  -webkit-background-clip: text;
  text-fill-color: transparent;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}
</style>