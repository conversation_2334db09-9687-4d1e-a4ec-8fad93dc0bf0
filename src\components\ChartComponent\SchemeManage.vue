<!--
 * @Description: 方案管理
 * @Author:
 * @Date：2025-07-16 
 * @LastEditors: dongqi<PERSON>qian
 * @LastEditTime: 2025-07-25 17:51:15
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">

      <IconLayout
        :showTitle="true"
        :titleObj="titleObj"
        :titleIcon="titleIcon"
        :iconList="iconList2x2"
        :cols="2"
      />
    </div>
  </div>
</template>
<script>
import { drawPie } from "@/components/constructionRecord/Echarts/echartsOne.js";
import IconLayout from './iconLayout.vue';
import { getConstructionSch } from "@/api/echrtsApi";
// import { getEnviroment } from "@/api/echrtsApi";
export default {
  components: { IconLayout },
  name: "SchemeManage",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,

      titleIcon: "",
      titleObj: {
        text: '', value: 0
      },
      iconList3x3: [

      ],
      iconList2x2: [

      ]
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
  },
  methods: {
    getBarData() {
      getConstructionSch()
        .then((res) => {
          const {
            data: { constructSchNames, counts },
            statusCode,
          } = res.data;
          if (statusCode == 200) {
            this.titleIcon = require('../../assets/customize/SchemeManage/totalcount.png');
            this.titleObj.text = "总方案数";
            if (Array.isArray(counts) && counts.length > 0) {
              counts.forEach((count, index) => {
                this.titleObj.value += count;
                this.iconList2x2.push({
                  icon: require(`../../assets/customize/SchemeManage/manage${index}.png`),
                  text: constructSchNames[index],
                  value: count
                })
              });
            }
          }
        })
        .catch(() => { });
    },
    setEcharts(val) {

    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
}
.area {
  overflow: hidden;
}
</style>
