<template>
  <div id="nav-bar">
    <div class="nav">
      <span
        v-for="item in list"
        :key="item.id"
        :class="item.actived === 1 ? 'active' : ''"
        @click="itemClick(item)"
        @mouseover="mouseOver(item)"
        @mouseleave="mouseLeave(item)"
      >
        {{ item.name }}
      </span>
    </div>
  </div>
</template>

<script>
import { getStore, setStore } from "@/util/store";
export default {
  name: "Nav",
  data() {
    return {
      list: [
        {
          id: "todo",
          name: "待办",
          actived: 1,
          isClick: true,
        },
        {
          id: "warning",
          name: "告警消息",
          actived: 0,
          isClick: false,
        },
        // {
        //   id: "constructionRecord",
        //   name: "消防防汛",
        //   actived: 0,
        //   isClick: false,
        // },
        // {
        //   id: "constructionRecord",
        //   name: "廉政建设",
        //   actived: 0,
        //   isClick: false,
        // },
        // {
        //   id: "constructionRecord",
        //   name: "会议管理",
        //   actived: 0,
        //   isClick: false,
        // },
        // {
        //   id: "constructionRecord",
        //   name: "远程协同",
        //   actived: 0,
        //   isClick: false,
        // },
        // {
        //   id: "constructionRecord",
        //   name: "文档中心",
        //   actived: 0,
        //   isClick: false,
        // },
        //  {id:'demo',name:'demo',actived:0},
      ],
      actived: false,
    };
  },
  created() {
    let buttonItem = this.$route.name;// || getStore({ name: "buttonId" })
    this.itemClick({ id: buttonItem });
  },
  methods: {
    mouseOver(val) {
      val.actived = 1;
    },
    mouseLeave(val) {
      !val.isClick && (val.actived = 0);
    },
    itemClick(val) {
      this.list.forEach((ele) => {
        if (ele.id == val.id) {
          ele.actived = 1;
          ele.isClick = true;
        } else {
          ele.actived = 0;
          ele.isClick = false;
        }
      });
      setStore({ name: "buttonId", content: val, type: "session" });
      this.$router.push({ path: `/${val.id}` });
    },
  },
};
</script>

<style lang="scss" scoped>
#nav-bar {
  // position: fixed;
  left: 0;
  right: 0;
  z-index: 11;
  background: url(./../../assets/bg.png) no-repeat center;
  background-size: 100% 100%;
  width: 100%;
  text-align: center;
  margin-left: 2%;
  margin-top: 90px;
  .nav {
    padding: 20px 0;
    padding-left: 10px;
    box-sizing: border-box;
    overflow-x: auto;
    span {
      // width: 140px;
      // height: 50px;
      padding: 10px 10px;
      line-height: 40px;
      text-align: center;
      margin-right: 20px;
      background: #22386d;
      color: #61d2f7;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
    }
    .active {
      background: #2f65ec !important;
    }
  }
}
</style>
