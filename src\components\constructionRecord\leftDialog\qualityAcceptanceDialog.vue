<!-- 进度管理弹框 -->
<template>
  <el-dialog
    :visible.sync="scheduleDialog"
    :append-to-body="true"
    width="52%"
    @close="closeCli"
  >
    <div slot="title" class="header-title">
      <span class="title-name">质量验收一次性通过率</span>
    </div>
    <el-row>
      <el-col>
        <el-form ref="form" :model="trainForm">
          <div style="margin-left:10%">
            <el-date-picker
              v-model="trainForm.checkPersonId"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
            <el-button style="margin-left:20px"> 统计</el-button>
          </div>
            <div
              id="barBars"
              :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
              style="display: inline-block; margin-top: 20px"
            ></div>
        </el-form>
      </el-col>
    </el-row>
  </el-dialog>
</template>
<script>
import { drawBar3 } from "@/util/echarts";
export default {
  name: "qualityAcceptanceDialog",
  props: {},
  data() {
    return {
      scheduleDialog: false,
      trainForm: {
        checkPersonId: 1,
      },
      peopleOptions: [
        { label: "请选择", value: 0 },
        { label: "建科研智慧建造云平台", value: 1 },
        { label: "北京市政", value: 2 },
        { label: "房地产2", value: 3 },
      ],
      barWidth: 1000,
      barHeight: 500,
      barParams: {
        dom: "barBar",
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
      pickerOptions: {
        shortcuts: [
          {
            // text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime());
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    closeCli() {
      this.scheduleDialog = false;
    },
  },
  destroyed() {},
};
</script>
<style lang="scss" scoped>
.header-title {
  border-bottom: 1px solid #4fa8d0;
}
.title-name {
  width: 100%;
  height: 0.5rem;
  line-height: 0.5rem;
  text-align: center;
  color: #00deff;
  font-size: 20px;
  font-weight: bold;
  margin-left: 45%;
  background: rgba(13, 51, 134, 0.8);
  // border-bottom: 1px solid #4fa8d0;
}
</style>

