<template>
  <div class="upload-main">
    <div class="upload-wrap">
      <el-upload
        class="uploadWrap"
        action=""
        :list-type="listType"
        :on-remove="handleRemove"
        :on-preview="handlePreview"
        :on-change="onChange"
        :file-list="fileList"
        :auto-upload="false"
        accept=".png,.jpg"
      >
        <el-col style="text-align: left"
          ><el-button v-if="fileList.length <= 2" size="small" type="primary">{{
            btnText
          }}</el-button>
        </el-col>

      </el-upload>
      <el-progress
        v-if="status === 2 && showProgress"
        type="circle"
        style="margin-left: 10px"
        :percentage="percentage"
        :width="26"
        :show-text="false"
      ></el-progress>
    </div>
  </div>
</template>

<script>
const UploadStatus = {
  pending: 0, // 待选着上传文件
  ready: 1, // 已选上传文件，上传参数已完善，待上传
  uploading: 2, // 上传中
  finally: 3, // 操作结束
};
import {uploadFile} from "@/api/report";
export default {
  model: {
    prop: "pathUrl",
    event: "input",
  },
  props: {
    pathUrl: {
      type: [String, Array],
      default: "",
    },
    listType: {
      type: String,
      default: "text",
    },
    btnText: {
      type: String,
      default: "选择文件",
    },
    showProgress: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      fileList: [],
      uploadEvent: null,
      status: UploadStatus.pending,
      percentage: 0,
      dialogImageUrl: "",
      dialogVisible: false,
      isPDF: false,
    };
  },
  watch: {
    pathUrl: {
      handler(newVal) {
        if (newVal) {
          // 接口返回的file内容
          // console.log(newVal,"newVal")
          let list = newVal.filter((item) => !item.uid);

          if (list.length) {
            list.forEach((element) => {
              let obj = { ...element };
              // console.log(element,"element")
              obj.url = this.$baseUrl + element.filePath;
              obj.name = element.fileName;
              this.fileList.push(obj);
            });
            // console.log(newVal,"newVal",this.fileList)
            this.status = UploadStatus.finally;
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 移除文件时
    handleRemove(file) {
      this.fileList=[]
      let fileIndex = this.fileList.findIndex((item) => item.uid === file.uid);
      if (fileIndex > -1) this.fileList.splice(fileIndex, 1);
      this.status = UploadStatus.pending;
      this.$emit("input", this.fileList);
    },
    // 预览
    async handlePreview(file) {
      window.open(this.$BASEURL + file.filePath)
    },
    restart() {
      this.fileList = [];
      this.status = UploadStatus.pending;
      this.percentage = 0;
      this.uploadEvent = null;
    },

    onProgress(p) {
      this.percentage = parseInt((p.loaded / p.total) * 100);
    },
    // 选择文件change
    async onChange(file, fileList) {
      this.fileList=[]
      fileList.forEach((ele)=>{
        if(file.uid ==ele.uid){
          this.fileList.push(ele)
        }
      })
      // this.fileList = fileList;
      this.status = UploadStatus.uploading;
      this.uploadFileFunc(file);
    },
    // 上传 接口
    async uploadFileFunc(file) {
      if (file) {
        const typeArr = [
          "image/png",
          "image/gif",
          "image/jpeg",
          "image/jpg"
        ];
        const isJPG = typeArr.indexOf(file.raw.type) !== -1;
        const isLt5M = file.size / 1024 / 1024 < 5;

        if (!isJPG) {
          let index = this.fileList.indexOf(file);
          this.fileList.splice(index, 1);
          this.$message.error("请上传.jpg、.png格式文件!");
          return;
        }
        if (!isLt5M) {
          let index = this.fileList.indexOf(file);
          this.fileList.splice(index, 1);
          this.$message.error("上传文件大小不能超过 5MB!");
          return;
        }
        let fd = new FormData();
      fd.append("req", file.raw);
      let { data } = await uploadFile({
        fd,
        onProgress: this.onProgress,
      });
      if (data.statusCode === 200) {
        this.status = UploadStatus.finally;
        this.percentage = 0;
        let fileItem = this.fileList.find((item) => item.uid === file.uid);
        if (fileItem) {
          fileItem.filePath = data.data[0].fileUrl;
          fileItem.fileName = data.data[0].fileName;
        }
        this.$emit("input", this.fileList);
      }
      }

    },
  },
};
</script>

<style lang="scss">
.upload-wrap {
     .uploadWrap {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border: 1px solid #fff;
    // max-width: 60%;
    .el-upload-list__item:hover{
      background: transparent !important;
    }
    .el-upload-list__item-name {
          color: #409eff;
          max-width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        }
     .el-upload--picture-card {
      border: none;
      width: 0;
      height: 0;
      line-height: 0;
    }
    .el-upload-list {
      // width: 100%;
      max-width: calc(100% - 90px);
      margin-top: -5px;
      .el-upload-list__item {
        width: auto;

      }
    }
    .el-upload-list__item {
      margin-top: 8px;
      .el-icon-close-tip {
        display: none !important;
      }
      .el-icon-close {
        top: 0.3rem;
        right: 0.6125rem;
        font-size: 16px !important;
      }
    }
  }
  .fileList {
    height: 26px;
    line-height: 26px;
    color: #fff;
    display: block;
    margin-right: 40px;
    overflow: hidden;
    padding-left: 4px;
    text-overflow: ellipsis;
    white-space: nowrap;
    background: #0d2c7a;
    cursor: pointer;

    i {
      margin-left: 8px;
    }
  }
}
</style>
