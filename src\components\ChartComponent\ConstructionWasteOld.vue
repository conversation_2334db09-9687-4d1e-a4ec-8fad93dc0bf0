<!--
 * @Description: 建筑垃圾管理
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2023-01-03 17:46:32
 * @LastEditors: lihanbing
 * @Usage:
-->
<template>
  <div class="area" style="cursor: pointer">
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div class="box" ref="ConstructionWasteRef">
        <div
          id="ConstructionWasteChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawPie } from "@/components/constructionRecord/Echarts/echartsOne.js";
import { getBuildWaster } from "@/api/echrtsApi";
export default {
  components: {},
  name: "ConstructionWaste",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "ConstructionWasteChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        // seriesRadius: '70%',
        seriesCenter: ["30%", "45%"],
        tooltipFormatter: `{b}<br /> ${this.$t(
          "customization.reSaveWeight"
        )}：{c}kg<br />${this.$t("customization.reSavePercent")}：{d}%`,
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.ConstructionWasteRef.offsetWidth - 40;
      this.barHeight = this.$refs.ConstructionWasteRef.offsetHeight;
    },
    getBarData() {
      getBuildWaster(this.projectId)
        .then((res) => {
          const { status, data } = res;
          if (status == 200) {
            this.pieParams.data = data.map((item) => {
              switch (item.name) {
                case "废弃木头":
                  item.name = this.$t("customization.anchor");
                  break;
                case "废弃保温板":
                  item.name = this.$t("customization.abandonedSavePlate");
                  break;
                case "废旧砖头":
                  item.name = this.$t("customization.abandonedBrick");
                  break;
                case "废弃混凝土块":
                  item.name = this.$t("customization.abandonedConcrete");
                  break;
                case "其它":
                  item.name = this.$t("customization.others");
                  break;
              }
              return item;
            });
            drawPie(this.pieParams);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
}
</style>
