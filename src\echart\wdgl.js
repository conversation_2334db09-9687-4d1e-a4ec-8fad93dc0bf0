var echartleft2 = echarts.init($(".wdgl")[0]);

var labelOption = {
	normal: {
		show: true,
		formatter: '{c} ',
		fontSize: 10,
		rich: {
			name: {
				textBorderColor: '#fff'
			}
		}
	}
};

var option2 = {

	color: ['rgb(255,255,26)',
		'rgb(30,255,0)',
		'rgb(225,32,32)'
	],
	grid: {
		bottom: '80%',
		top: '95%',
		left: '2%',
		right: '2%',
		containLabel: true
	},
	tooltip: {
		trigger: 'axis',
		axisPointer: {
			type: 'shadow'
		}
	},
	legend: {
		data: ['抗震工程', '高大模板', '脚手架'],
		textStyle: {
			color: '#fff'
		}
	},
	toolbox: {
		show: true,
		orient: 'vertical',
		left: 'right',
		top: 'center'
	},
	calculable: true,
	xAxis: [{
			type: 'category',
			axisLine: {
				lineStyle: {
					type: 'solid',
					color: 'rgb(104,104,104)',
					width: '1'
				}
			},
			axisTick: {
				show: false
			},
			data: ['已验证', '在施'],
			axisLabel: {
				interval: 0,
				show: true,
				splitNumber: 15,
				textStyle: {
					fontSize: 10,
					textAline: 'center',
					color: '#fff'
				},
			}
		}

	],
	yAxis: [{
		type: 'value',
		splitLine: {
			show: true,
			lineStyle: {
				color: 'rgb(104,104,104)'
			}
		},
		axisLabel: {
			interval: 0,
			show: true,
			splitNumber: 15,
			textStyle: {
				fontSize: 10,
				textAline: 'center',
				color: '#fff'
			},
		}
	}],
	series: [{
			name: '抗震工程',
			type: 'bar',
			barGap: 0,
			barCategoryGap: '20%',
			barWidth: '20%',
			data: [100, 200]
		},
		{
			name: '高大模板',
			type: 'bar',
			barCategoryGap: '20%',
			barWidth: '20%',
			data: [896, 524]
		},
		{
			name: '脚手架',
			type: 'bar',
			barCategoryGap: '20%',
			barWidth: '20%',
			data: [414, 363]
		}
	]
};

echartleft2.setOption(option2);