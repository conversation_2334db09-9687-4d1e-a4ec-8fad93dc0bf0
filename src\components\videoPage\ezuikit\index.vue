<template>
  <div
    class="ezuikitPlayerWrap"
    ref="root"
  >
    <div class="videoWrap">
      <div :id="domId ? domId : `video-container_${videoId}`"></div>
    </div>
    <div
      class="presetWrap"
      v-if="allowPreset"
    >
      <!-- <Preset
        :videoId="videoId"
        :videoCfg="videoCfg"
        @fetchYSAPIAfter="fetchYSAPIAfter"
      /> -->
    </div>
  </div>
</template>

<script>
import EZUIKit from "ezuikit-js";
import JkyUtils from "jky-utils";

import { getVideoDetail } from "./api";

const { getDeviceCapacity } = JkyUtils.videoPTZ;

export default {
  name: "EZUIKitJs",
  props: {
    domId: String,
    videoId: {
      type: [String, Number],
      retuired: true
    },
    showPreset: {
      type: Boolean,
      default: false
    }
  },
  data: function () {
    return {
      videoCfg: {},
      allowPreset: false,
      hdurl: '',
    };
  },

  mounted() {
    this.initDatas(this.videoId);
    // window.addEventListener("resize", this.resetWSize);
  },
  beforeDestroy() {
    this.player && this.player.stop(); //关闭视频流

    // window.removeEventListener("resize", this.resetWSize);
  },
  created() { },
  methods: {
    // 初始化页面数据
    initDatas(videoId) {
      this.$nextTick(() => {
        this.getVideoDetail(videoId);
      });
    },

    // 刷新萤石accessToken
    resetAccessToken() {
      this.getVideoDetail(this.videoId);
    },

    // 初始化视频基本信息
    async getVideoDetail(videoId) {
      if (!videoId) return;

      const res = await getVideoDetail(videoId);
      let data = res.data || {};

      data = data.data || {};

      const { deviceSerial, channelNo, token, liveEzOpenUrl, hdLiveEzOpenUrl } = data;

      this.videoCfg = {
        deviceSerial, // 设备序列号
        channelNo, // 通道号
        accessToken: token
      };

      this.playerUri = hdLiveEzOpenUrl; // EzOpen高清直播地址
      this.getDeviceCapacity();
    },

    // 查询设备能力集
    // https://open.ys7.com/help/77
    async getDeviceCapacity() {
      if (!this.showPreset) {
        this.allowPreset = false;
        this.initVideo();
        return;
      }

      let res = await getDeviceCapacity({ ...this.videoCfg });
      res = res.data || {};

      const data = res.data || {};

      let allowPreset = true;
      if (!data.ptz_preset || data.ptz_preset !== "1") {
        allowPreset = false;
      }

      this.allowPreset = allowPreset && this.showPreset;

      this.initVideo();
    },

    computedSize() {
      const { offsetWidth, offsetHeight } = this.$refs.root;

      let h = offsetHeight;
      if (this.allowPreset) {
        h = h - 40;
      }

      return [offsetWidth, h];
    },

    getPlayerThemeData() {
      const params = {
        autoFocus: 5,
        poster:
          "https://resource.eziot.com/group1/M00/00/89/CtwQEmLl8r-AZU7wAAETKlvgerU237.png",
        header: {
          btnList: []
        },
        footer: {
          color: "#FFFFFF",
          activeColor: "#1890FF",
          backgroundColor: "#00000021",
          btnList: [
            {
              iconId: "play",
              part: "left",
              defaultActive: 1,
              memo: "播放",
              isrender: 1
            },
            {
              iconId: "capturePicture",
              part: "left",
              defaultActive: 0,
              memo: "截屏按钮",
              isrender: 1
            },
            {
              iconId: "sound",
              part: "left",
              defaultActive: 0,
              memo: "声音按钮",
              isrender: 1
            },
            {
              iconId: "pantile",
              part: "left",
              defaultActive: 0,
              memo: "云台控制按钮",
              isrender: this.allowPreset ? 1 : 0
            },
            {
              iconId: "recordvideo",
              part: "left",
              defaultActive: 0,
              memo: "录制按钮",
              isrender: 1
            },
            {
              iconId: "talk",
              part: "left",
              defaultActive: 0,
              memo: "对讲按钮",
              isrender: 1
            },
            {
              iconId: "zoom",
              part: "left",
              defaultActive: 1,
              memo: "电子放大",
              isrender: this.allowPreset ? 1 : 0
            },
            {
              iconId: "hd",
              part: "right",
              defaultActive: 0,
              memo: "清晰度切换按钮",
              isrender: 1
            },
            {
              iconId: "webExpend",
              part: "right",
              defaultActive: 0,
              memo: "网页全屏按钮",
              isrender: 0
            },
            {
              iconId: "expend",
              part: "right",
              defaultActive: 0,
              memo: "全局全屏按钮",
              isrender: 1
            }
          ]
        }
      };

      return params;
    },

    initVideo() {
      let that = this;

      const [w, h] = this.computedSize();

      const { accessToken } = this.videoCfg;

      that.player = new EZUIKit.EZUIKitPlayer({
        autoplay: false, //默认播放
        id: this.domId ? this.domId : `video-container_${this.videoId}`, // 视频容器ID
        accessToken: accessToken,
        url: this.playerUri,
        startTalk: () => that.player.startTalk(),
        stopTalk: () => that.player.stopTalk(),
        audio: 0, //是否开启声音  0 - 关闭 1 - 开启
        width: w,
        height: h,
        themeData: {
          ...this.getPlayerThemeData()
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.ezuikitPlayerWrap {
  width: 100%;
  height: 100%;
}

.videoWrap {
  height: calc(100% - 40px);
}

.presetWrap {
}
</style>

<style type="text/css">
video {
  object-fit: fill;
}
</style>
