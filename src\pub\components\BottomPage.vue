<template>
    <el-row>
        <el-col :span="24" class="bottom-rom" id="table-bottom">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="pagination.currentPage"
                    :page-size="pagination.pageSize"
                    :page-sizes="pagination.pageSizes"
                    :layout="pagination.layout"
                    :total="pagination.totalSize">
            </el-pagination>
        </el-col>
    </el-row>
</template>

<script>
    export default {

        props:{
            pagination:Object
        },
        methods: {
            handleCurrentChange:function(val){
                this.$emit('currentChange',val);
            },
            handleSizeChange:function(val){
                this.$emit('sizeChange',val);
            }
        }
    }
</script>
<style scoped>
</style>
