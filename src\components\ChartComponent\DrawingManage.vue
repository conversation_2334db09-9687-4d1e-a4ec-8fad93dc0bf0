<!--
 * @Description: 图纸管理
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-24 17:37:45
 * @LastEditors: dong<PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="DrawingManageRef"
      >
        <div
          id="DrawingManageChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import { drawCustomBar } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getDrawingInfo } from "@/api/echrtsApi";
export default {
  components: {},
  name: "DrawingManage",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      lineParams: {
        dom: "DrawingManageChart",
        xAxisData: [],
        seriesData: [],
        boundaryGap: true,
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        xAxisType: "value",
        yAxisType: "category",
        xminInterval: 1,
      },
      arr: [],
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.DrawingManageRef.offsetWidth;
      this.barHeight = this.$refs.DrawingManageRef.offsetHeight;
    },
    getData() {
      getDrawingInfo(this.projectId)
        .then((res) => {
          let result = res.data;
          const {
            data: {
              xData,
              yAxis: { data },
            },
            statusCode,
          } = result;
          if (statusCode == 200) {
            let xAxisData = data;
            let seriesData = [];
            xData.forEach((ele) => {
              seriesData.push({
                type: "bar",
                ...ele,
                label: {
                  normal: {
                    show: true,
                    position: "right",
                    textStyle: {
                      fontSize: 10,
                    },
                  },
                },
              });
            });
            this.lineParams.xAxisData = xAxisData.map((item) => {
              let target = "";
              switch (item) {
                case "其他":
                  target = this.$t("customization.others");
                  break;
                case "电气":
                  target = this.$t("customization.electrical");
                  break;
                case "暖通":
                  target = this.$t("customization.HVAC");
                  break;
                case "给排水":
                  target = this.$t("customization.outWater");
                  break;
                case "结构":
                  target = this.$t("customization.structure");
                  break;
                case "建筑":
                  target = this.$t("customization.building");
                  break;
              }
              return target;
            });
            this.lineParams.seriesData = seriesData.map((item) => {
              switch (item.name) {
                case "图纸":
                  item.name = this.$t("customization.drawing");
                  break;
                case "变更洽商":
                  item.name = this.$t("customization.changeTalk");
                  break;
              }
              return item;
            });
            console.log(seriesData, "666");
            drawCustomBar(this.lineParams);
          }
        })
        .catch(() => { });
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
