/*
 * @Description: 定制化接口
 * @Author:
 * @Date: 2022-12-23 15:41:48
 * @LastEditTime: 2023-02-20 14:04:51
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Usage:
 */
import Axios from "@/router/axios";

// 所有可配置列表
export function getAllModule(projectId) {
  return Axios({
    url: `/modul/all-module/${projectId}`,
    method: "get"
  });
}

// 获取个人可配置列表
export function userConfigure(userid, projectid) {
  return Axios({
    url: `/modul/user-configure/${userid}/${projectid}`,
    method: "get"
  });
}

// 保存配置信息
export function saveUserConfigure(data) {
  return Axios({
    url: `/modul/save-user-configure`,
    method: "post",
    data
  });
}
