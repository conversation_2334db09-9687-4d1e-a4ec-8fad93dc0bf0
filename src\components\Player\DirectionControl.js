export default class DirectionControl {
  constructor(props) {
    this.props = props || {};

    this.panelIsShow = true;
    this.panelWrapNode = null;
    this.scaleKeys = [8, 9];
    this.directionKeys = [0, 1, 2, 3];
  }

  onMousedownPTZ(val, i) {
    let cls = this.scaleKeys.indexOf(val) !== -1 ? "scaleBtn" : "sector";
    const dom = this.panelWrapNode.getElementsByClassName(cls)[i];
    dom.className = cls + " active";

    this.props.onMousedownPTZ(val);
  }

  onMouseUpPTZ(val, i) {
    let cls = this.scaleKeys.indexOf(val) !== -1 ? "scaleBtn" : "sector";
    const dom = this.panelWrapNode.getElementsByClassName(cls)[i];
    dom.className = cls;

    this.props.onMouseUpPTZ(val);
  }

  onClickAddPresetBtn() {
    this.props.onClickAddPresetBtn();
  }

  // 底部欄增加按鈕
  createToggleBtn(el) {
    const cont = el.getElementsByClassName("jky_directionControl")[0];
    if (cont) return;

    const footerBar = el.getElementsByClassName("prism-controlbar")[0];
    var wrap = document.createElement("div");
    wrap.className = "jky_directionControl";
    wrap.addEventListener("click", () => {
      this.onClick();
    });
    footerBar.appendChild(wrap);
  }

  createPanel(el) {
    this.panelWrapNode = document.createElement("div");
    this.panelWrapNode.className = "panelWrap";

    window.onMousedownVideoPTZ = (v, i) => {
      this.onMousedownPTZ(v, i);
    };
    window.onMouseUpVideoPTZ = (v, i) => {
      this.onMouseUpPTZ(v, i);
    };

    window.onClickAddPresetBtn = () => {
      this.onClickAddPresetBtn();
    };

    this.panelWrapNode.innerHTML = `
      <div class="panel">
        <div class="direction clearfix">
          <div
            class="sector"
            onmousedown="onMousedownVideoPTZ(0, 0)"
            onmouseup="onMouseUpVideoPTZ(0, 0)"
          >
            <!-- 向上 -->
            <div class="upA"></div>
          </div>
          <div
            class="sector"
            onmousedown="onMousedownVideoPTZ(3,1)"
            onmouseup="onMouseUpVideoPTZ(3,1)"
          >
            <!-- 向右 -->
            <div class="rightA"></div>
          </div>
          <div
            class="sector"
            onmousedown="onMousedownVideoPTZ(2,2)"
            onmouseup="onMouseUpVideoPTZ(2,2)"
          >
            <!-- 向左 -->
            <div class="leftA"></div>
          </div>
          <div
            class="sector"
            onmousedown="onMousedownVideoPTZ(1,3)"
            onmouseup="onMouseUpVideoPTZ(1,3)"
          >
            <!-- 向下 -->
            <div class="downA"></div>
          </div>
        </div>
        <div class="scale">
          <!-- 缩小 -->
          <div
            class="scaleBtn"
            onmousedown="onMousedownVideoPTZ(9,0)"
            onmouseup="onMouseUpVideoPTZ(9,0)"
          >
            -
          </div>
          <!-- 放大 -->
          <div
            class="scaleBtn"
            onmousedown="onMousedownVideoPTZ(8,1)"
            onmouseup="onMouseUpVideoPTZ(8,1)"
          >
            +
          </div>
        </div>
      </div>
      <div
        class="presetBtn"
        onclick="onClickAddPresetBtn()"
      >
        设置预设点
      </div>
    `;

    this.panelWrapNode.style.display = this.panelIsShow ? "block" : "none";
    el.appendChild(this.panelWrapNode);
  }

  createEl(el) {
    this.createToggleBtn(el);
    this.createPanel(el);
  }

  onClick(val) {
    this.panelIsShow = !this.panelIsShow;
    this.panelWrapNode.style.display = this.panelIsShow ? "block" : "none";
  }

  hidePanel() {
    this.onClick(0);
  }

  ended() {
    // this.hidePanel();
  }

  error() {
    this.hidePanel();
  }

  dispose() {
    // this.hidePanel();
  }
}
