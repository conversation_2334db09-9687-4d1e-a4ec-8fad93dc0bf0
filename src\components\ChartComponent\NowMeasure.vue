<!--
 * @Description: 智能测量
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-25 10:05:02
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="NowMeasureRef"
      >
        <div
          id="NowMeasureChart"
          :style="{ height: barHeight + 'px', width: '100%' }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import { drawHorizontalBarChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getMeasure } from "@/api/echrtsApi";

export default {
  name: "NowMeasure",
  props: {
    moduleName: String,
  },
  watch: {
    "$i18n.locale"(val) {
      if (val && this.$IsProjectShow) {
        this.language = val;
        this.languageChange();
      }
    },
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      barParams: {
        dom: "NowMeasureChart",
        xAxisData: [],
        seriesData: [],
        boundaryGap: true,
        isMoreLine: true,
        legendIcon: "rect",
        legendCenter: "left",
        axisPointerType: "shadow",
        unit: "%",
        fontSize: this.getDynamicFontSize(), // 动态字体大小
        labelFontSize: this.getDynamicFontSize(), // 动态标签大小
        barWidth: this.getDynamicBarWidth(), // 动态柱宽
        grid: {
          borderWidth: 0,
          top: "14%",
          left: "2%",
          right: "21%",
          bottom: "3%",
        },
        axisLabelFormatter: (value) => {
          const isSmallScreen = window.innerWidth < 768;
          let res = value;
          if (isSmallScreen && res.length > 3) {
            res = res.substring(0, 3) + "..";
          } else if (res.length > 6) {
            res = res.substring(0, 6) + "..";
          }
          return res;
        },
        tooltipFormatter: (val) => {
          let msg = `${val[0].name}<br/>${val[0].marker}${this.$t("averageRate")}
            <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
            <b>${val[0].data}</b>%`;
          return msg;
        },
      },
      arr: [],
      language: "zh",
    };
  },
  created() {
    this.projectId = getStore({ name: "projectId" });
    this.companyId = getStore({ name: "companyId" });
    this.language = getStore({ name: "language" });
    if (this.$IsProjectShow) {
      this.languageChange();
    } else {
      this.getBarData();
    }
  },
  mounted() {
    this.setEchartsWidth();
    this.handleResize();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    handleResize() {
      let tid = null;
      const resizeHandler = () => {
        clearTimeout(tid);
        tid = setTimeout(() => {
          this.setEchartsWidth();
          // 更新动态参数
          this.barParams.fontSize = this.getDynamicFontSize();
          this.barParams.labelFontSize = this.getDynamicFontSize();
          this.barParams.barWidth = this.getDynamicBarWidth();
          // 重新渲染图表
          if (this.barParams.xAxisData.length > 0) {
            drawHorizontalBarChart(this.barParams);
          }
        }, 300);
      };
      window.addEventListener('resize', resizeHandler);
      this.$once('hook:beforeDestroy', () => {
        window.removeEventListener('resize', resizeHandler);
      });
    },
    languageChange() {
      this.barParams.tooltipFormatter = (val) => {
        let msg = `${val[0].name}<br/>${val[0].marker}${this.language === "en" ? "Average pass rate" : "平均合格率"
          }
          <span style="display:inline-block;margin-right:0px;border-radius:10px;width:10px;height:10px;"></span>
          <b>${val[0].data}</b>%`;
        return msg;
      };
      this.getBarData();
    },
    setEchartsWidth() {
      const container = this.$refs.NowMeasureRef;
      if (!container) return;

      this.barWidth = container.offsetWidth;
      // 根据屏幕宽度动态调整高度
      const isSmallScreen = window.innerWidth < 768;
      this.barHeight = isSmallScreen
        ? Math.max(container.offsetHeight * 1.5, 250)  // 小屏增加高度，最小250px
        : container.offsetHeight;
    },
    getDynamicFontSize() {
      // 根据屏幕宽度动态返回字体大小
      const width = window.innerWidth;
      if (width < 480) return 10;
      if (width < 768) return 12;
      return 14;
    },
    getDynamicBarWidth() {
      // 根据屏幕宽度动态返回柱宽
      const width = window.innerWidth;
      if (width < 480) return "8px";
      if (width < 768) return "10px";
      return "12px";
    },
    getBarData() {
      getMeasure(this.projectId)
        .then((res) => {
          const { data, statusCode } = res.data;
          if (statusCode === 200) {
            let seriesData = [
              {
                data: data.avgPassRate,
                type: "bar",
                itemStyle: {
                  normal: {
                    color: "#4e81e4",
                  },
                },
              },
            ];

            let arr = [];
            data.equipName.forEach((ele) => {
              if (this.$IsProjectShow) {
                if (ele.includes("水平仪")) {
                  ele = this.$t(`qualifiedlevel`);
                } else if (ele == "靠尺平均合格率") {
                  ele = this.$t(`guidingruler`);
                } else if (ele == "测距仪平均合格率") {
                  ele = this.$t(`rangefinder`);
                } else if (ele == "角尺平均合格率") {
                  ele = this.$t(`anglerule`);
                }
              }
              arr.push(ele);
            });

            this.barParams.xAxisData = arr;
            this.barParams.seriesData = seriesData;
            drawHorizontalBarChart(this.barParams);
          }
        })
        .catch(() => { });
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .box {
    height: 300px; /* 小屏时增加高度 */
  }

  #NowMeasureChart {
    margin-top: 15px; /* 增加上边距 */
  }
}

@media screen and (max-width: 480px) {
  .box {
    height: 280px;
  }
}
</style>