import Axios from '@/router/axios'
// 保存参数设置
export function saveConfig(query) {
  return Axios({
    url: "/api/environment-detection-module/save-config",
    method: 'post',
    data: query
  })
}
// 获取参数
export function getCOnfig(query) {
  return Axios({
    url: `/api/environment-detection-module/config-info/${query}`,
    method: 'get'
  })
}
// 枚举配置参数
export const enumConfig = {
  pM25: 'PM2.5趋势',
  pM10: 'PM10趋势',
  noise: '噪音趋势',
  temperature: '气温趋势',
  windSpeed: '风速趋势',
  humidity: '湿度',
  tsp: 'TSP',
  pressure: '大气压'
}
// 枚举配置参数
export const enumConfig2 = {
  pM25: 'PM2.5',
  pM10: 'PM10',
  noise: '噪声',
  temperature: '气温',
  windSpeed: '风速',
  humidity: '湿度',
  tsp: 'TSP',
  pressure: '大气压'
}