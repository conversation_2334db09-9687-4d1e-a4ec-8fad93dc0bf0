<!--
 * @Description: 爱国卫生
 * @Author:
 * @Date: 2022-07-25 18:45:32
 * @LastEditTime: 2025-07-25 15:10:55
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>an
 * @Usage:
-->
<template>
  <div
    class="area"
    style="cursor: pointer"
  >
    <div class="text">{{ moduleName }}</div>
    <div class="areaContent">
      <div
        class="box"
        ref="PatrioticHealthRef"
      >
        <div
          id="PatrioticHealthChart"
          :style="{ height: barHeight + 'px', width: barWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>

import { drawAnnularChart } from "@/components/constructionRecord/Echarts/echartsTwo.js";
import { getPatrioticHealth } from "@/api/echrtsApi";
export default {
  components: {},
  name: "PatrioticHealth",
  props: {
    moduleName: String,
  },
  data() {
    return {
      projectId: "",
      companyId: "",
      barWidth: null,
      barHeight: null,
      pieParams: {
        dom: "PatrioticHealthChart",
        data: [],
        nameTitle: null,
        seriesLabel: false,
        subtext: this.$t("customization.patrioticHealthTotal"),
        seriesCenter: ["25%", "58%"],
        richNameWidth: 60,
        legendTop: '10px',
        noTooltipShow: true, //不显示
        itemStyleEmphasis: {
          label: {
            show: true,
            // position: 'center',
            x: "20%",
            y: "10%",
            textStyle: {
              rich: {
                numText: {
                  color: "#fff",
                  fontSize: 13,
                  width: 30,
                  textAlign: "center",
                },
                text: {
                  color: "#fff",
                  fontSize: 13,
                  padding: [0, 0, 10, 0],
                  width: 30,
                  textAlign: "center",
                },
              },
            },
            formatter: (params) => {
              return `{text| ${params.name}：${params.value
                }}\n{numText|${this.$t("Proportion")}： ${params.percent || 0}%}`;
            },
          },
        },
        costomLegendFormatter:function(name){
          return name;
        },
      },
      totalCount: 0,
    };
  },
  created() {
    this.projectId = getStore({
      name: "projectId",
    });
    this.companyId = getStore({
      name: "companyId",
    });
    this.getBarData();
  },
  mounted() {
    this.setEchartsWidth();
    let that = this;
    let tid = null;
    window.addEventListener("resize", function () {
      clearTimeout(tid);
      tid = setTimeout(that.setEchartsWidth(), 300);
    });
  },
  methods: {
    setEchartsWidth() {
      this.barWidth = this.$refs.PatrioticHealthRef.offsetWidth;
      this.barHeight = this.$refs.PatrioticHealthRef.offsetHeight;
    },
    getBarData() {
      getPatrioticHealth()
        .then((res) => {
          const { status, data } = res;
          if (status == 200) {
            this.setEcharts(data);
          }
        })
        .catch(() => { });
    },
    setEcharts(val) {
      let dataList = val;
      // this.pieParams.titleInfor.text = totalCount;
      if (dataList.length > 0) {
        let legendFormatter = (name) => {
          const item = dataList.find((i) => {
            return i.name === name;
          });
          const p = item.value;
          let newName = name.length > 9 ? name.slice(0, 9) + "..." : name;
          return "{name|" + newName + "}" + "{percent|" + p + "}";
        };
        this.pieParams.legendFormatter = legendFormatter;
      }
      let sum = 0;
      dataList.forEach((ele) => {
        sum = sum + Number(ele.value);
      });

      this.totalCount = sum;
      this.pieParams.data = dataList.map((item) => {
        switch (item.name) {
          case "清洁行动":
            item.name = this.$t("customization.cleanAct");
            break;
          case "环境提升行动":
            item.name = this.$t("customization.envirProAct");
            break;
          case "宣传行动":
            item.name = this.$t("customization.propagateAcT");
            break;
          case "除“四害”行动":
            item.name = this.$t("customization.clearFour");
            break;
        }
        return item;
      });

      drawAnnularChart(this.pieParams);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
